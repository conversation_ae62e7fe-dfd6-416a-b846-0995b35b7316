"use strict";

function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it["return"] != null) it["return"](); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
/*!
 * Signature Pad v4.1.6 | https://github.com/szimek/signature_pad
 * (c) 2023 Szymon Nowak | Released under the MIT license
 */
var Point = /*#__PURE__*/function () {
  function Point(x, y, pressure, time) {
    _classCallCheck(this, Point);
    if (isNaN(x) || isNaN(y)) {
      throw new Error("Point is invalid: (".concat(x, ", ").concat(y, ")"));
    }
    this.x = +x;
    this.y = +y;
    this.pressure = pressure || 0;
    this.time = time || Date.now();
  }
  _createClass(Point, [{
    key: "distanceTo",
    value: function distanceTo(start) {
      return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));
    }
  }, {
    key: "equals",
    value: function equals(other) {
      return this.x === other.x && this.y === other.y && this.pressure === other.pressure && this.time === other.time;
    }
  }, {
    key: "velocityFrom",
    value: function velocityFrom(start) {
      return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 0;
    }
  }]);
  return Point;
}();
var Bezier = /*#__PURE__*/function () {
  function Bezier(startPoint, control2, control1, endPoint, startWidth, endWidth) {
    _classCallCheck(this, Bezier);
    this.startPoint = startPoint;
    this.control2 = control2;
    this.control1 = control1;
    this.endPoint = endPoint;
    this.startWidth = startWidth;
    this.endWidth = endWidth;
  }
  _createClass(Bezier, [{
    key: "length",
    value: function length() {
      var steps = 10;
      var length = 0;
      var px;
      var py;
      for (var i = 0; i <= steps; i += 1) {
        var t = i / steps;
        var cx = this.point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);
        var cy = this.point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);
        if (i > 0) {
          var xdiff = cx - px;
          var ydiff = cy - py;
          length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);
        }
        px = cx;
        py = cy;
      }
      return length;
    }
  }, {
    key: "point",
    value: function point(t, start, c1, c2, end) {
      return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;
    }
  }], [{
    key: "fromPoints",
    value: function fromPoints(points, widths) {
      var c2 = this.calculateControlPoints(points[0], points[1], points[2]).c2;
      var c3 = this.calculateControlPoints(points[1], points[2], points[3]).c1;
      return new Bezier(points[1], c2, c3, points[2], widths.start, widths.end);
    }
  }, {
    key: "calculateControlPoints",
    value: function calculateControlPoints(s1, s2, s3) {
      var dx1 = s1.x - s2.x;
      var dy1 = s1.y - s2.y;
      var dx2 = s2.x - s3.x;
      var dy2 = s2.y - s3.y;
      var m1 = {
        x: (s1.x + s2.x) / 2.0,
        y: (s1.y + s2.y) / 2.0
      };
      var m2 = {
        x: (s2.x + s3.x) / 2.0,
        y: (s2.y + s3.y) / 2.0
      };
      var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
      var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
      var dxm = m1.x - m2.x;
      var dym = m1.y - m2.y;
      var k = l2 / (l1 + l2);
      var cm = {
        x: m2.x + dxm * k,
        y: m2.y + dym * k
      };
      var tx = s2.x - cm.x;
      var ty = s2.y - cm.y;
      return {
        c1: new Point(m1.x + tx, m1.y + ty),
        c2: new Point(m2.x + tx, m2.y + ty)
      };
    }
  }]);
  return Bezier;
}();
var SignatureEventTarget = /*#__PURE__*/function () {
  function SignatureEventTarget() {
    _classCallCheck(this, SignatureEventTarget);
    try {
      this._et = new EventTarget();
    } catch (error) {
      this._et = document;
    }
  }
  _createClass(SignatureEventTarget, [{
    key: "addEventListener",
    value: function addEventListener(type, listener, options) {
      this._et.addEventListener(type, listener, options);
    }
  }, {
    key: "dispatchEvent",
    value: function dispatchEvent(event) {
      return this._et.dispatchEvent(event);
    }
  }, {
    key: "removeEventListener",
    value: function removeEventListener(type, callback, options) {
      this._et.removeEventListener(type, callback, options);
    }
  }]);
  return SignatureEventTarget;
}();
function throttle(fn) {
  var wait = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 250;
  var previous = 0;
  var timeout = null;
  var result;
  var storedContext;
  var storedArgs;
  var later = function later() {
    previous = Date.now();
    timeout = null;
    result = fn.apply(storedContext, storedArgs);
    if (!timeout) {
      storedContext = null;
      storedArgs = [];
    }
  };
  return function wrapper() {
    var now = Date.now();
    var remaining = wait - (now - previous);
    storedContext = this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    storedArgs = args;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      result = fn.apply(storedContext, storedArgs);
      if (!timeout) {
        storedContext = null;
        storedArgs = [];
      }
    } else if (!timeout) {
      timeout = window.setTimeout(later, remaining);
    }
    return result;
  };
}
var SignaturePad = /*#__PURE__*/function (_SignatureEventTarget) {
  _inherits(SignaturePad, _SignatureEventTarget);
  var _super = _createSuper(SignaturePad);
  function SignaturePad(canvas) {
    var _this;
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    _classCallCheck(this, SignaturePad);
    _this = _super.call(this);
    _this.canvas = canvas;
    _this._drawningStroke = false;
    _this._isEmpty = true;
    _this._lastPoints = [];
    _this._data = [];
    _this._lastVelocity = 0;
    _this._lastWidth = 0;
    _this._handleMouseDown = function (event) {
      if (event.buttons === 1) {
        _this._drawningStroke = true;
        _this._strokeBegin(event);
      }
    };
    _this._handleMouseMove = function (event) {
      if (_this._drawningStroke) {
        _this._strokeMoveUpdate(event);
      }
    };
    _this._handleMouseUp = function (event) {
      if (event.buttons === 1 && _this._drawningStroke) {
        _this._drawningStroke = false;
        _this._strokeEnd(event);
      }
    };
    _this._handleTouchStart = function (event) {
      if (event.cancelable) {
        event.preventDefault();
      }
      if (event.targetTouches.length === 1) {
        var touch = event.changedTouches[0];
        _this._strokeBegin(touch);
      }
    };
    _this._handleTouchMove = function (event) {
      if (event.cancelable) {
        event.preventDefault();
      }
      var touch = event.targetTouches[0];
      _this._strokeMoveUpdate(touch);
    };
    _this._handleTouchEnd = function (event) {
      var wasCanvasTouched = event.target === _this.canvas;
      if (wasCanvasTouched) {
        if (event.cancelable) {
          event.preventDefault();
        }
        var touch = event.changedTouches[0];
        _this._strokeEnd(touch);
      }
    };
    _this._handlePointerStart = function (event) {
      _this._drawningStroke = true;
      event.preventDefault();
      _this._strokeBegin(event);
    };
    _this._handlePointerMove = function (event) {
      if (_this._drawningStroke) {
        event.preventDefault();
        _this._strokeMoveUpdate(event);
      }
    };
    _this._handlePointerEnd = function (event) {
      if (_this._drawningStroke) {
        event.preventDefault();
        _this._drawningStroke = false;
        _this._strokeEnd(event);
      }
    };
    _this.velocityFilterWeight = options.velocityFilterWeight || 0.7;
    _this.minWidth = options.minWidth || 0.5;
    _this.maxWidth = options.maxWidth || 2.5;
    _this.throttle = 'throttle' in options ? options.throttle : 16;
    _this.minDistance = 'minDistance' in options ? options.minDistance : 5;
    _this.dotSize = options.dotSize || 0;
    _this.penColor = options.penColor || 'black';
    _this.backgroundColor = options.backgroundColor || 'rgba(0,0,0,0)';
    _this.compositeOperation = options.compositeOperation || 'source-over';
    _this._strokeMoveUpdate = _this.throttle ? throttle(SignaturePad.prototype._strokeUpdate, _this.throttle) : SignaturePad.prototype._strokeUpdate;
    _this._ctx = canvas.getContext('2d');
    _this.clear();
    _this.on();
    return _this;
  }
  _createClass(SignaturePad, [{
    key: "clear",
    value: function clear() {
      var ctx = this._ctx,
        canvas = this.canvas;
      ctx.fillStyle = this.backgroundColor;
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      this._data = [];
      this._reset(this._getPointGroupOptions());
      this._isEmpty = true;
    }
  }, {
    key: "fromDataURL",
    value: function fromDataURL(dataUrl) {
      var _this2 = this;
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      return new Promise(function (resolve, reject) {
        var image = new Image();
        var ratio = options.ratio || window.devicePixelRatio || 1;
        var width = options.width || _this2.canvas.width / ratio;
        var height = options.height || _this2.canvas.height / ratio;
        var xOffset = options.xOffset || 0;
        var yOffset = options.yOffset || 0;
        _this2._reset(_this2._getPointGroupOptions());
        image.onload = function () {
          _this2._ctx.drawImage(image, xOffset, yOffset, width, height);
          resolve();
        };
        image.onerror = function (error) {
          reject(error);
        };
        image.crossOrigin = 'anonymous';
        image.src = dataUrl;
        _this2._isEmpty = false;
      });
    }
  }, {
    key: "toDataURL",
    value: function toDataURL() {
      var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'image/png';
      var encoderOptions = arguments.length > 1 ? arguments[1] : undefined;
      switch (type) {
        case 'image/svg+xml':
          if (_typeof(encoderOptions) !== 'object') {
            encoderOptions = undefined;
          }
          return "data:image/svg+xml;base64,".concat(btoa(this.toSVG(encoderOptions)));
        default:
          if (typeof encoderOptions !== 'number') {
            encoderOptions = undefined;
          }
          return this.canvas.toDataURL(type, encoderOptions);
      }
    }
  }, {
    key: "on",
    value: function on() {
      this.canvas.style.touchAction = 'none';
      this.canvas.style.msTouchAction = 'none';
      this.canvas.style.userSelect = 'none';
      var isIOS = /Macintosh/.test(navigator.userAgent) && 'ontouchstart' in document;
      if (window.PointerEvent && !isIOS) {
        this._handlePointerEvents();
      } else {
        this._handleMouseEvents();
        if ('ontouchstart' in window) {
          this._handleTouchEvents();
        }
      }
    }
  }, {
    key: "off",
    value: function off() {
      this.canvas.style.touchAction = 'auto';
      this.canvas.style.msTouchAction = 'auto';
      this.canvas.style.userSelect = 'auto';
      this.canvas.removeEventListener('pointerdown', this._handlePointerStart);
      this.canvas.removeEventListener('pointermove', this._handlePointerMove);
      this.canvas.ownerDocument.removeEventListener('pointerup', this._handlePointerEnd);
      this.canvas.removeEventListener('mousedown', this._handleMouseDown);
      this.canvas.removeEventListener('mousemove', this._handleMouseMove);
      this.canvas.ownerDocument.removeEventListener('mouseup', this._handleMouseUp);
      this.canvas.removeEventListener('touchstart', this._handleTouchStart);
      this.canvas.removeEventListener('touchmove', this._handleTouchMove);
      this.canvas.removeEventListener('touchend', this._handleTouchEnd);
    }
  }, {
    key: "isEmpty",
    value: function isEmpty() {
      return this._isEmpty;
    }
  }, {
    key: "fromData",
    value: function fromData(pointGroups) {
      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
        _ref$clear = _ref.clear,
        clear = _ref$clear === void 0 ? true : _ref$clear;
      if (clear) {
        this.clear();
      }
      this._fromData(pointGroups, this._drawCurve.bind(this), this._drawDot.bind(this));
      this._data = this._data.concat(pointGroups);
    }
  }, {
    key: "toData",
    value: function toData() {
      return this._data;
    }
  }, {
    key: "_getPointGroupOptions",
    value: function _getPointGroupOptions(group) {
      return {
        penColor: group && 'penColor' in group ? group.penColor : this.penColor,
        dotSize: group && 'dotSize' in group ? group.dotSize : this.dotSize,
        minWidth: group && 'minWidth' in group ? group.minWidth : this.minWidth,
        maxWidth: group && 'maxWidth' in group ? group.maxWidth : this.maxWidth,
        velocityFilterWeight: group && 'velocityFilterWeight' in group ? group.velocityFilterWeight : this.velocityFilterWeight,
        compositeOperation: group && 'compositeOperation' in group ? group.compositeOperation : this.compositeOperation
      };
    }
  }, {
    key: "_strokeBegin",
    value: function _strokeBegin(event) {
      this.dispatchEvent(new CustomEvent('beginStroke', {
        detail: event
      }));
      var pointGroupOptions = this._getPointGroupOptions();
      var newPointGroup = Object.assign(Object.assign({}, pointGroupOptions), {
        points: []
      });
      this._data.push(newPointGroup);
      this._reset(pointGroupOptions);
      this._strokeUpdate(event);
    }
  }, {
    key: "_strokeUpdate",
    value: function _strokeUpdate(event) {
      if (this._data.length === 0) {
        this._strokeBegin(event);
        return;
      }
      this.dispatchEvent(new CustomEvent('beforeUpdateStroke', {
        detail: event
      }));
      var x = event.clientX;
      var y = event.clientY;
      var pressure = event.pressure !== undefined ? event.pressure : event.force !== undefined ? event.force : 0;
      var point = this._createPoint(x, y, pressure);
      var lastPointGroup = this._data[this._data.length - 1];
      var lastPoints = lastPointGroup.points;
      var lastPoint = lastPoints.length > 0 && lastPoints[lastPoints.length - 1];
      var isLastPointTooClose = lastPoint ? point.distanceTo(lastPoint) <= this.minDistance : false;
      var pointGroupOptions = this._getPointGroupOptions(lastPointGroup);
      if (!lastPoint || !(lastPoint && isLastPointTooClose)) {
        var curve = this._addPoint(point, pointGroupOptions);
        if (!lastPoint) {
          this._drawDot(point, pointGroupOptions);
        } else if (curve) {
          this._drawCurve(curve, pointGroupOptions);
        }
        lastPoints.push({
          time: point.time,
          x: point.x,
          y: point.y,
          pressure: point.pressure
        });
      }
      this.dispatchEvent(new CustomEvent('afterUpdateStroke', {
        detail: event
      }));
    }
  }, {
    key: "_strokeEnd",
    value: function _strokeEnd(event) {
      this._strokeUpdate(event);
      this.dispatchEvent(new CustomEvent('endStroke', {
        detail: event
      }));
    }
  }, {
    key: "_handlePointerEvents",
    value: function _handlePointerEvents() {
      this._drawningStroke = false;
      this.canvas.addEventListener('pointerdown', this._handlePointerStart);
      this.canvas.addEventListener('pointermove', this._handlePointerMove);
      this.canvas.ownerDocument.addEventListener('pointerup', this._handlePointerEnd);
    }
  }, {
    key: "_handleMouseEvents",
    value: function _handleMouseEvents() {
      this._drawningStroke = false;
      this.canvas.addEventListener('mousedown', this._handleMouseDown);
      this.canvas.addEventListener('mousemove', this._handleMouseMove);
      this.canvas.ownerDocument.addEventListener('mouseup', this._handleMouseUp);
    }
  }, {
    key: "_handleTouchEvents",
    value: function _handleTouchEvents() {
      this.canvas.addEventListener('touchstart', this._handleTouchStart);
      this.canvas.addEventListener('touchmove', this._handleTouchMove);
      this.canvas.addEventListener('touchend', this._handleTouchEnd);
    }
  }, {
    key: "_reset",
    value: function _reset(options) {
      this._lastPoints = [];
      this._lastVelocity = 0;
      this._lastWidth = (options.minWidth + options.maxWidth) / 2;
      this._ctx.fillStyle = options.penColor;
      this._ctx.globalCompositeOperation = options.compositeOperation;
    }
  }, {
    key: "_createPoint",
    value: function _createPoint(x, y, pressure) {
      var rect = this.canvas.getBoundingClientRect();
      return new Point(x - rect.left, y - rect.top, pressure, new Date().getTime());
    }
  }, {
    key: "_addPoint",
    value: function _addPoint(point, options) {
      var _lastPoints = this._lastPoints;
      _lastPoints.push(point);
      if (_lastPoints.length > 2) {
        if (_lastPoints.length === 3) {
          _lastPoints.unshift(_lastPoints[0]);
        }
        var widths = this._calculateCurveWidths(_lastPoints[1], _lastPoints[2], options);
        var curve = Bezier.fromPoints(_lastPoints, widths);
        _lastPoints.shift();
        return curve;
      }
      return null;
    }
  }, {
    key: "_calculateCurveWidths",
    value: function _calculateCurveWidths(startPoint, endPoint, options) {
      var velocity = options.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - options.velocityFilterWeight) * this._lastVelocity;
      var newWidth = this._strokeWidth(velocity, options);
      var widths = {
        end: newWidth,
        start: this._lastWidth
      };
      this._lastVelocity = velocity;
      this._lastWidth = newWidth;
      return widths;
    }
  }, {
    key: "_strokeWidth",
    value: function _strokeWidth(velocity, options) {
      return Math.max(options.maxWidth / (velocity + 1), options.minWidth);
    }
  }, {
    key: "_drawCurveSegment",
    value: function _drawCurveSegment(x, y, width) {
      var ctx = this._ctx;
      ctx.moveTo(x, y);
      ctx.arc(x, y, width, 0, 2 * Math.PI, false);
      this._isEmpty = false;
    }
  }, {
    key: "_drawCurve",
    value: function _drawCurve(curve, options) {
      var ctx = this._ctx;
      var widthDelta = curve.endWidth - curve.startWidth;
      var drawSteps = Math.ceil(curve.length()) * 2;
      ctx.beginPath();
      ctx.fillStyle = options.penColor;
      for (var i = 0; i < drawSteps; i += 1) {
        var t = i / drawSteps;
        var tt = t * t;
        var ttt = tt * t;
        var u = 1 - t;
        var uu = u * u;
        var uuu = uu * u;
        var x = uuu * curve.startPoint.x;
        x += 3 * uu * t * curve.control1.x;
        x += 3 * u * tt * curve.control2.x;
        x += ttt * curve.endPoint.x;
        var y = uuu * curve.startPoint.y;
        y += 3 * uu * t * curve.control1.y;
        y += 3 * u * tt * curve.control2.y;
        y += ttt * curve.endPoint.y;
        var width = Math.min(curve.startWidth + ttt * widthDelta, options.maxWidth);
        this._drawCurveSegment(x, y, width);
      }
      ctx.closePath();
      ctx.fill();
    }
  }, {
    key: "_drawDot",
    value: function _drawDot(point, options) {
      var ctx = this._ctx;
      var width = options.dotSize > 0 ? options.dotSize : (options.minWidth + options.maxWidth) / 2;
      ctx.beginPath();
      this._drawCurveSegment(point.x, point.y, width);
      ctx.closePath();
      ctx.fillStyle = options.penColor;
      ctx.fill();
    }
  }, {
    key: "_fromData",
    value: function _fromData(pointGroups, drawCurve, drawDot) {
      var _iterator = _createForOfIteratorHelper(pointGroups),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var group = _step.value;
          var points = group.points;
          var pointGroupOptions = this._getPointGroupOptions(group);
          if (points.length > 1) {
            for (var j = 0; j < points.length; j += 1) {
              var basicPoint = points[j];
              var point = new Point(basicPoint.x, basicPoint.y, basicPoint.pressure, basicPoint.time);
              if (j === 0) {
                this._reset(pointGroupOptions);
              }
              var curve = this._addPoint(point, pointGroupOptions);
              if (curve) {
                drawCurve(curve, pointGroupOptions);
              }
            }
          } else {
            this._reset(pointGroupOptions);
            drawDot(points[0], pointGroupOptions);
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  }, {
    key: "toSVG",
    value: function toSVG() {
      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        _ref2$includeBackgrou = _ref2.includeBackgroundColor,
        includeBackgroundColor = _ref2$includeBackgrou === void 0 ? false : _ref2$includeBackgrou;
      var pointGroups = this._data;
      var ratio = Math.max(window.devicePixelRatio || 1, 1);
      var minX = 0;
      var minY = 0;
      var maxX = this.canvas.width / ratio;
      var maxY = this.canvas.height / ratio;
      var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      svg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
      svg.setAttribute('viewBox', "".concat(minX, " ").concat(minY, " ").concat(maxX, " ").concat(maxY));
      svg.setAttribute('width', maxX.toString());
      svg.setAttribute('height', maxY.toString());
      if (includeBackgroundColor && this.backgroundColor) {
        var rect = document.createElement('rect');
        rect.setAttribute('width', '100%');
        rect.setAttribute('height', '100%');
        rect.setAttribute('fill', this.backgroundColor);
        svg.appendChild(rect);
      }
      this._fromData(pointGroups, function (curve, _ref3) {
        var penColor = _ref3.penColor;
        var path = document.createElement('path');
        if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {
          var attr = "M ".concat(curve.startPoint.x.toFixed(3), ",").concat(curve.startPoint.y.toFixed(3), " ") + "C ".concat(curve.control1.x.toFixed(3), ",").concat(curve.control1.y.toFixed(3), " ") + "".concat(curve.control2.x.toFixed(3), ",").concat(curve.control2.y.toFixed(3), " ") + "".concat(curve.endPoint.x.toFixed(3), ",").concat(curve.endPoint.y.toFixed(3));
          path.setAttribute('d', attr);
          path.setAttribute('stroke-width', (curve.endWidth * 2.25).toFixed(3));
          path.setAttribute('stroke', penColor);
          path.setAttribute('fill', 'none');
          path.setAttribute('stroke-linecap', 'round');
          svg.appendChild(path);
        }
      }, function (point, _ref4) {
        var penColor = _ref4.penColor,
          dotSize = _ref4.dotSize,
          minWidth = _ref4.minWidth,
          maxWidth = _ref4.maxWidth;
        var circle = document.createElement('circle');
        var size = dotSize > 0 ? dotSize : (minWidth + maxWidth) / 2;
        circle.setAttribute('r', size.toString());
        circle.setAttribute('cx', point.x.toString());
        circle.setAttribute('cy', point.y.toString());
        circle.setAttribute('fill', penColor);
        svg.appendChild(circle);
      });
      return svg.outerHTML;
    }
  }]);
  return SignaturePad;
}(SignatureEventTarget);

angular.module('signaturePad', []).factory('signaturePadService', function () {
    return SignaturePad;
});
