.table-borderless>thead>tr>th,
.table-borderless>thead>tr>td,
.table-borderless>tbody>tr>th,
.table-borderless>thead,
.table-borderless>tbody>tr>td,
.table-borderless>tfoot>tr>th,
.table-borderless>tfoot>tr>td {
    border: 0px !important;
}

 .camp-panel {
    margin: 20px;
    /* width: 50%; */
} 

.camp-panel-body {
    padding-left: 15px;
    padding-top: 15px;
    padding-bottom: 5px;
    padding-right: 15px;
}

.camp-panel-heading h3 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: normal;
    width: 75%;
    padding-top: 8px;
}

.camp-sort-order-arrow {
    padding-top: 10px;
}

.camp-name {
    color: #3399F3;
    text-decoration: underline;
}

.camp-text-warning {
    color:#999
}

.no-clear-btn button.btn-danger {
    display: none;
}

.add-camp {
    display: flex;
    align-items: center;

    &-warning {
        margin-left: 5px;
        font-weight: bold;
    }
    &-text_danger {
        color: #A94442;
    }
}

.deactivate-ticket_save-btn {
    &_wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    &_input {
        width: 80%;
    }
}

.deactivate-button {
    &_wrapper {
        padding-top: 15px;
    }
}

.scanned-unique-tickets-table {
    border-collapse: collapse;

    th, td {
        text-align: center;
        padding: 5px 10px;
        border: 1px solid black;
    }

    table-layout: fixed;
    width: 100%;
}
