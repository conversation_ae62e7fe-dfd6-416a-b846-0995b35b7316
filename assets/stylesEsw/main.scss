$grid-float-breakpoint:     1000px !default;

$icon-font-path: "../styles/fonts/";
$fa-font-path: "../styles/fonts";

@import "bootstrap-sass-official/assets/stylesheets/bootstrap.scss";
@import "font-awesome/scss/font-awesome";


@import "compass-mixins/lib/compass/functions";
@import "compass-mixins/lib/compass/css3/box-shadow";
@import "compass-mixins/lib/compass/css3/images";

html {
  min-height: 100%;
  position: relative;
}

body {
  margin-bottom: 60px;
  padding-bottom: 10px; 
}

@mixin animation($time, $name) {
  -webkit-animation: $time $name;
  -moz-animation: $time $name;
  -o-animation: $time $name;
  animation: $time $name;
}

@media (min-width: 768px) {
  footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #f5f5f5;
    .copyright {
      padding: 15px 0;
    }
  }
}

@media (max-width: 768px) {
  footer {
    display: none;
  }
}

@media (max-width: 768px) {
  .pool-details-tabs {
    display: block;
  }
  .pool-details-tabs.wide {
    display: none;
  }

  .pool-details-wide {
    display: none;
  }
  .pool-details-wide.tabs {
    display: block;
  }

  .pool-wide-link {
    display: block;
    margin: 10px 0;
  }
}

@media (min-width: 768px) {
  .pool-details-tabs {
    display: none;
  }
  .pool-details-wide {
    display: block;
  }

  .pool-wide-link {
    display: none;
  }
}

.row-space {
    margin-top: 10px;
}


.navbar {
   @include background(linear-gradient(to bottom, #FFFFFF, #EEEEEE 50%, #E4E4E4));
   border: 1px solid #E4E4E4;
   font-size: 14px;

   .nav > li > a {
      text-shadow: 0 1px 0 rgba(255, 255, 255, 0.3);
   }
}

.navbar-default {
   background-color: #EEEEEE;
   border-color: #E4E4E4;
}

.center-block {
   @include center-block;
   float: none;
}

.nav, .pagination, .carousel, .panel-title a { 
  cursor: pointer; 
}

.notification-holder {
  position: fixed;
  z-index: 9999;
  top: 20px;
  right: 20px;
  width: 280px;
  .alert {
    margin: 20px 0 0;
    &:first-child {
      margin: 0;
    }
  }
}

@media (max-width: 767px) {
  .notification-holder{
    width: 350px;
  }
}

@media (max-width: 479px) {
  .notification-holder{
    width: 300px;
    top: auto;
    bottom: 20px;
    right: 10px;
  }
}

.notification {
  position: fixed; 
  right: 10px !important;
  top: -100px;
  z-index: 999999;
  width: 250px;
  min-height: 50px;
  border-radius: 10px;
  padding: 10px;
  text-align: center;  
}

.notification.show {
  top: 10px !important;
  @include animation(0.7s, notif);
}

.notification.hide {  
  @include animation(0.7s, notif_hide);
}

.spacer-sm-t {margin-top: 10px;}
.spacer-sm-r {margin-right: 10px;}
.spacer-sm-b {margin-bottom: 10px;}
.spacer-sm-l {margin-left: 10px;}

.spacer-md-t {margin-top: 15px;}
.spacer-md-r {margin-right: 15px;}
.spacer-md-b {margin-bottom: 15px;}
.spacer-md-l {margin-left: 15px;}

.spacer-lg-t {margin-top: 20px;}
.spacer-lg-r {margin-right: 20px;}
.spacer-lg-b {margin-bottom: 20px;}
.spacer-lg-l {margin-left: 20px;}

@media only screen and (max-width: 1199px) {
  .spacer-mobile-sm-t {margin-top: 10px;}
  .spacer-mobile-sm-r {margin-right: 10px;}
  .spacer-mobile-sm-b {margin-bottom: 10px;}
  .spacer-mobile-sm-l {margin-left: 10px;}
  
  .spacer-mobile-md-t {margin-top: 15px;}
  .spacer-mobile-md-r {margin-right: 15px;}
  .spacer-mobile-md-b {margin-bottom: 15px;}
  .spacer-mobile-md-l {margin-left: 15px;}
  
  .spacer-mobile-lg-t {margin-top: 20px;}
  .spacer-mobile-lg-r {margin-right: 20px;}
  .spacer-mobile-lg-b {margin-bottom: 20px;}
  .spacer-mobile-lg-l {margin-left: 20px;}
}

.block-height-min {
    min-height: 550px;
}
.block-height-min.remove-min-height {
    min-height: 0;
}

.inline-block {
    display: inline-block;
}

.glyphicon, .fa-chevron-up, .fa-chevron-down {
  color: #428bca;
}

.officials-list {
  &.ui-state-highlight {
      a {
        background: #dff0d8;
      }
  }
}
.courts-schedule-table {
  padding: 0 5px;
  min-height: 350px !important;
  overflow: auto;

  .empty-filters-text {
      position: relative;
      z-index: 1;
  }

  .btn-space {
      margin: 10px;
  }

  tr>th .btn {
     z-index: 1;
     position: absolute;
  }

  th .pull-right {
     margin-top: -1px;
     margin-right: 20px;
  }

  tr>th:after , tr td:first-of-type:after {
    content:'';
    position:absolute;
    left: 0;
    bottom: 0;
    width:100%;
    height: 100%;
    border: 1px solid #ddd;
  }
  table {
    border-left: none;
  }

  tr>th:after {
    border-left: none;
  }
  tr>th:first-of-type:after {
    border-left: 1px solid #ddd;
  }
  tr>th:last-of-type:after {
    border-right: none;
  }

  
  tr td:first-of-type:after {
    border-top: none;
  }

  tbody tr:first-of-type td {
    border-top: none;
  }

  tbody tr td:nth-child(2) {
    border-left: none;
  }

  .table {
    position: relative;
  }

  th {
    position: -webkit-sticky !important;
    position: -moz-sticky !important;
    position: -ms-sticky !important;
    position: -o-sticky !important;
    position: sticky !important;
    border: none !important;
    z-index:100;
    top: -1px;
    background: white;
  }

  th:first-of-type {
    z-index:200;
    left: -1px;
  }

  tr td:first-of-type {
    border: none !important;
    vertical-align: middle;
    position: -webkit-sticky;
    position: -moz-sticky;
    position: -ms-sticky;
    position: -o-sticky;
    position: sticky;
    left: -1px;
    background: white;
}
  .court-tablet {
    display: block;
  }
  .court-mobile {
    display: none;
  }
  .table {
    width: 100%;
    td,th {
      text-align: center;
      white-space: nowrap;
    }
    th {
      vertical-align: middle;
    }
    td {
      cursor: pointer;
      &:hover,
      &.ui-state-highlight.ui-hover-highlight {
          background: #d9edf7;
      }
      &.ui-state-highlight {
        background: #dff0d8;
      }
      .ref-team {
        font-size: 12px;
      }
    }
    .col-1 {
      width: 60px;
    }
  }
  .table.schedule-officials-table {
    td {
      height: 50px;
      vertical-align: middle;
    }
  }
}

.table.table-striped {
  > tbody {
    > tr {
      > td {
        &.ui-selecting {
          background: #fcf8e3 !important;
        }
        &.ui-selected {
          background: #d9edf7 !important;
        }
      }
    }
  }
  
}

@media only screen and (max-width: 991px) {
  .courts-schedule-table {
    padding: 0;
    .table .col-1 {
      width: auto !important;
      min-width: 0 !important;
    }
  }
}

@media only screen and (max-width: 478px) {
  .courts-schedule-table {
    .court-tablet {
      display: none;
    }
    .court-mobile {
      display: block;
    }
  }
  
}

.sw_header_breadcrumbs .breadcrumb {
  .active {
    a {
        color: #777 !important;
    }

    a:hover {
        color: #333 !important;
    }
  }
}

.list-arrow {
  float: right;
}

.list-text-right {
  float: right;
  margin: 0 10px;
}

.division-links {
  span:first-child {
    top: 0px;
  }
  span:hover {
    color: #08243C;
  }
  .links-group a {
    display: inline; 
  }
}

span.list-text-right-sm {
  float: right;
  margin: 0 10px;
  color: #428bca;
}

span.list-text-right-lt {
  @extend span.list-text-right-sm;
  margin: 0px 15px 0px 10px;
}

span.list-text-right-fst {
  @extend span.list-text-right-sm;
  margin: 0 2px 0 15px;
}

.list-text-right-strict {
  float: right;
}

.span-right-strict {
  margin-right: 5px;
}

/* === For portrait phones =================================== */
@media (max-width: 479px) {
  .span-right-strict {
    display: none;
    margin-right: 5px;
  }
}

.mobile-menu {
  float: left;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: 14px;
}

.esw-state {
  padding: 15px 0 10px 0px;
  overflow: hidden;
  float: left;
}

.esw_title {
  margin-top: 0;
  margin-bottom: 20px;
}

.future-item {
  .list-group-item {
    color: #555;
    .counter {
      display: inline-block;
      vertical-align: middle;
      min-width: 45px;
      min-height: 5px;
      font-size: 18px;
      line-height: 21px;
      font-weight: bold;
      padding-bottom: 2px;
      padding-right: 5px;
    }
  }

  &-link {
      padding: 10px;
      cursor: pointer;
  }
}

.brackets-table {
  width: 100%;
  text-align: center;
  border-collapse: separate;
  td {
    padding: 2px 5px;
    white-space: nowrap;
  }
  .bracket-scores {
    display: inline-block;
    vertical-align: top;
    margin-left: 10px;
  }
  .bd {
    border-color: #000;
    border-width: 0;
    border-style: solid;
  }
  .bd-t {
    border-top-width: 2px;
  }
  .bd-b {
    border-bottom-width: 2px;
  }
  .bd-r {
    border-right-width: 2px;
  }
  .bd-l {
    border-left-width: 2px;
  }
  .bd-style-dashed {
    border-style: dashed;
  }
}

@media (max-width: 991px) {
  .esw-state {
    float: none;
    padding: 15px 0 10px 0px;
  }

  .esw-event-list {
      padding-left: 8px;
      padding-right: 8px;
  }
}

@media (max-width: 501px) {
    .esw-event-date-small {
        float: left;
        width: 50px;
    }

    .esw-event-date {
        display: none;
    }
}

@media (min-width: 500px) {
  .esw-event-date-small {
      display: none;
  }

  .esw-event-date {
      width: 80px;
      float: left;
  }
}

.esw-event-lname {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: bold;
}

.esw-event-old {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-style: italic;
}

.text-ellipsis {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.btn-group.btn-vertical-aligned {
  display: table;
  width: 100%;
  .btn-group {
    float: none;
    display: table-cell;
    width: 50%;
    border: 1px solid #ccc;
    background: #fff;
    border-radius: 4px;
    &:first-child {
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }
    &:last-child {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
    .btn {
      display: block;
      width: 100%;
      text-align: center;
      border: none;
      background: none;
      white-space: normal;
    }
  }
}

@media (max-width: 768px) {
  .team-code {
    display: none;
  }
}

@media (max-width: 500px) {
  .events-city {
    display: none;
  }
}

.division-short-name {
  display: inline-block;
  width: 50px;
}

.club-line {
  .info {
    float: left;
    margin: 0 10px 0 0;
  }
  .name {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.loading-image path{
  fill: #333;
}

/* Breadcrumbs from http://bootsnipp.com/snippets/featured/triangle-breadcrumbs-arrows */
.btn-breadcrumb .btn:not(:last-child):after {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  border-top: 17px solid transparent;
  border-bottom: 17px solid transparent;
  border-left: 10px solid white;
  position: absolute;
  top: 50%;
  margin-top: -17px;
  left: 100%;
  z-index: 3;
}
.btn-breadcrumb .btn:not(:last-child):before {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  border-top: 17px solid transparent;
  border-bottom: 17px solid transparent;
  border-left: 10px solid rgb(173, 173, 173);
  position: absolute;
  top: 50%;
  margin-top: -17px;
  margin-left: 1px;
  left: 100%;
  z-index: 3;
}

.btn-breadcrumb .btn {
  padding:6px 12px 6px 24px;
}
.btn-breadcrumb .btn:first-child {
  padding:6px 6px 6px 10px;
}

/** Default button **/
.btn-breadcrumb .btn.btn-default:not(:last-child):after {
  border-left: 10px solid #fff;
}
.btn-breadcrumb .btn.btn-default:not(:last-child):before {
  border-left: 10px solid #ccc;
}
.btn-breadcrumb .btn.btn-default:hover:not(:last-child):after {
  border-left: 10px solid #ebebeb;
}
.btn-breadcrumb .btn.btn-default:hover:not(:last-child):before {
  border-left: 10px solid #adadad;
}

/* The responsive part */
.btn-breadcrumb > * > div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;    
}

.btn-breadcrumb > *:nth-child(n+2) {
  display:none;
  padding:6px 10px 6px 20px;
}

/* === For portrait phones =================================== */
@media (max-width: 479px) {
    .btn-breadcrumb > *:nth-last-child(-n+2) {
        display:block;
    }
    .btn-breadcrumb > .btn {
      padding: 6px 10px 6px 22px;
    }
    .btn-breadcrumb > * div {
        max-width: 95px;
    }
}

/* === For tablets ================================== */
@media (min-width: 480px) and (max-width:991px) {
    .btn-breadcrumb > *:nth-last-child(-n+4) {
        display:block;
    }
    .btn-breadcrumb > * div {
        max-width: 100px;
    }
}

/* === For desktops ================================== */
@media (min-width: 992px) {
    .btn-breadcrumb > *:nth-last-child(-n+6) {
        display:block;
    } 
    .btn-breadcrumb > * div {
        max-width: 170px;
    }
}

.remove-fav {
  margin-left: 10px;
  color: #428bca;
  &:hover {
    color: #2D5679;
  }
}

.future-items {
  padding: 0px;
}

.future-table-desktop, .future-table-mobile {
  margin-bottom: 0px;
  thead {
    font-weight: bold;
  }
  &:hover {
    cursor: pointer;
  }
}

.future-table-mobile {
  tbody td, thead {
    border: none !important;
  }
  thead {
    cursor: auto;
  }
  margin-bottom: 0px;
}

.future-mobile-item {
  margin-bottom: 0px;
}

@media (max-width: 478px) {
  .future-table-desktop {
    display: none;
  }
  .standings-scores {
    margin-top: 5px;
  }
  .standings-scores-data {
    margin-left: 22px !important;
  }
  .rank-cell {
    font-size: 16px;
  }
}

@media (min-width: 478px) {
  .standings-scores {
    margin-top: 15px;
  }
}

@media (max-width: 600px) {
  .future-table-desktop {
    display: none;
  }
}

@media (min-width: 601px) {
  .future-mobile-item {
    display: none;
  }
  .future-table-mobile {
    display: none;
  }
}

.pool-detail-results {
  td {
    border: 1px solid #ddd;
    cursor: pointer;
  } 
  thead > tr > td {
    font-weight: bold;
    border-top: 1px solid #ddd !important;
  }
  .col-1 {
    width: 20%;
  }
  .col-2 {
    width: 20%;
  }
  .col-3 {
    width: 20%;
  }
  .col-4 {
    width: 20%;
  }
  .col-5 {
    width: 20%;
  }
}

.link-arrow {
  display: none;
  float: right;
}

@media (max-width: 478px) {
  .pool-detail-results .scores {
    display: none;
  }
  .link-arrow {
    display: inline-block;
  }
}

.next-link, .prev-link {
  font-size: 16px;
  margin-left: 10px;
}

.pool-icons {
  position: absolute;
  right: 15px;
  width: auto;
  white-space: nowrap;
  overflow: hidden;
  float: right;
}

.absolute-right-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  margin-left: 10px;
}

.match-info-icon {
  float: left;
  padding-top: 1px;
  width: 25px;
}

.match-info-icon.fa-sitemap.fa-rotate-90 {
  position: relative;
  top: 7px;
  left: -3px;
}

.match-info-list {
  display: inline-block;  
  width: 100%;
  .match-opponent {
    margin-left: 25px;
  }
}

.team-code-adaptive {
  width: 20%;
  max-width: 140px;
  float: right;
  margin: 0 10px;
  overflow: hidden;
  white-space: nowrap;
}

@media (max-width: 400px) {
  .team-code-adaptive {
    display: none;
  }
}

.bracket-title {
  padding: 5px 0px 0px 5px; 
}

.bracket-container {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.clip {
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 30px;
}

.clip-link {
  @extend .clip;
  margin-right: 2%;
  width: 31.33%;
  font-size: 12px;
}

.schedule-form > div {
  margin-right: 10px;
  width: 125px;
  display: inline-block;
}

.underlined {
  text-decoration: underline !important;
}

@media (max-width: 500px) {
  .pool-stars {
    display: none;
  }
  .esw-nav {
    display: block;
  }
  .mobile-layout {
    display: inline-block;
  }
  .desktop-layout {
    display: none;
  }
  .match-team-code {
    display: none;
  }
  .team-name {
    display: none;
  }
}

@media (min-width: 500px) {
  .pool-stars {
    display: inline-block;
  }
  .esw-nav {
    display: inline-block;
  }
  .desktop-layout {
    display: inline-block;
  }
  .mobile-layout {
    display: none;
  }
  .team-name {
    display: inline-block;
  }
}

@media (min-width: 1000px) {
  .navbar-header {
    overflow: hidden;
  }
}

@media (min-width: 1200px) {
  .navbar-header {
    /*width: 300px;*/
  }
}

.pool-title-link {
  font-size: 14px;
  margin-top: 5px;
  margin-left: 10px;
} 

.favorite-button {
  float: right;
}

.tap-star {
  margin-left: 10px;
  margin-top: 10px;
}

.red-star {
  color: red;
}

.staff-item {
  overflow: hidden;
  width: 70%;
  text-overflow: ellipsis;
  display: inline-block;
  white-space: nowrap;
}

@media (max-width: 200px) {
  .staff-team-code {
    display: none;
  }
}

.match-day-button {
  margin-right: 20px;
}

.match-search-item:hover {
  background: #dddddd;
  cursor: pointer;
}

.match-title {
  float: right;
}

.match-name {
  font-weight: bold;
}

.dl-event-horizontal dt {
  float: left;
  width: 160px;
  clear: left;
  text-align: left !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.social-img {
    width: 24px;
    height: 24px;
}

// jquery UI additional styles\
.ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted black;
}

.link-block {
    overflow-wrap: break-word;
}

.match-time-form {
  margin-top: -20px !important;

  .validation-invalid {
    display: none;
  }
}

.input-side-text {
  font-weight: bold;
  font-size: 16px;
  padding: 10px;
}

.time-results-btn {
  margin-top: 15px !important;
}

.error-input {
  border: 2px solid #d43f3a;
}

.division-line td {
  padding: 10px !important;
  cursor: pointer;
}

.spinner-wrapper {
  width: 100%;
}

.flow-chart {
  width: 100%;
}

.page-title {
  margin-left: 20px;
}

.line-red {
  color: red;
}

@media (max-width: 460px) {
  .prevqual-table > thead > tr > td:nth-child(3),
  .prevqual-table > tbody > tr > td:nth-child(3) {
    display: none;
  }

  .prevqual-table.division-filtered > thead > tr > td:first-child,
  .prevqual-table.division-filtered > tbody > tr > td:first-child {
    display: none;
  }

  .div-short {
    display: inline-block;

    select {
      &.divisions-desktop {
        display: none;
      }
      &.divisions-mobile {
        display: inline-block;
      }
    }
  }
}

@media (min-width: 460px) {
  .prevqual-table > thead > tr > td:nth-child(3),
  .prevqual-table > tbody > tr > td:nth-child(3) {
    display: block;
  }

  .prevqual-table.division-filtered > thead > tr > td:first-child,
  .prevqual-table.division-filtered > tbody > tr > td:first-child {
    display: block;
  }

  .div-short {
    display: inline-block;

    select {
      &.divisions-desktop {
        display: block;
      }
      &.divisions-mobile {
        display: none;
      }
    }
  }
}

.bracket-bottom-text {
  margin-top: 20px;
}

.footnote-icon {
  margin-left: 25px;
}

@media (min-width: 460px) {
  .team-row, .score-row {
    td:nth-child(2), td:nth-child(3) {
      width: 200px;
    }
  } 
}

@media (max-width: 460px) {
  .team-row, .score-row {
    td:nth-child(2), td:nth-child(3) {
      width: 100px;
    }
  } 
}

.match-team-box table {
  table-layout:fixed;

  td {
    overflow: hidden;
  }
}

.accepted-bid {
    border: 3px #FFEF00 solid;
    padding: 2px;
}

.event-notes {
  h1 { font-size: 2em; }
}

.buy-admission {
    margin-bottom: 20px;
    display: block;
    width: 140px;
    border: 1px #ccc solid;

    cursor: pointer;
    color: rgb(51, 51, 51);
}

.buy-admission:visited, .buy-admission:hover {
    color: rgb(51, 51, 51);
    text-decoration: none;
}

.prev-qual-badge {
    margin: 3px;
}
.pool-brackets-headings {
    padding: 0 15px;
    .pb-stats-headings {
        margin-top: 30px;
    }
}


