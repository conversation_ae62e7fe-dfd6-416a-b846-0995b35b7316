'use strict';

module.exports = {
    // get /api/event/:event/tickets/reg/additional
    getAdditionalRegFields: async (req, res) => {
        const eventID = Number(req.params.event);

        if (!eventID) {
            return res.validation('No event id provided');
        }

        try {
            const fields = await EventService.ticketsAdditionalFields.getFields(eventID);

            res.status(200).json(fields);
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/reg/additional
    saveAdditionalRegField: async (req, res) => {
        const eventID = Number(req.params.event);
        const fieldData = req.body;

        if (!eventID) {
            return res.validation('No event id provided');
        }

        try {
            await EventService.ticketsAdditionalFields.saveField(eventID, fieldData);
            await SalesHubService.sync.syncCustomField(eventID, fieldData.field);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    // put /api/event/:event/tickets/reg/additional
    updateAdditionalRegFields: async (req, res) => {
        const eventID = Number(req.params.event);
        const fieldsData = req.body.additional;

        if (!eventID) {
            return res.validation('No event id provided');
        }

        try {
            await EventService.ticketsAdditionalFields.saveFields(eventID, fieldsData);
            await SalesHubService.sync.syncCustomFields(eventID);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // post /api/event/:event/tickets/reg/additional_default
    setAdditionalDefault: async (req, res) => {
        let eventID = Number(req.params.event);

        if (!eventID) {
            return res.validation('Invalid Event ID Specified');
        }

        let eventOwnerID = eventOwnerService.findId(eventID, req.user);

        try {
            let additionalFields = await EventService.ticketsAdditionalFields.addDefaultFields(eventID, eventOwnerID);

            await SalesHubService.sync.syncCustomFields(eventID);

            res.status(200).json({ additional: additionalFields });
        } catch (err) {
            res.customRespError(err);
        }
    }
};
