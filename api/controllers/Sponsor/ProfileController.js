'use strict';

const Joi 		    = require('joi');
const sponsorSchema = require('../../validation-schemas/sponsor').sponsor;

const DEFAULT_SALES_MANAGER_EMAIL = '<EMAIL>';

module.exports = {
    find: function (req, res) {
        let sponsorIDs  = req.params.sponsor
            ? [Number(req.params.sponsor)]
            : req.session.passport.user.sponsor_ids || [];

        if (!Array.isArray(sponsorIDs) || _.isEmpty(sponsorIDs)) {
            return res.ok({});
        }

        let fields = [
            'first', 'last', 'email', 'mobile_phone', 'office_phone', 'street', 'city', 'state', 'zip',
            'company_name', 'company_description', 'badge_names', 'is_exhibitor', 'is_sponsor', 'is_non_profit',
            'is_other', 'website_url', 'sponsor_title', 'samples_food', 'samples_beverages'
        ];

        let query = squel.select().from('sponsor')
            .fields(fields)
            .where('sponsor_id IN ?', sponsorIDs)
            .order('created');

        Db.query(query).then(function (result) {
            if (result.rowCount === 0) {
                return res.notFound('Sponsor profile does not exists');
            }

            res.ok(result.rows[0]);
        }).catch(err => {
            res.customRespError(err);
        })
    },

    create: function(req, res) {
        let sponsorIDs = req.session.passport.user.sponsor_ids || [];

        if(Array.isArray(sponsorIDs) && !_.isEmpty(sponsorIDs)) {
            return res.validation('Sponsor profile already created');
        }

        let validation = sponsorSchema.validate(req.body);

        if (validation.error) {
            return res.status(400).json({ validationErrors: validation.error.details })
        }

        if(req.body.company_samples) {
            if(!req.body.samples_beverages && !req.body.samples_food) {
                return res.status(400).json({error: 'No samples are chosen'});
            }
        }

        let userId = req.session.passport.user.user_id;

        let query = squel.insert().into('sponsor')
            .setFields(req.body)
            .set('user_id', userId)
            .set('added_by_sales_id',
                squel.select().from('sales_manager', 'sm')
                    .field('sm.sales_manager_id')
                    .join(
                        'user', 'u',
                        squel.expr()
                            .and('u.user_id = sm.user_id')
                            .and('u.email = ?', DEFAULT_SALES_MANAGER_EMAIL)
                    )
            )
            .returning('sponsor_id');

        Db.query(query)
            .then(({rows}) => {
                req.session.passport.user.sponsor_ids = [rows[0].sponsor_id];
                res.status(201).ok(); 
            })
            .catch(res.customRespError.bind(res));
    },

    update: function(req, res) {
        let sponsorIDs  = req.params.sponsor
            ? [Number(req.params.sponsor)]
            : req.session.passport.user.sponsor_ids || [];

        if (!sponsorIDs.length) {
            return res.forbidden('You are not a sponsor');
        }

        let validation = sponsorSchema.validate(req.body);

        if (validation.error) {
            return res.status(400).json({ validationErrors: validation.error.details })
        }

        if(req.body.company_samples) {
            if(!req.body.samples_beverages && !req.body.samples_food) {
                return res.status(400).json({error: 'No samples are chosen'});
            }
        }

        let query = squel.update().table('sponsor', 's')
            .setFields(req.body)
            .where('s.sponsor_id = ?',
                squel.select().from('sponsor', 's1')
                    .field('s1.sponsor_id')
                    .where('s1.sponsor_id IN ?', sponsorIDs)
                    .order('created')
                    .limit(1)
                );

        Db.query(query)
            .then(() => res.ok())
            .catch(res.customRespError.bind(res));
    },

    //GET /api/sponsor/:sponsor/info
    info: async function (req, res) {
        let sponsorIDs  = req.params.sponsor
            ? [Number(req.params.sponsor)]
            : req.session.passport.user.sponsor_ids || [];

        if(!Array.isArray(sponsorIDs) || _.isEmpty(sponsorIDs)) {
            return res.status(400).json({error: 'No item selected.'});
        }

        try {
            let paymentsQuery = squel.select().from('purchase_booth', 'pb')
                .field('e.name', 'event_name')
                .field('pb.created')
                .field('pb.amount')
                .field('p.date_paid')
                .field('p.type')
                .field('pb.quantity')
                .field('eb.fee')
                .field('eb.title')
                .field('eb.description')
                .left_join('purchase', 'p', 'p.purchase_id = pb.purchase_id')
                .left_join('event_booth', 'eb', 'eb.event_booth_id = pb.event_booth_id')
                .left_join('event', 'e', 'e.event_id = eb.event_id')
                .where('p.payment_for = \'booths\'')
                .where('p.sponsor_id IN ?', sponsorIDs)
                .order('pb.created', false);

            let sponsorQuery = squel.select().from('sponsor')
                .fields(['first', 'last', 'company_name'])
                .where('sponsor_id IN ?', sponsorIDs);

            let [payments, sponsor] = await Promise.all([
                Db.query(paymentsQuery).then(({rows}) => rows),
                Db.query(sponsorQuery).then(({rows}) => rows[0])
            ]);

            res.status(200).json({sponsor, payments});
        } catch (err) {
            res.customRespError(err);
        }
    }
};
