var argv 	= require('optimist').argv,

	API_VER = sails.config.stripe_api.version;

module.exports = {
	/*
	* Create a Special-Case Transfer to a Connected Account
	* More Details: https://stripe.com/docs/connect/special-case-transfers
	* URL: 
	*/
	createTransfer: function (req, res) {
		var $accountId 	= req.body.account,
			$amount 	= parseFloat(req.body.amount, 10);

		if(!$accountId) return res.validation('Invalid Account Id');
		if(!$amount) return res.validation('Invalid Amount');

		__retrievePlatformSecretKey()
		.then(function (secretKey) {
			return __createTransfer(secretKey, $accountId, $amount)
		}).then(function (transfer) {
			return __saveStripeTransfer(transfer, $amount, $accountId)
		}).then(function (transfer) {
			res.status(200).json({
    			transfer: {
    				id: transfer.id
    			}
    		})
		}).catch(function (err) {
			res.customRespError(err);
		})
	}
}

function __saveStripeTransfer (transfer, amount, accountId) {
	return Db.query(
    	squel.insert().into('stripe_transfer')
    	.setFields({
    		amount 				: amount,
    		stripe_account_id 	: accountId,
    		stripe_transfer_id 	: transfer.id,
    	})
    ).then(function () {
    	return transfer
    }, function () {
    	loggers.errors_log.error(transfer)
    	return transfer;
    })
}

function __createTransfer (sk, connectedAccountId, amount) {
	return new Promise(function (resolve, reject) {
		var stripe = require('stripe')(sk, { apiVersion: API_VER });

		stripe.transfers.create({
		    amount 		: (Math.round(parseFloat(amount) * 100)),
		    currency 	: 'usd',
		    destination : connectedAccountId
		  }, function (err, transfer) {
		  	if(err) {
				reject(err)
			} else {
				resolve(transfer);
			}
		  }
		);
	})
}

function __retrievePlatformSecretKey () {
	return Db.query(
    	`SELECT "value"->>'secret_key' "secret_key" FROM "settings" WHERE "key" = $1`,
    	[(argv.dev?'stripe_connect_dev':'stripe_connect')]
    ).then(function (result) {
    	var row  		= _.first(result.rows),
    		secret_key 	= row && row.secret_key;
    	if(!secret_key) {
    		throw {
    			validation: 'No Platform Secret Key found'
    		}
    	}
    	return secret_key
    })
}
