'use strict';

const
    crypto                  = require('crypto'),
    co                      = require('co'),
    passwordRecoverySchema  = require('../validation-schemas/user').password_recovery,
    UserService = require('../services/UserService'),
    {RECOVERY_CODE_LIFE_SECONDS, MIN_PASSWORD_LENGTH} = require("../constants/users-auth");

const ALLOW_RECOVER_NOT_ACTIVATED_AFTER_SECONDS   = 15 * 60;

module.exports = {
    // post /api/recover/send
	send: function (req, res) {
        let $email = req.body.email;
        const isCreationMode = req.body.isCreationMode;

        const validationRes  = passwordRecoverySchema.validate(req.body);

        let userAgent   = req.headers['user-agent'] || null,
            ip          = req.headers['x-forwarded-for'] || req.connection.remoteAddress || null;

        if (validationRes.error) {
            return res.customRespError({ validationErrors: validationRes.error.details });
        }

        $email = $email.trim().toLowerCase();

        co(function* () {
            // Check duplicate sending
            let result = yield (Db.query(
                squel.select().from('password_recovery')
                    .field('COUNT(*)::INT')
                .where('LOWER(TRIM(email)) = ?', $email)
                .where('recovered_at IS NULL')
                .where('user_id IS NOT NULL')
                .where('EXTRACT(EPOCH FROM NOW() - CREATED)::INT <= ?', RECOVERY_CODE_LIFE_SECONDS)
            ));

            if(+result.rows[0].count > 0) {
                return Promise.reject({
                    validationErrors: [{
                        message: `Message with recovery instructions have been sent to your email recently. 
                                 Please check your inbox. Also, make sure it’s not in your SPAM folder.`,
                        path: 'email',
                    }]
                });
            }

            // Check user exists
            result = yield (Db.query(
                squel.select().from('user')
                    .field('user_id')
                    .field('email')
                    .field('activated')
                    .field('EXTRACT(EPOCH FROM NOW() - CREATED)::INT', 'delta')
                .where('LOWER(TRIM(email)) = ?', $email)
            ));

            // handle request

            let user = result.rows[0];

            let userExists = !_.isEmpty(user);

            if (!userExists) {
                return Promise.reject({
                    validationErrors: [{
                        message: `User with email "${$email}" doesn't exist.`,
                        path: 'email',
                    }]
                });
            }

            let allowRecovery
                        = userExists && (user.activated || +user.delta < ALLOW_RECOVER_NOT_ACTIVATED_AFTER_SECONDS);

            let pswdRowSQL = 
                squel.insert().into('password_recovery')
                    .set('ip_address', ip)
                    .set('user_agent', userAgent)
                    .set('email', $email);

            let recoveryCode;

            if (allowRecovery) {
                let salt        = crypto.randomBytes(8).toString('hex');
                recoveryCode    = crypto.createHash('md5').update(user.email + salt).digest('hex');

                pswdRowSQL.set('recovery_code', recoveryCode);
                pswdRowSQL.set('user_id', user.user_id);
            }

            yield (Db.query(pswdRowSQL));

            if (!allowRecovery) {
                return Promise.reject({
                    validationErrors: [{
                        message: 'User is not activated',
                        path: 'email',
                    }]
                });
            }

            let template = 'password_recovery_letter';
            let link = `${sails.config.urls.home_page.baseUrl}/recovery?code=${recoveryCode}`;
            let subject = 'Password recovery';

            if(isCreationMode) {
                template = 'password_creation_letter';
                link = `${sails.config.urls.home_page.baseUrl}/password-generation?code=${recoveryCode}`;
                subject = 'Generate Account Password Link';
            }

            yield (EmailService.renderAndSend({
                template    : template,
                data        : { link },
                from        : 'SportWrench <<EMAIL>>',
                to          : $email,
                subject     : subject
            }))
        }).then(() => {
            res.status(200).end();
        }).catch(err => {
            res.customRespError(err)
        });
    },

    check_code: function(req, res) {
        var code = req.param('code');
        if (!code)
            return res.badRequest('no code');

        var q = squel.update()
            .table('password_recovery')
            .set('opened_at = now()')
            .where('recovery_code = ?', code)
            .where('extract(epoch from now() - created)::int <= ?',
                RECOVERY_CODE_LIFE_SECONDS);

        Db.query(q)
        .then(result => {
            if(result.rowCount === 0) {
                res.badRequest('invalid code');
            } else {
                res.ok();
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },

    reset: async (req, res) => {
        try {
            const code = req.param('code');
            const password = req.param('password');

            if (!code) {
                throw { validation: 'No code' };
            }

            if (!password) {
                throw { validation: 'No password' };
            }

            if (password.length < MIN_PASSWORD_LENGTH) {
                throw { validation: 'Password must be at least 6 characters long' };
            }

            await UserService.auth.passwordRecovery.resetPassword(code, password);

            return res.status(200).json('Password has been updated');
        } catch (e) {
            res.customRespError(e);
        }
    }
};

