
const requestBodySchema = require('../../../../../validation-schemas/vertical-insurance').teamsQuoteRequestBody;

// post api/vertical-insurance/team-registration/event/:event/quote
module.exports = {
    friendlyName: 'Create Quote',
    description: 'Creates Vertical Insurance Quote',

    inputs: {
        event: {
            type: 'number',
            example: 123565,
            description: 'Event Event identifier',
            required: true,
        }
    },

    exits: {
        created: {
            statusCode: 201,
        },
        user_not_found: {
            statusCode: 401,
        },
        master_club_not_found: {
            statusCode: 400,
        },
        validation_error: {
            statusCode: 400,
        }
    },

    fn: async function (inputs, exits) {
        const event = inputs.event;
        const user = this.req.user;
        const masterClubID = Number(this.req.session.passport.user.master_club_id);
        const teams = this.req.body?.teams || [];

        if(_.isEmpty(user)) {
            return exits.user_not_found();
        }

        if(!masterClubID) {
            return exits.master_club_not_found();
        }

        try {
            const bodyValidationError = validateBody(teams);

            if(bodyValidationError) {
                return exits.validation_error(bodyValidationError);
            }

            const quoteData = await VerticalInsuranceService.teamRegistrationQuote.create(
                event, masterClubID, user, teams
            );

            exits.created({ quote: quoteData });
        } catch (error) {
            this.res.customRespError(error);
        }
    },
};

function validateBody(body) {
    const { error } = requestBodySchema.validate(body);

    if (error) {
        return error.details[0].message;
    }
}
