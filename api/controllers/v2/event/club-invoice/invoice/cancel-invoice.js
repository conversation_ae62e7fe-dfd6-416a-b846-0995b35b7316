
//POST /api/event/:event/club-invoice/:invoice/cancel
module.exports = {
    friendlyName: 'Club Invoice Cancel',
    description: 'Makes a club invoice canceled',

    inputs: {
        event: {
            type: 'number',
            description: 'Event Id',
            required: true
        },
        invoice: {
            type: 'number',
            description: 'Club Invoice Id',
            required: true
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventId, invoice: invoiceId } = inputs;
        const userData = {
            eventOwnerId: this.req.session.passport.user.event_owner_id,
            userId: Number(this.req.session.passport.user.user_id),
        }

        try {
            await ClubInvoiceService.editing.cancelClubInvoice(eventId, invoiceId, userData);

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
