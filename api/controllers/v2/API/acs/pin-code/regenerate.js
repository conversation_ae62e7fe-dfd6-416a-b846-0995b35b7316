const PIN_CODE_LENGTH = 5;
const ACS_PIN_CODE_DB_UNIQUE = 'action_clip_streaming_pin_codes_unique';
const MAX_REGENERATION_ATTEMPTS_COUNT = 5;
const EVENT_CHANGE_ACTION = 'acs.pincode.code.generated';

module.exports = {
    friendlyName: 'Verify the team/pin code pair',
    description: 'Checks if team pin code is valid on event',
    
    inputs: {
        event_id: {
            type: 'ref',
            description: 'Event ID'
        },
        team_code: {
            type: 'ref',
            description: 'Team organization code'
        },
        pin_code: {
            type: 'ref',
            description: 'ACS Streaming team pin code'
        }
    },
    
    exits: {
        success: {
            statusCode: 201
        },
        resourceNotFound: {
            statusCode: 404
        },
        validationError: {
            statusCode: 400
        },
        serverError: {
            statusCode: 500
        }
    },
    
    fn: async function(inputs, exits) {
        const eventId = Number(inputs.event_id);
        const teamCode = inputs.team_code;

        const paramsValidationError = await sails.helpers.acs.validateProperties.with({
            properties: {
                event_id: eventId,
                team_code: teamCode
            }
        });

        if (paramsValidationError) {
            throw {
                validationError: {
                    error: paramsValidationError
                }
            };
        }

        const dataExistenceError = await sails.helpers.acs.checkEventTeamByCode.with({ eventId, teamCode });

        if (dataExistenceError) {
            throw {
                resourceNotFound: {
                    error: {
                        messages: [dataExistenceError],
                        properties: []
                    }
                }
            };
        }
        
        try {
            const pinCode = await _regeneratePinCode({ eventId, teamCode });

            exits.success({ pin_code: pinCode });
        } catch(err) {
            loggers.errors_log.error(err);

            throw {
                serverError: {
                    error: {
                        messages: [err.message || 'Server internal error'],
                        properties: []
                    }
                }
            };
        }
    }
};

async function _regeneratePinCode({ eventId, teamCode, attempt = 0 }) {
    const pinCode = _generatePinCode();

    if(attempt > MAX_REGENERATION_ATTEMPTS_COUNT) {
        throw new Error('Max attempts count for ACS pin code generation exceed.');
    }

    try {
        const rosterTeam = await _updateTeamPinCode(eventId, teamCode, pinCode);
        
        const eventChangeData = {
            event_id: eventId,
            action: EVENT_CHANGE_ACTION,
            roster_club_id: rosterTeam.roster_club_id,
            roster_team_id: rosterTeam.roster_team_id,
            comments: `Re-generated ACS pincode - ${pinCode}`
        };
        await _savePincodeHistory(eventChangeData);
        
        return pinCode;
    } catch(err) {
        if(Db.utils.isUniqueConstraintViolation(err, ACS_PIN_CODE_DB_UNIQUE)) {
            attempt++;

            return await _regeneratePinCode({ eventId, teamCode, attempt });
        }

        throw err;
    }
}

function _generatePinCode(n = PIN_CODE_LENGTH) {
    return Math.floor(Math.random() * (9 * (Math.pow(10, n - 1)))) + (Math.pow(10, n - 1));
}

async function _updateTeamPinCode ( eventId, teamCode, pinCode) {
    const query = knex('roster_team')
        .update({ action_clip_streaming_pin: pinCode })
        .where({
            organization_code: teamCode,
            event_id: eventId
        })
        .returning(['roster_club_id', 'roster_team_id']);

    let result = await Db.query(query);

    if(result?.rowCount > 0) {
        return result.rows[0];
    }
    
    throw new Error('Team or event not found');
}

async function _savePincodeHistory(eventChangeData) {
    const query = knex('event_change').insert(eventChangeData);
    await Db.query(query);
}
