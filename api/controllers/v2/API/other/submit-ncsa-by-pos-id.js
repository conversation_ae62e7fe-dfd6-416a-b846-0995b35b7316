//POST /api/ncsa/submit/point-of-sales/:pointOfSalesId/athlete

const { GENDER_VALUES } = require('../../../../constants/common');

const GENDERS = [GENDER_VALUES.FEMALE, GENDER_VALUES.MALE];

module.exports = {
    friendlyName: 'Submit NCSA by point of sales ID',
    description: 'Submit NCSA by point of sales ID',

    inputs: {
        athlete_first: {
            type: 'string',
            description: 'First Name',
            required: true,
        },
        athlete_last: {
            type: 'string',
            description: 'Last Name',
            required: true,
        },
        athlete_gradyear: {
            type: 'number',
            description: 'Grad Year',
            required: true,
        },
        gender: {
            type: 'string',
            isIn: GENDERS,
            description: 'Gender',
            required: true,
        },
        parent_email: {
            type: 'string',
            description: 'Parent Email',
            required: true,
        },
        parent_first: {
            type: 'string',
            description: 'Parent First Name',
            required: true,
        },
        parent_last: {
            type: 'string',
            description: 'Parent Last Name',
            required: true,
        },
        parent_phone: {
            type: 'string',
            description: 'Parent Phone Number',
            required: true,
        },
        zip: {
            type: 'string',
            description: 'Zip code',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
        notFound: {
            statusCode: 400,
        }
    },

    fn: async function (inputs, exits) {
        try {
            const { pointOfSalesId } = this.req.params;

            const pointOfSales = await findEventByPointOfSalesId(pointOfSalesId) // pass event id here from params, should i enter it as inputs?

            if(!pointOfSales) {
                return exits.notFound();
            }

            await NCSAService.submitAthleteInfoToNCSA(
                pointOfSales.sw_event_id,
                inputs
            );

            return exits.success({
                success: true,
            });
        } catch (err) {
            if (!_.isUndefined(err.success)) {
                this.res.status(400).json(err);
            } else {
                this.res.customRespError(err);
            }
        }
    },
};

async function findEventByPointOfSalesId(posId) {
    const query = knex('event_point_of_sales')
        .select('event_point_of_sales_id', 'sw_event_id')
        .where('point_of_sales_id', posId);

    return Db.query(query).then(({ rows }) => rows[0]);
}
