module.exports = {
    friendlyName: 'Mark notification as read',

    inputs: {
        id: {
            type: 'number',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function(inputs, exits) {
        try {
            await UserNotificationService.markAsRead(this.req.user.user_id, inputs.id);

            return exits.success({ message: 'Notification marked as read' });
        } catch (error) {
            return this.res.customRespError(error);
        }
    }
};
