// PUT /api/club/v2/purchase/payment-hub/change-type

module.exports = {
    friendlyName: 'Teams payment change type for payment hub',
    description: 'Payment Type change for payment hub',

    inputs: {
        event: {
            type: 'number',
            required: true
        },
        amount: {
            type: 'number',
            required: true
        },
        payment_hub_payment_intent_id: {
            type: 'string',
            required: true
        },
        type: {
            type: 'string',
            required: false
        },
        purchase_id: {
            type: 'number',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const master_club_id = Number(this.req.user.master_club_id);
        const club_owner_id = Number(this.req.user.club_owner_id);
        const season = sails.config.sw_season.current;
        const user = {
            ..._.pick(this.req.user, 'user_id', 'email', 'first', 'last'),
            phone: this.req.user.phone_mob
        };

        const data = {
            purchase_id: inputs.purchase_id,
            event_id: inputs.event,
            amount: inputs.amount,
            payment_hub_payment_intent_id: inputs.payment_hub_payment_intent_id,
            type: inputs.type,
            club_owner_id, 
            master_club_id, 
            season, 
            user,
        }

        try {
            await PaymentService.teams.changePaymentHubPaymentType(data);

            exits.success()
        } catch (err) {
            this.res.customRespError(err);
        }
    }
};
