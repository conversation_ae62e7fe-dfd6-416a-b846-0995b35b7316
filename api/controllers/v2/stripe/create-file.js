const ALLOWED_PURPOSES = Object.values(StripeService.files.PURPOSE);

module.exports = {
    friendlyName: "Stripe File Upload",
    description: "Uploads file to stripe",

    files: ["file"],

    inputs: {
        file: {
            type: "ref",
            required: true,
            description: "File to upload",
        },
        filename: {
            type: "string",
            required: true,
            description: "Stripe file name",
        },
        purpose: {
            type: "string",
            required: true,
            description: "Stripe file purpose",
            isIn: ALLOWED_PURPOSES,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ file, filename, purpose }, exits) {
        try {
            const fileBuffer = await __getInputFileBuffer(file);
            const stripeFile = await StripeService.files.uploadFile({
                file: fileBuffer,
                filename,
                purpose
            });

            exits.success({ file: stripeFile });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

const __getInputFileBuffer = async (ref) => {
    const [file] = ref._files;

    if (!file) {
        return null;
    }

    return __loadBuffer(file.stream);
};

const __loadBuffer = async (stream) => {
    return new Promise((resolve, reject) => {
        const _buf = [];

        stream.on("data", (chunk) => _buf.push(chunk));
        stream.on("end", () => resolve(Buffer.concat(_buf)));
        stream.on("error", (err) => reject(err));
    });
};
