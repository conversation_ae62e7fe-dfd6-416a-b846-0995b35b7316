// POST /api/sales-hub/webhook

const config = sails.config.salesHub;

module.exports = {
    friendlyName: 'Sales Hub Webhook',
    description: 'Sales Hub Webhook',

    inputs: {
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const data = this.req.body;

        validateWebhookRequest(this.req)

        try {
            await SalesHubService.webhook.processWebhook(data);

            exits.success()
        } catch (err) {
            return this.res.customRespError(err);
        }
    }
};


function validateWebhookRequest(req) {
    const token = req.headers['x-auth-token'];

    const isAuthorized = token && token === config.webhookSecret;

    if (!isAuthorized) {
        throw new Error('Request not authorized')
    }
}