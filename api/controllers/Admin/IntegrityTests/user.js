module.exports = [
    {
        title: 'Duplicated Event Officials (same official_id at one event)',
        query:
        `
            SELECT eo.event_id,
                   eo.official_id,
                   string_agg(eo.event_official_id::TEXT, ', ') ids,
                   string_agg(eo.created::TEXT, ', ')           dates_created,
                   COUNT(eo.event_official_id)                  cnt
            FROM "event_official" eo
            WHERE eo.official_id > 0
              AND eo.is_official IS TRUE
              AND eo.deleted IS NULL
            GROUP BY eo.event_id, eo.official_id
            HAVING COUNT(eo.event_official_id) > 1;
        `
    }

];
