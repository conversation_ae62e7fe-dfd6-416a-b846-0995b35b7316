'use strict';

module.exports = {
    // POST /api/admin/club/master/copy-member/:from/:to
    createOppositeRoleForMember: function (req, res) {
        const from = req.param('from');
        const to = req.param('to');
        const { membership_number, birthdate } = req.body;
        
        AdminClubMemberService.createOppositeRoleForMember(
            from, 
            to,
            membership_number,
            birthdate
        )
            .then(() => res.status(200).ok())
            .catch((err) => res.customRespError(err));
    },
    // POST /api/admin/club/master/athlete/tostaff
    masterAthleteToStaff: function (req, res) {
        if(!req.body.master_athlete_id) return res.status(400).json({error: 'Required an athlete identifier'});

        TeamMembersService.moveMasterAthleteToStaff({
            master_athlete_id: req.body.master_athlete_id,
            master_team_id: req.body.master_team_id,
            bg_screening: req.body.bg_screening,
            cert: req.body.cert,
            staff_role: req.body.staff_role
        }, function (err) {
            if(err) return res.serverError(err);
            return res.ok();
        })
    },
    // GET /api/admin/club/master/athlete/find
    findMasterAthletes: function (req, res) {
        
        var $first = req.param('first');
        var $last = req.param('last');
        var $email = req.param('email');
        var $usav = req.param('usav');

        var params = [];

        var query = 
            'SELECT  \
                ma.master_athlete_id, \
                ma.organization_code, \
                ma.first, \
                ma.last, \
                ma.email, \
                ma.master_club_id, \
                mc.club_name, \
                ma.age \
            FROM master_athlete ma \
            LEFT JOIN master_club mc \
                ON ma.master_club_id = mc.master_club_id \
            WHERE ma.deleted IS NULL';

        if($first) {
            params.push('%' + $first + '%');
            query += ' AND lower(ma.first) LIKE lower($' + params.length + ')';
        }

        if($last) {
            params.push('%' + $last + '%');
            query += ' AND lower(ma.last) LIKE lower($' + params.length + ')';
        }

        if($email) {
            params.push('%' + $email + '%');
            query += ' AND lower(ma.email) LIKE lower($' + params.length + ')';
        }

        if($usav) {
            params.push('%' + $usav + '%'); 
            query += ' AND lower(ma.organization_code) LIKE lower($' + params.length + ')';       
        }

        if(!params.length) return res.status(400).json({error: 'Fill the parameters fields'});

        query += ' ORDER BY mc.club_name, ma.organization_code';
        
        Db.query(query, params).then(result => {
            res.status(200).json({ athletes: result.rows });
        }).catch(err => {
            res.customRespError(err);
        })
    },

    // GET /api/admin/club/:club/teams
    clubTeams: function (req, res) {
        var $master_club_id = +req.param('club');
        if(!$master_club_id) return res.status(400).json({error: 'No club identifier specified.'});

        var query = 
            'SELECT \
                mt.master_team_id, \
                mt.team_name \
            FROM master_team mt \
            WHERE mt.master_club_id = $1 \
            AND mt.deleted IS NULL';

        Db.query(query, [$master_club_id]).then(result => {
            res.status(200).json({ teams: result.rows });
        }).catch(err => {
            res.customRespError(err);
        });
    },

    // GET /api/admin/club/staff/roles
    staffRoles: function (req, res) {
        Db.query(
            `SELECT r.role_id "id", r.name 
             FROM "role" r 
             WHERE r.role_id IN (4, 5, 6, 15, 7)`
        ).then(result => {
            res.status(200).json({ roles: result.rows });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // GET /api/admin/club/members/find
    findMembers: function (req, res) {
        var $first                  = req.query.first,
            $last                   = req.query.last,
            $clubName               = req.query.club_name,
            $clubCode               = req.query.club_code,
            season                  = sails.config.sw_season.current,
            sqlParams               = [season],
            athleteConditionsBlock  = '',
            staffConditionsBlock    = '';

        if(_.isEmpty(req.query))
            return res.validation('No params passed')

        if($first) {
            sqlParams.push(`%${$first}%`)
            athleteConditionsBlock += ` AND ma.first ILIKE $${sqlParams.length}`;
            staffConditionsBlock += ` AND ms.first ILIKE $${sqlParams.length}`;
        }

        if($last) {
            sqlParams.push(`%${$last}%`)
            athleteConditionsBlock += ` AND ma.last ILIKE $${sqlParams.length}`;
            staffConditionsBlock += ` AND ms.last ILIKE $${sqlParams.length}`;
        }

        if($clubName) {
            sqlParams.push(`%${$clubName}%`)
            athleteConditionsBlock += ` AND mc.club_name ILIKE $${sqlParams.length}`;
            staffConditionsBlock += ` AND mc.club_name ILIKE $${sqlParams.length}`;
        }

        if($clubCode) {
            sqlParams.push(`%${$clubCode}%`)
            athleteConditionsBlock += ` AND mc.code ILIKE $${sqlParams.length}`;
            staffConditionsBlock += ` AND mc.code ILIKE $${sqlParams.length}`;
        }

        Db.query(
            `SELECT 
                'Player' "type",
                ma.gender,
                FORMAT('%s %s', ma.first, ma.last) "name",
                ma.organization_code "usav",
                ma.usav_number,
                mc.club_name
            FROM "master_athlete" ma
            LEFT JOIN "master_club" mc
                ON mc.master_club_id = ma.master_club_id
            WHERE ma.deleted is null
                AND ma.season = $1          
                ${athleteConditionsBlock}
            UNION ALL
            SELECT
                'Staff' "type",
                ms.gender,
                FORMAT('%s %s', ms.first, ms.last) "name",
                ms.organization_code "usav",
                ms.usav_number,
                mc.club_name
            FROM "master_staff" ms
            LEFT JOIN "master_club" mc
                ON mc.master_club_id = ms.master_club_id
            WHERE ms.deleted is null
                AND ms.season = $1          
                ${staffConditionsBlock}
            ORDER BY "type"`, 
            sqlParams
        )
        .then(function (result) {
            res.status(200).json({ members: result.rows })
        }, function (err) {
            res.customRespError(err);
        })
    },

    // POST /api/admin/club/athletes/update-height
    updateAthleteHeight: async function(req, res) {
        try {
            await AdminClubMemberService.updateAthletesHeight();

            res.ok();
        } catch (err) {
            return res.customRespError(err);
        }
    }
}
