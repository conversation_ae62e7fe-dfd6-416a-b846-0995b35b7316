'use strict';

module.exports = {
    // api/admin/event/:event/tickets
    all: function (req, res) {
        var $event_id = +req.params.event;
        if(!$event_id) return res.validation('Event id is required')

        var query = 
            'SELECT  \
                e.event_id,  \
                e.long_name, \
                e.tickets_sw_fee application_fee, \
                ( \
                    SELECT array_to_json(array_agg(row_to_json(t))) \
                    FROM ( \
                        SELECT \
                            et.published, \
                            et.event_ticket_id, \
                            et.label, \
                            et.short_label, \
                            et.current_price::NUMERIC, \
                            et.initial_price::NUMERIC, \
                            et.application_fee, \
                            ( \
                                SELECT  \
                                    string_agg( ("value"->>\'value\')::NUMERIC::money::TEXT, \', \')  \
                                FROM json_each(et.prices) \
                            ) price_changes, \
                            et.application_fee::NUMERIC \
                        FROM event_ticket et \
                        WHERE et.event_id = e.event_id \
                    ) t \
                ) tickets \
            FROM "event" e \
            WHERE e.event_id = $1';
        Db.query(query, [$event_id]).then(function (result) {
            res.status(200).json({ event: _.first(result.rows) || {} });
        }).catch(err => {
            res.serverError(err);
        })
    },
    // put /api/admin/event/:event/tickets
    updateTicketsFee: async (req, res)=> {
        const $event_id= +req.params.event;
        const $tickets = req.body.tickets;
        const $event_app_fee= +req.body.application_fee;

        if(!$event_id) {
            return res.validation('Event id is required');
        }

        if(!$tickets) {
            return res.validation('Tickets required');
        }

        if( !($tickets instanceof Array) ) {
            return res.status(200).json({validation: 'Tickets should be an array'});
        }

        try {
            await AdminEventTicketsService.updateEventSWFeeForTickets($event_id, $event_app_fee);
            await AdminEventTicketsService.updateEventTicketsSWFee($event_id, $tickets);

            return res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    }
}
