'use strict';

const
    QRGenerator = require('../../../lib/QRTicketsGenerator');

const ticketValidationSchemas = require('../../../validation-schemas/tickets');

function isPaymentTypeChangeAllowed (p) {
    return (
        p.is_camps && 
        (p.type === 'check') && 
        (p.status === 'pending') && 
        !p.is_event_finished &&
         p.cards_available
    );
}

function generatePaymentsHashLinks (req, purchases) {
    return purchases.map(p => {
        let hash = QRGenerator.generateHash(p);

        if (hash) {
            p.receipt = QRGenerator.generateImageName(p.ticket_barcode, hash);

            if (isPaymentTypeChangeAllowed(p)) {
                p.typeChangeLink = SWTReceiptService.getTypeChangeLink(req, p.event_code, p.receipt);
            }
        }

        p.user_id       = undefined;
        p.event_id      = undefined;
        p.purchase_id   = undefined;
        p.event_code    = undefined;

        return p;
    });
}

module.exports = {
    // post /api/tickets/buy/:type
    buyTickets: function(req, res) {
        req.body.type   = req.params.type;
        req.body.event  = req.body.event ? parseInt(req.body.event, 10).toString() : req.body.event;

        req.body.hash   = req.headers['x-hash'] || null;

        if (req.body.method && (req.body.method ===  SWTPaymentsService.PENDING_PAYMENT_METHOD)) {
            return res.validation(`'Pending Payment' method is not allowed`);
        }

        const isAppleDevice = /iPad|iPhone|iPod|Macintosh/gm.test(req.get('User-Agent'));

        buyTicketsService.buy(req.body, null, isAppleDevice)
        .then(data => {
            const response = buyTicketsService.getPayment(data);

            res.status(200).json(response);
        })
        .catch(err => {
            let msg = err.validation || err.text || err.message;

            if (msg) {
                let responseObj = {};

                responseObj.validation = msg;

                if (err.type === 'receipt') {
                    responseObj.type = err.type;
                }

                const reqInfo = LoggingService.collectInfo(req);
                loggers.debug_log.debug(responseObj, reqInfo);

                res.status(400).json(responseObj);
            } else {
                /*
                * Stripe Error types are explained here:
                *           https://github.com/stripe/stripe-node/wiki/Error-Handling
                **/
                if (err.type !== 'StripeCardError') {
                    ErrorSender.ticketsPaymentError({
                        error: err,
                        request_body: req.body,
                        userAgent: req.headers['user-agent'],
                        uiHash: {
                            client: req.headers['ui-hash'],
                            current: staticHashService.getProjectHash('swt'),
                        },
                    });
                    const reqInfo = LoggingService.collectInfo(this.req);
                    loggers.errors_log.error(err, reqInfo);
                }
                res.serverError({ validation: 'Payment failed. Please, try again' });
            }
        })
    },

    //post /api/kiosk/buy
    buyTicketsInKiosk: function (req, res) {
        req.body.type   = 'tickets';
        req.body.event  = req.body.event ? parseInt(req.body.event, 10).toString() : req.body.event;

        req.body.hash               = req.headers['x-hash'] || null;
        req.body.validation_mode    = 'kiosk';

        buyTicketsService.buy(req.body, {source: 'api'}).then(data => {
            res.status(200).json(data);
        }).catch(res.customRespError.bind(res));
    },

    // get /api/tickets/user/purchases
    getUserPurchases: function(req, res) {
        let $user_id = req.user && req.user.user_id;

        let query =
            `SELECT  
                TO_CHAR(p.created, 'Mon DD, YYYY HH12:MI am') created, 
                p.ticket_barcode, 
                e.long_name "event_name",  
                e.name "short_event_name", 
                p.amount,  
                p.purchase_id, 
                p.event_id, 
                p.user_id,
                p.type,
                p.status,
                TO_CHAR(p.date_refunded, 'Mon DD, YYYY HH12:MI am') date_refunded, 
                (SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))) 
                    FROM( 
                        SELECT 
                            pt.quantity, et.label, et.short_label,
                            pt.ticket_fee "fee", ec.name "camp_name"
                        FROM purchase_ticket pt 
                        LEFT JOIN event_ticket et 
                            ON et.event_ticket_id = pt.event_ticket_id 
                        LEFT JOIN event_camp ec 
                            ON ec.event_camp_id = et.event_camp_id
                        WHERE  pt.purchase_id = p.purchase_id 
                    ) t 
                ) tickets,
                e.ticket_camps_registration "is_camps", 
                e."tickets_purchase_by_card" "cards_available",
                (e."date_end" < (NOW() AT TIME ZONE e."timezone")) "is_event_finished",
                e."event_tickets_code" "event_code",
                p.first,
                p.last
            FROM purchase p  
            INNER JOIN "event" e 
                ON e.event_id = p.event_id 
            INNER JOIN purchase pr
                ON pr.is_payment IS TRUE
                AND (pr.purchase_id = p.linked_purchase_id OR pr.purchase_id = p.purchase_id)
                AND (pr.kiosk IS NULL OR pr.status <> 'pending')
            WHERE (pr.user_id = $1)
                AND p.is_ticket = true
                AND p.payment_for='tickets'
            ORDER BY p.created DESC`;

        Db.query(query, [$user_id])
        .then(result => {
            let purchases = generatePaymentsHashLinks(req, result.rows);

            res.status(200).json({ purchases });
        }).catch(res.customRespError.bind(res))
    },
    // post /api/tickets/event/:event/invoice/:invoice/pay
    payWaitlist: function (req, res) {
        buyTicketsService.payInvoice(
            req.params.event,
            req.params.invoice, 
            req.body
        ).then(code => {
            res.status(200).json({ code })
        }).catch(err => {
            if(err instanceof Error) {
                res.customRespError({ validation: err.message });
            } else {
                res.customRespError(err);
            }
        })
    },
    // post /api/tickets/event/:event/change-type/:invoice
    changeTypeToCard: function (req, res) {
        let eventID     = Number(req.params.event);
        let invoiceID   = req.params.invoice;
        let payment     = req.body

        buyTicketsService.changeTypeToCard(eventID, invoiceID, payment)
        .then(() => {
            res.status(200).json({})
        })
        .catch(res.customRespError.bind(res));
    }
}
