'use strict';

module.exports = {
    // POST /api/tickets/events/:event/payment-discount
    getEventPaymentDiscount: function (req, res) {
        let eventCode   = req.params.event;
        let itemsQty    = Number(req.body.items);
        let amount      = Number(req.body.amount);

        if (!eventCode) {
            return res.validation('Event ID required');
        }

        if (!_.isNumber(itemsQty) || itemsQty < 0) {
            return res.validation('Invalid items quantity');
        }

        if (!_.isNumber(amount) || amount < 0) {
            return res.validation('Invalid amount');
        }


        SWTSettingsService.findEventPaymentDiscounts(eventCode)
        .then(paymentDiscounts => {
            if (_.isObject(paymentDiscounts)) {
                return SWTPaymentsService.findPaymentDiscount(paymentDiscounts, {
                    itemsQty,
                    amount
                });
            } else {
                return 0;
            }
        }).then(discount => {
            res.status(200).json({ discount });
        }).catch(res.customRespError.bind(res));
    }
}
