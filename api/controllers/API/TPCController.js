const swUtils = require("../../lib/swUtils");

module.exports = {
    // GET /api/tpc/export/:event/schedule
    schedule: async function(req, res) {
        try {
            const param = req.params.event;

            if (!param) {
                return res.send('No parameter passed');
            }

            const isNumeric = swUtils.isNumeric(param);

            const params = isNumeric
                ? { eventID: parseInt(param, 10) }
                : { clubPrivateRegCode: param };

            const schedule = await UAExportService.getSchedule(params);

            if (!schedule.length) {
                return res.status(200).send('No schedule found for the event');
            }

            res.status(200).json(schedule);
        } catch (error) {
            res.send(error);
        }
    }
};
