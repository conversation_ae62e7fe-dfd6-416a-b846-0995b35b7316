

module.exports = {
    //GET /api/event-checkin/type/:type/event/:event/scan/:barcode
    scan: async (req, res) => {
        const {
            type,
            event,
            barcode
        } = req.params;

        try {
            if(!type) {
                throw { validation: 'Checkin type required' };
            }

            if(!event) {
                throw { validation: 'Event ID required' };
            }

            if(!barcode) {
                throw { validation: 'Scan barcode required' };
            }

            const official = await EventCheckinAPIService.scan(type, event, barcode);

            res.status(200).json({ success: true, official });
        } catch (err) {
            errorHandler(err, res);
        }
    },

    //GET /api/event-checkin/type/:type/events
    events: async (req, res) => {
        const { type } = req.params;

        try {
            if(!type) {
                throw { validation: 'Checkin type required' };
            }

            const events = await EventCheckinAPIService.getEvents(type);

            res.status(200).json({ success: true, events });
        } catch (err) {
            errorHandler(err, res);
        }
    }
}

function errorHandler (err, res) {
    let code = 500;
    let message = 'Internal Error';

    if(err.validation) {
        code = 400;
        message = err.validation;
    } else {
        //Log issue if it is not caused by validation
        loggers.errors_log.error(err);
    }

    res.status(code).json({ success: false, message });
}
