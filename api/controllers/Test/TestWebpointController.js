'use strict';

module.exports = {
    // /api/admin/webpoint3/member/:usav/find
    getMember: function (req, res) {
        let member = req.params.usav;

        WebpointDataReceiver.v2.getMember(member)
            .then(member => {
                res.status(200).json({ member })
            }).catch(err => {
            res.customRespError(err);
        })
    },

    // /api/admin/webpoint3/clubmbr/:username/:password/find
    getClub: function (req, res) {
        let usrName     = req.params.username;
        let password    = req.params.password;

        WebpointDataReceiver.v2.getClubMembers(usrName, password)
            .then(club => {
                res.status(200).json({ club })
            }).catch(err => {
                res.customRespError(err);
            })
    }
};
