'use strict';

module.exports = {
	// get /api/test/sess
	testAll: function (req, res) {
		RedisService.getKey('sess*', function (err, rows) {
			if (err) {
				res.customRespError(err);
			} else {
				res.status(200).json(rows);
			}
		});


		// req.sessionID, 
		// sails.config.session.store.get(req.sessionID, function (err, session) {
		// 	if (err) {
		// 		res.customRespError(err);
		// 	} else {
		// 		res.status(200).json(session);
		// 	}
		// })
		
	}
};