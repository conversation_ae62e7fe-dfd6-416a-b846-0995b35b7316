const { ENTRY_STATUSES } = require('../../constants/teams');

module.exports = {
    friendlyName: 'Check event team data',
    description: 'Check if event and team code are existed and team is not declined',
    
    inputs: {
        eventId: {
            type: 'number',
            example: 23016,
            description: 'Event ID',
            required: true
        },
        teamCode: {
            type: 'string',
            example: 'G10HAPPY10CA',
            description: 'Team organization code',
            required: false
        }
    },

    fn: async function(inputs) {
        const eventId = inputs.eventId;
        const teamCode = inputs.teamCode;

        let eventTeam = await _getEventTeam(eventId, teamCode);
        
        if(_.isEmpty(eventTeam)) {
            return 'Event not found';
        }
        if(teamCode) {
            if(eventTeam.team_not_found) {
                return 'Invalid Team Code. Contact your coach to verify';
            }
            if(eventTeam.team_is_declined) {
                return 'Team declined';
            }
        }
    }
};

function _getEventTeam (eventId, teamCode) {
    const query = knex('event as e').select(1).where('e.event_id', eventId);

    if(teamCode) {
        query
            .select({
                team_is_declined: knex.raw(`rt.status_entry = ?`, [ENTRY_STATUSES.DECLINED]),
                team_not_found: knex.raw('rt.roster_team_id IS NULL OR rt.deleted IS NOT NULL')
            })
            .leftJoin('roster_team as rt', (join) => {
                join.on('rt.event_id', 'e.event_id')
                    .andOn(knex.raw(`LOWER(organization_code) = LOWER(?)`, teamCode))
            })
    }

    return Db.query(query).then(result => result?.rows?.[0] || null);
}
