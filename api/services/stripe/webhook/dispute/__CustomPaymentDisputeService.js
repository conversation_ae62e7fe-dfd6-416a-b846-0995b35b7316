
const swUtils = require('../../../../lib/swUtils');

class CustomPaymentDisputeService {
    constructor(parentService) {
        this.parentService = parentService;
    }

    async process (dispute, purchase, disputeType) {
        if(_.isEmpty(dispute)) {
            throw new Error('Dispute object is empty');
        }

        if(_.isEmpty(purchase)) {
            throw new Error('Purchase object is empty');
        }

        let tr;

        try {
            tr = await Db.begin();

            let actions = [];

            if(this.parentService.DISPUTE.CREATED === disputeType) {
                //Create stripe_dispute row
                actions.push(this.__createStripeDisputeRow(tr, dispute));

                //Set stripe_payment_intent.stripe_dispute_id = dispute.id
                actions.push(this.__updateStripeCustomPaymentRow(tr, dispute.charge, dispute.id));
            } else{
                //Update stripe_dispute status
                actions.push(this.__updateStripeDisputeRow(tr, dispute));
            }

            if(purchase.status !== 'disputed') {
                //Set purchase row status = 'disputed'
                actions.push(this.__updateCustomPaymentRow(tr, purchase.purchase_id));
            }

            await Promise.all(actions);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err;
        }
    }

    getPurchaseData (purchaseID) {
        let query = knex('custom_payment AS cp')
            .select(
                'cp.event_id', 'cp.payment_for', 'cp.amount', 'cp.custom_payment_id AS purchase_id', 'e.email',
                'e.email AS user_email', 'spi.stripe_card_fingerprint AS fingerprint',
            )
            .join('stripe_payment_intent AS spi', 'spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
            .join('event AS e', 'e.event_id', 'cp.event_id')
            .where('cp.custom_payment_id', purchaseID);

        return Db.query(query).then(result => result && result.rows[0] || null);
    }

    __updateCustomPaymentRow (tr, purchaseID) {
        if(!purchaseID) {
            throw new Error('Purchase ID required');
        }

        let query = knex('custom_payment AS cp')
            .update({ status: 'disputed' })
            .where('custom_payment_id', purchaseID);

        return tr.query(query).then(result => result && result.rowCount > 0)
            .then(updated => {
                if(!updated) {
                    throw new Error('Payment row status not updated');
                }
            })
    }

    __createStripeDisputeRow (tr, dispute) {
        if(!dispute.id) {
            throw new Error('Dispute ID required');
        }

        if(!dispute.amount) {
            throw new Error('Dispute amount required');
        }

        if(!dispute.status) {
            throw new Error('Dispute status required');
        }

        if(!dispute.charge) {
            throw new Error('Dispute charge required');
        }

        let preparedDisputeData = {
            stripe_dispute_id: dispute.id,
            amount: swUtils.normalizeNumber(Number(dispute.amount) / 100),
            status: dispute.status,
            stripe_charge_id: dispute.charge
        }

        let query = knex('stripe_dispute').insert(preparedDisputeData);

        return tr.query(query).then(result => result && result.rowCount > 0)
            .then(inserted => {
                if(!inserted) {
                    throw new Error('Dispute row not created');
                }
            })
    }

    __updateStripeDisputeRow (tr, dispute) {
        if(!dispute.id) {
            throw new Error('Dispute ID required');
        }

        if(!dispute.status) {
            throw new Error('Dispute status required');
        }

        let disputeID = dispute.id;
        let disputeStatus = dispute.status;

        let query = knex('stripe_dispute AS sd')
            .update({ status: disputeStatus })
            .where('sd.stripe_dispute_id', disputeID);

        return tr.query(query).then(result => result && result.rowCount > 0);
    }

    __updateStripeCustomPaymentRow (tr, stripeChargeID, disputeID) {
        if(!stripeChargeID) {
            throw new Error('Stripe Charge ID required');
        }

        if(!disputeID) {
            throw new Error('Dispute ID required');
        }

        let query = knex('stripe_payment_intent AS spi')
            .update({ stripe_dispute_id: disputeID })
            .where('spi.stripe_charge_id', stripeChargeID);

        return tr.query(query).then(result => result && result.rowCount > 0)
            .then(updated => {
                if(!updated) {
                    throw new Error('Stripe payment row row not updated');
                }
            })
    }
}

module.exports = CustomPaymentDisputeService;
