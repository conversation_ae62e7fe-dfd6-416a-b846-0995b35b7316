'use strict';

const Stripe    = require('stripe');
const swUtils   = require('../../lib/swUtils');
const moment    = require('moment');

const USD = 'usd';
const DEFAULT_API_VER = sails.config.stripe_api.version;

const Queue                  = require('bull');
const AutoPayoutProcessQueue = new Queue('auto-payouts-process-job', sails.config.redis_queue.auto_payout_process);

/**
 * https://stripe.com/docs/upgrades#2017-04-06
 *
 * Payouts were moved out of Transfers into their own resource.
 * /v1/payouts now represents moving money from a Stripe account to a bank account
 * or debit card, and /v1/transfers now represents Connect platforms moving money between
 * Stripe accounts. For more details, see https://stripe.com/docs/transfer-payout-split.
 *
 * NOTE: SW api ver. is greater than this
 */
const PAYOUTS_CREATION_API_VER_DATE = moment.utc('2017-04-06', 'YYYY-MM-DD');

function StripePayoutsService () {}

StripePayoutsService.prototype.createPayout = function (platformSK, destinationAccID, amount) {
    return Promise.resolve().then(() => {
        if (!destinationAccID) {
            return Promise.reject(new Error('Destination Account missing!'));
        }

        if (!_.isNumber(amount) || amount <= 0) {
            return Promise.reject(new Error('Invalid Amount!'));
        }

        let PlatformStripe = Stripe(platformSK, { apiVersion: DEFAULT_API_VER, maxNetworkRetries: 2 });

        // TODO: maybe need to add saving to "stripe_transfer" table here

        return PlatformStripe.transfers.create({
          amount        : amount * 100,
          currency      : USD,
          destination   : destinationAccID,
        });
    })
};

StripePayoutsService.prototype.cancelPayout = function (accountID, transferID) {
    if(!accountID) {
        throw new Error('Account ID required');
    }

    if(!transferID) {
        throw new Error('Transfer ID required');
    }

    let query = squel.update().table('stripe_transfer')
        .set('date_canceled', 'NOW()', { dontQuote: true })
        .where('stripe_account_id = ?'  , accountID)
        .where('stripe_transfer_id = ?' , transferID);

    return Db.query(query).then(result => result.rowCount > 0);
};

StripePayoutsService.prototype.createLoginLink = function(platformSK, connectedAccountID) {
        let PlatformStripe = Stripe(platformSK, { apiVersion: DEFAULT_API_VER, maxNetworkRetries: 2 });

        return PlatformStripe.accounts.createLoginLink(connectedAccountID);
};

/**
 * Processes a Stripe Payout (ex. Stripe Transfer to a bank account), namely:
 * 1. Finds secret key for specified account id in our database
 * 2. Checks, whether passed payout is manual or auto
 * 3. Saves "stripe_transfer" row if it does not exit
 **/
StripePayoutsService.prototype.processStripePayout = async function (stripeAccountID, transferData, apiVer) {
    if (!_.isString(stripeAccountID) || (stripeAccountID.length === 0)) {
        throw new Error('Expecting Stripe Account ID to be a non-empty string');
    }

    if (!_.isObject(transferData) || _.isEmpty(transferData)) {
        throw new Error('Expecting transfer to be an object');
    }

    let stripeTransferID = transferData.id;

    if (!_.isString(stripeTransferID) || (stripeTransferID.length === 0)) {
        throw new Error('Expecting Stripe Transfer ID to be a non-empty string');
    }

    let transferType    = (transferData.automatic) ? 'auto' : 'pay_out';

    let dateSent        = Number.isInteger(transferData.arrival_date)
        ? moment(transferData.arrival_date * 1000).format('YYYY-MM-DD')
        : (void 0);
    let amount          = Number(transferData.amount) || 0;

    let reversedAmount  = Number(transferData.amount_reversed) || 0;
    amount              = swUtils.normalizeNumber((amount - reversedAmount) / 100);

    let alreadyHasRecord = await (FundsTransferService._saveStripeTransferRow({
        amount,
        stripe_account_id   : stripeAccountID,
        stripe_transfer_id  : stripeTransferID,
        description         : transferData.description,
        transfer_type       : transferType,
        date_sent           : dateSent
    }).catch(err => {
        /*
        * Do not crash if duplicate transfer row insertion attempt
        * https://www.postgresql.org/docs/current/static/errcodes-appendix.html
        * 23505 - unique_violation
        */
        if (err.code !== '23505') {
            throw err;
        } else {
            return true;
        }
    }));

    if(transferType === 'auto') {
        return AutoPayoutProcessQueue.add({
            account_id      : stripeAccountID,
            payout_id       : stripeTransferID,
            payout_created  : transferData.arrival_date,
            apiVer
        });
    }

    if ((transferType !== 'auto') && !alreadyHasRecord) {
        ErrorSender.transferWOEventID({
            error           : new Error('Stripe Transfer without "event_id"'),
            stripeEventObj  : transferData
        })
    }
};

StripePayoutsService.closeQueue = async function() {
    return AutoPayoutProcessQueue.close();
};

module.exports = StripePayoutsService;
