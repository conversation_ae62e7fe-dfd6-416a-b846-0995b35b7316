class LoggingService {
    collectInfo(req) {
        if(!_.isObject(req)) {
            return undefined;
        }
        const {pm_id, name} = process.env;
        const appId = pm_id && name && `${name} #${pm_id}`;
        return {
            method: req.method,
            url: req.url,
            body: this.clearSensitiveData(req.body),
            headers: {
                host: req.get('Host'),
                contentType: req.get('Content-Type'),
                userAgent: req.get('User-Agent'),
                referer: req.get('Referer'),
            },
            user: req.user && ({
                user_id: req.user.user_id,
                first: req.user.first,
                last: req.user.last,
                event_owner_id: req.user.event_owner_id,
                official_id: req.user.official_id,
                master_club_id: req.user.master_club_id,
                club_owner_id: req.user.club_owner_id,
            }),
            appId,
        };
    }

    clearSensitiveData(obj) {
        if(!_.isObject(obj)) {
            return obj;
        }
        const result = {...obj};
        for(const key of Object.getOwnPropertyNames(result)) {
            if(/(password|token)/i.test(key)) {
                result[key] = '***';
            }
            if(_.isObject(result[key])) {
                result[key] = this.clearSensitiveData(result[key])
            }
            else if(Array.isArray(result[key])) {
                result[key] = result[key].map((v) => this.clearSensitiveData(v));
            }
        }

        return result;
    }
}

module.exports = new LoggingService();
