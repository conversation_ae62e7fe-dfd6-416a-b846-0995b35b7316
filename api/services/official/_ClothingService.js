'use strict';

class ClothingService {
    constructor () {}

    get SIZE_TYPES () {
        return {
            INTERNATIONAL: 'international',
            SHOES_INCHES: 'shoes_inches',
            HAT_INCHES: 'hat_inches',
        };
    }

    get SIZE_VALUES () {
        return {
            [this.SIZE_TYPES.INTERNATIONAL]: [
                'XS',
                'S',
                'M',
                'L',
                'XL',
                'XXL',
                'XXXL',
                'XXXXL',
            ],
            [this.SIZE_TYPES.SHOES_INCHES]: [
                '5',
                '5.5',
                '6',
                '6.5',
                '7',
                '7.5',
                '8',
                '8.5',
                '9',
                '9.5',
                '10',
                '10.5',
                '11',
                '11.5',
                '12',
                '12.5',
                '13',
                '14',
                '15',
                '16',
            ],
            [this.SIZE_TYPES.HAT_INCHES]: [
                '6 5/8',
                '6 3/4',
                '6 7/8',
                '7',
                '7 1/8',
                '7 1/4',
                '7 3/8',
                '7 1/2',
                '7 5/8',
                '7 3/4',
                '7 7/8',
                '8',
            ],
        };
    }


    async findSizesForOfficials (members, eventId, role) {
        if(!(members instanceof Array) || !eventId || !role) {
            throw new Error('Invalid parameters');
        }

        if(_.isEmpty(members)) {
            throw new Error('Event has no entries');
        }

        const memberIds = members.filter(m => !!m && !!m.official_id).map(m => m.official_id);

        const [
            eventClothes,
            officialsClothesSizes,
            totalsResult
        ] = await Promise.all([
            (async () => {
                const { rows: eventClothes } = await Db.query(
                    squel.select()
                        .from('event_clothes', 'ec')
                        .join('common_item', 'ci', 'ci.common_item_id = ec.common_item_id')
                        .where('ec.event_id = ? AND ec.member_type = ?', eventId, role)
                        .fields(['ec.gender', 'ci.common_item_id', 'ci.title'])
                        .field(`ci.details->'size_type'`, 'size_type')
                        .order(`ci.details->'order'`)
                        .toParam()
                );
                let result = {
                    male: [],
                    female: [],
                };
                eventClothes.forEach(ec => {
                    result[ec.gender].push(_.omit(ec,'gender'));
                });
                return result;
            })(),
            (async () => {
                const { rows: clothesSizes } = await Db.query(`
                    with size_sub as (
                        SELECT
                            sub_oecs."size",
                            sub_ec.common_item_id,
                            sub_ec."gender" as gender_size,
                            sub_oecs.official_id as official_id
                        FROM
                            official_event_clothes_size sub_oecs 
                        LEFT JOIN
                            event_clothes sub_ec ON sub_ec.event_clothes_id = sub_oecs.event_clothes_id
                        WHERE
                            sub_oecs.event_id = $1
                            AND sub_oecs.official_id IN (${memberIds.join(', ')})
                            AND sub_ec.member_type = $2
                        GROUP BY
                            sub_ec.common_item_id, sub_oecs."size", sub_ec."gender", sub_oecs.official_id
                    )
                    SELECT
                        "of"."official_id",
                        COALESCE(json_object_agg(ec.common_item_id, coalesce(oecs."size", ocs."size")), '{}'::JSON) AS sizes,
                        COALESCE(json_object_agg(ec.common_item_id, coalesce(oecs.gender_size, u.gender::text)), '{}'::JSON) AS gender_sizes
                    FROM
                        official of
                    JOIN
                        "user" u ON u.user_id = of.user_id
                    JOIN
                        official_clothes_size ocs ON ocs.official_id = of.official_id
                    JOIN
                        event_clothes ec ON ec.common_item_id = ocs.common_item_id AND ec.deleted IS NULL
                    LEFT JOIN 
                      size_sub oecs ON oecs.common_item_id = ec.common_item_id 
                      and oecs.official_id = of.official_id
                    WHERE
                        ec.event_id = $1
                        AND ec.member_type = $2
                        AND of.official_id IN (${memberIds.join(', ')})
                    GROUP BY
                        of.official_id
                `, [eventId, role]);

                if (!clothesSizes?.length) {
                    throw { validation: `${role} clothes size feature is not used for the event`};
                }

                let result = new Map();
                clothesSizes.forEach(({ official_id, sizes, gender_sizes }) => {
                    result.set(official_id, {sizes, gender_sizes});
                });
                return result;
            })(),
            (async () => {
                const { rows: result } = await Db.query(`
                    SELECT
                    q.gender,
                    q.common_item_id,
                    q."size",
                    COUNT(q."size")::INTEGER AS count
                FROM
                    (
                        SELECT
                            ec.gender,
                            ec.common_item_id,
                            COALESCE(oecs."size", ocs."size") AS "size"
                        FROM
                            official of
                        JOIN
                            "user" u ON u.user_id = of.user_id
                        JOIN
                            official_clothes_size ocs ON ocs.official_id = of.official_id
                        LEFT JOIN
                            official_event_clothes_size oecs ON oecs.official_id = of.official_id
                            AND oecs.event_id = $1
                        JOIN
                            event_clothes ec ON ec.common_item_id = ocs.common_item_id
                            AND (
                                oecs.event_clothes_id = ec.event_clothes_id
                                OR (oecs.event_clothes_id IS NULL AND ec.gender = u.gender::text)
                            )
                        WHERE
                            ec.event_id = $1
                            AND ec.member_type = $2
                            AND of.official_id IN (${memberIds.join(', ')} )
                        GROUP BY
                            ec.gender,
                            ec.common_item_id,
                            ocs.size,
                            oecs.size
                    ) AS q
                GROUP BY 
                    q.common_item_id,
                    q.gender,
                    q.size;

            `, [eventId, role]);
                let totals = {};
                return result.reduce((r, v) => {
                    if(!r[v.gender]) {
                        r[v.gender] = {};
                    }
                    if(!r[v.gender][v.common_item_id]) {
                        r[v.gender][v.common_item_id] = {};
                    }
                    r[v.gender][v.common_item_id][v.size] = v.count;
                    return r;
                }, {});
            })()
        ]);

        const officialGroups = ['male', 'female'].reduce((r, gender) => {
            const genderMembers = members.filter(m=>m.gender === gender);
            const officials = genderMembers.map(m => {
                const {sizes: officialSizes, gender_sizes: officialGenderSizes} = officialsClothesSizes.get(m.official_id);

                const sizes = eventClothes[m.gender].reduce((r, v) => {
                    r[v.common_item_id] = officialSizes && officialSizes[v.common_item_id] || null;
                    // show tips like (Women's or Mans ) in case user gender and item gender are different
                    if (officialGenderSizes?.[v.common_item_id] !== m.gender) {
                        r[v.common_item_id] = this.addAliasForSize(r[v.common_item_id], officialGenderSizes[v.common_item_id]);
                    }

                    return r;
                }, {});

                return {...m, ...sizes};
            });
            const totals = eventClothes[gender].reduce(
                (r, {common_item_id, size_type}) => {
                    const sizeValues = this.SIZE_VALUES[size_type];
                    let sizes = [];
                    if(totalsResult[gender] && totalsResult[gender][common_item_id]) {
                        sizes = Object.keys(totalsResult[gender][common_item_id])
                            .sort((a, b) => sizeValues.indexOf(a) - sizeValues.indexOf(b));
                    }
                    r[common_item_id] = sizes.map(size => (
                            {
                                size,
                                count: totalsResult[gender][common_item_id][size],
                            }
                        )
                    );
                    return r;
                },
                {}
            );
            r[gender] = {
                eventClothes: eventClothes[gender],
                officials,
                totals,
            };
            return r;
        }, {});

        return {
            officialGroups,
        };
    }

    addAliasForSize(size, gender) {
        const aliases = {
            male: `Mens`,
            female: `Women's`,
        }

        return `${aliases[gender]} - ${size}`;
    }

    async getMemberClothesForEvent(memberId, eventId, memberType) {
        const query =  knex('official_event_clothes_size as oes')
            .innerJoin('event_clothes as ec', 'ec.event_clothes_id', 'oes.event_clothes_id')
            .innerJoin('common_item as ci', 'ec.common_item_id', 'ci.common_item_id')
            .select('gender', 'size', 'ci.title')
            .where({
                'official_id': memberId,
                'oes.event_id': eventId,
                'ec.member_type': memberType
            });

        const { rows } = await  Db.query(query);
        return rows.map(({title, size, gender}) => ({
            title,
            size: this.addAliasForSize(size,  gender)
        }));
    }

    async getMemberClothesForEventOld(memberId, eventId, memberType) {
        const sql = `
                SELECT ocs.size, ci.title
                FROM "event_clothes" ec
                LEFT JOIN official_clothes_size  ocs
                    ON ocs.common_item_id = ec.common_item_id
                    AND ocs.official_id = $1
                LEFT JOIN common_item ci
                    ON ci.common_item_id = ec.common_item_id
                LEFT JOIN "official" o
                    ON o.official_id = $1
                LEFT JOIN "user" u
                    ON u.user_id = o.user_id
                WHERE ec.event_id = $2
                AND ec.deleted IS NULL
                AND u.gender::TEXT = ec.gender::TEXT
                AND ci.item_type = 'event_clothes'
                AND ec.member_type = $3
                ORDER BY ci.details::jsonb->>'order'
        `;

        const { rows } = await Db.query(sql, [memberId, eventId, memberType]);
        return rows;
    }

    async getMemberClothes(memberId, eventId, memberType) {
        const allowMemberTypes = ['official', 'staff'];

        if (!allowMemberTypes.includes(memberType)) {
            throw { validation: 'Invalid member type'};
        }

        const result = await this.getMemberClothesForEvent(memberId, eventId, memberType);

        if (result?.length) {
            return result;
        }

        return this.getMemberClothesForEventOld(memberId, eventId, memberType);
    }
}

module.exports = new ClothingService();
