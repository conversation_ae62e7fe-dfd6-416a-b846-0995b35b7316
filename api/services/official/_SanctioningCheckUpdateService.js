'use strict';

const {SANCTIONING_CHECK_FIELDS} = require('../../constants/event-official');

class SanctioningCheckUpdateService {

    async setOk (data, isHeadOfficial, eventID, eventOfficialID) {
        let query = squel.update().table('event_official', 'eo')
            .where('eo.event_official_id = ?', eventOfficialID)
            .where('eo.event_id = ?', eventID)
            .returning('eo.event_official_id');

        let manualOkMessage = `Set OK by ${isHeadOfficial ? 'HO' : 'EO'} on ${data.date}`;

        if(data.field === SANCTIONING_CHECK_FIELDS.SAFESPORT) {
            query.set('safesport_manual_ok', manualOkMessage)
                .returning('eo.safesport_manual_ok');

        } else if(data.field === SANCTIONING_CHECK_FIELDS.BACKGROUND) {
            query.set('bg_manual_ok', manualOkMessage)
                .returning('eo.bg_manual_ok');

        } else if(data.field === SANCTIONING_CHECK_FIELDS.AAU_SAFESPORT) {
            query.set('aau_safesport_manual_ok', manualOkMessage)
                .returning('eo.aau_safesport_manual_ok');

        } else if(data.field === SANCTIONING_CHECK_FIELDS.AAU_BACKGROUND) {
            query.set('aau_bg_manual_ok', manualOkMessage)
                .returning('eo.aau_bg_manual_ok');

        } else {
            return Promise.reject({validation: 'Field is not valid'});
        }

        return Db.query(query).then(result => result.rows[0] || null);
    }
}

module.exports = new SanctioningCheckUpdateService();
