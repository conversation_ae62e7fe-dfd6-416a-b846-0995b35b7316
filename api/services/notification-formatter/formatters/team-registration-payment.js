
const {
    R<PERSON><PERSON><PERSON>ATION_PAID_BY_CARD,
    REGISTRATION_PAYMENT_TYPE_CHANGED_FROM_CHECK_TO_CARD,
    REGISTRATION_PAYMENT_MAID_BY_CARD_REFUNDED,
    R<PERSON><PERSON><PERSON>ATION_PAID_BY_CHECK,
    REG<PERSON><PERSON><PERSON><PERSON>_PAID_BY_CHECK_CANCELED,
    REGISTRATION_PAID_BY_CHECK_PENDING,
    REGISTRATION_PAID_BY_ACH_PAID,
    REGISTRATION_PAID_BY_ACH_PENDING,
    REGISTRATION_PAID_BY_ACH_CANCELED,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_PAYMENT_REFUNDED,
    R<PERSON>IS<PERSON>ATION_PAYMENT_REFUNDED_PARTIALLY,
    R<PERSON>ISTRATION_PAYMENT_CANCELLED,
    TEAM_PAYMENT_STATUS_CHANGED_TO_PAID_BY_EO,
    TEAM_PAYMENT_STATUS_CHANGED_TO_NOT_PAID_BY_EO,
    TEAM_PAYMENT_REFUND_FULL,
    TEAM_PAYMENT_REFUND_PARTIAL,
    REGIS<PERSON>AT<PERSON>_PAID_BY_CHECK_PARTIALLY_CANCELED,
} = require('../../../constants/notification-actions');

module.exports = {
    [REGISTRATION_PAID_BY_CARD]:
        (n) => ({
            title: `Club has paid by card for ${n.pt_count} team(s) for a total of $${n.amount}. Teams: ${
                __getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAYMENT_TYPE_CHANGED_FROM_CHECK_TO_CARD]:
        (n) => ({
            title: `Club has paid by card after change from check for ${n.pt_count} team(s) for a total of $${
                n.amount}. Teams: ${__getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAYMENT_MAID_BY_CARD_REFUNDED]:
        (n) => ({
            title: `Club has refunded by card for ${n.pt_count} team(s) for a total of $${n.amount}. Teams: ${
                __getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAID_BY_CHECK]:
        (n) => ({
            title: `Check #${(n.check_num || n.purchase_id)} for amount $${n.amount} has been received for ${
                n.pt_count} team(s): ${__getTeamsNames(n.pteams)}`
        }),
    [REGISTRATION_PAID_BY_CHECK_CANCELED]:
        (n) => ({
            title: `Invoice #${n.purchase_id} for amount $${n.amount} has been canceled by ${n.username} for ${
                n.pt_count} team(s): ${__getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAID_BY_CHECK_PENDING]:
        (n) => ({
            title: `Invoice #${n.purchase_id} for amount $${n.amount} has been created by ${n.username} for ${
                n.pt_count} team(s): ${__getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAID_BY_ACH_PAID]:
        (n) => ({
            title:  `Club has paid by ACH for ${n.pt_count} team(s) for a total of $${n.amount}. Teams: ${
                __getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAID_BY_ACH_PENDING]:
        (n) => ({
            title: `Club has pending by ACH for ${n.pt_count} team(s) for a total of $${n.amount}. Teams: ${
                __getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAID_BY_ACH_CANCELED]:
        (n) => ({
            title: `Club has canceled by ACH for ${n.pt_count} team(s) for a total of $${n.amount}. Teams: ${
                __getTeamsNames(n.pteams)}`,
        }),
    [REGISTRATION_PAYMENT_REFUNDED]:
        (n) => ({
            title: `Payment Refunded`,
        }),
    [REGISTRATION_PAYMENT_REFUNDED_PARTIALLY]:
        (n) => ({
            title: `Payment Partially Refunded`,
        }),
    [REGISTRATION_PAYMENT_CANCELLED]:
        (n) => ({
            title: `Payment (Check) Canceled`,
        }),
    [TEAM_PAYMENT_STATUS_CHANGED_TO_PAID_BY_EO]:
        (n) => ({
            title: `Team '${n.team_name}' paid status changed to "Paid" by event`,
            comments: n.comments
        }),
    [TEAM_PAYMENT_STATUS_CHANGED_TO_NOT_PAID_BY_EO]:
        (n) => ({
            title: `Team '${n.team_name}' paid status changed to "Not Paid" by event`,
            comments: n.comments
        }),
    [TEAM_PAYMENT_REFUND_FULL]:
        (n) => ({
            title: `Fully Refunded by "${n.username}"`,
        }),
    [TEAM_PAYMENT_REFUND_PARTIAL]:
        (n) => ({
            title: `Partially Refunded by "${n.username}" Amount of Refund $${n.amount}`,
        }),
    [REGISTRATION_PAID_BY_CHECK_PARTIALLY_CANCELED]:
        (n) => ({
            title: `Check Partially Canceled by "${n.username}"`,
        })
}

function __getTeamsNames (teams) {
    if(!Array.isArray(teams) || !teams.length) {
        return '';
    }

    return teams.map(t => t.team_name).join(', ');
}
