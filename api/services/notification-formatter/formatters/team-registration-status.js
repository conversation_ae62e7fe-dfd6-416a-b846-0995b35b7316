const { ageFormatterForTeamRegistration } = require('./helpers');

const {
    TEAM_STATUS_CHANGED_TO_PENDING,
    TEAM_STATUS_CHANGED_TO_ACCEPTED,
    TEAM_STATUS_CHANGED_TO_DECLINED,
    TEAM_STATUS_CHANGED_TO_WAITING,
    TEAM_ENTERED_ON_EVENT,
    TEAM_DELETED_FROM_EVENT,
    TEAM_CHANGED_DIVISION,
    TEAM_CHANGED_GENDER,
    TEAM_CHANGED_NAME,
    TEAM_CHANGED_AGE,
    TEAM_CHANGED_RANK,
    TEAM_ROSTER_CHANGED,
} = require('../../../constants/notification-actions');

module.exports = {
    [TEAM_STATUS_CHANGED_TO_PENDING]:
        (n) => ({
            title: `Team ${n.team_name} has been PENDING.`,
            comments: n.comments
        }),
    [TEAM_STATUS_CHANGED_TO_ACCEPTED]:
        (n) => ({
            title: `Team ${n.team_name} has been ACCEPTED.`,
            comments: n.comments
        }),
    [TEAM_STATUS_CHANGED_TO_DECLINED]:
        (n) => ({
            title: `Team ${n.team_name} has been DECLINED.`,
            comments: n.comments
        }),
    [TEAM_STATUS_CHANGED_TO_WAITING]:
        (n) => ({
            title: `Team ${n.team_name} has been WAITING.`,
            comments: n.comments
        }),
    [TEAM_ENTERED_ON_EVENT]:
        (n) => ({
            title: `Team ${n.team_name} has been entered into division ${n.division_name}.`
        }),
    [TEAM_DELETED_FROM_EVENT]:
        (n) => ({
            title: `Team ${n.team_name} has been removed from the event.`,
        }),
    [TEAM_CHANGED_DIVISION]:
        (n) => ({
            title: `Team ${n.team_name} changed division to ${n.division_name}.`,
        }),
    [TEAM_CHANGED_GENDER]:
        (n) => ({
            title: `Changed Gender of team ${n.team_name} (${_.capitalize(n.old_data.gender)} → ${_.capitalize(n.new_data.gender)}) By ${n.first} ${n.last}.`,
        }),
    [TEAM_CHANGED_NAME]:
        (n) => ({
            title: `Changed Name of team ${n.team_name} (${n.old_data.team_name} → ${n.new_data.team_name}) By ${n.first} ${n.last}.`,
        }),
    [TEAM_CHANGED_AGE]:
        (n) => ({
            title: `Changed Age of team ${n.team_name} (${ageFormatterForTeamRegistration(n.old_data.age)} → ${ageFormatterForTeamRegistration(n.new_data.age)}) By ${n.first} ${n.last}.`,
        }),
    [TEAM_CHANGED_RANK]:
        (n) => ({
            title: `Changed Rank of team ${n.team_name} (${n.old_data.rank} → ${n.new_data.rank}) By ${n.first} ${n.last}.`,
        }),
    [TEAM_ROSTER_CHANGED]: (n) => ({
        title: `Team Roster was changed.`,
        comments: n.comments
    }),
}
