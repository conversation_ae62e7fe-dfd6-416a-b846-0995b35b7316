'use strict';

const redis = require('redis');

class BulkLogger {
    get KEY_NAME() { return sails.config.bulkLogging && sails.config.bulkLogging.keyName; }
    get TYPES() {
        return {
            REQUEST: 'request',
            RESPONSE: 'response',
        };
    }

    constructor() {
        this.canLog = false;
        if(!this.isLoggingEnabled()) {
            return;
        }
        const redisConfig = sails.config.bulkLogging.redis;
        this.client = redis.createClient({
            url: sails.config.connections.redis,
            enable_offline_queue: false,
            retry_strategy: function(options) {
                if (options.error && options.error.code === "ECONNREFUSED") {
                    // End reconnecting on a specific error and flush all commands with
                    // a individual error
                    return new Error("The server refused the connection");
                }
                if (options.total_retry_time > 60000) {
                    // End reconnecting after a specific timeout and flush all commands
                    // with a individual error
                    return new Error("Retry time exhausted");
                }
                if (options.attempt > 10) {
                    // End reconnecting with built in error
                    return undefined;
                }
                // reconnect after
                return Math.min(options.attempt * 100, 3000);
            },
        });
        this.client.on('ready', () => {
            this.client.select(redisConfig.database, () => {
                this.canLog = true;
            });
        });
        // "error" events get turned into exceptions if they aren't listened for.
        this.client.on('error', () => {});
    }

    isLoggingEnabled(type = undefined) {
        if(
            !_.isObject(sails.config.bulkLogging)
            || !_.isString(sails.config.bulkLogging.keyName)
            || !_.isObject(sails.config.bulkLogging.redis)
            || !_.isNumber(sails.config.bulkLogging.redis.port)
            || !_.isString(sails.config.bulkLogging.redis.host)
            || !_.isNumber(sails.config.bulkLogging.redis.database)
        ) {
            return false;
        }
        if(type) {
            return sails.config.bulkLogging.enabled === true
                || (
                    Array.isArray(sails.config.bulkLogging.enabled)
                    && sails.config.bulkLogging.enabled.includes(type)
                )
        }
        else {
            return sails.config.bulkLogging.enabled === true
                || (
                    Array.isArray(sails.config.bulkLogging.enabled)
                    && sails.config.bulkLogging.enabled.length > 0
                )
        }
    }

    logData(type, data) {
        if(!this.canLog) {
            return false;
        }
        if(!this.isLoggingEnabled()) {
            return;
        }
        const message = {
            type,
            timestamp: Date.now(),
            instance: this._getInstanceId(),
            data,
        };
        this.client.lpush(this.KEY_NAME, JSON.stringify(message), (err) => null);
    }

    logRequest(request_id, req) {
        const messageType = this.TYPES.REQUEST;
        if(!this.isLoggingEnabled(messageType)) {
            return;
        }
        const data = {
            request_id,
            request: this.getRequestData(req),
        };
        this.logData(messageType, data);
    }

    getRequestData(req) {
        const {
            executionStartTime,
            body,
            method,
            url,
            query,
            headers,
        } = req;
        const user = req.user && req.user.user_id;
        const id = req.unique_id;
        return {
            executionStartTime,
            body,
            method,
            url,
            query,
            headers,
            ip: req.getIP(),
            user,
        };
    }

    logResponse(request_id, req, metadata = undefined) {
        const messageType = this.TYPES.RESPONSE;
        if(!this.isLoggingEnabled(messageType)) {
            return;
        }
        const data = {
            request_id,
            request: this.getResponseData(req),
            metadata,
        };
        this.logData(messageType, data);
    }

    getResponseData(res) {
        const {
            statusCode,
            _headers,
            finished,
        } = res;
        return {
            statusCode,
            headers: _headers,
            finished,
            // not awailable at the moment when request is handled
            params: res.req && res.req.params,
        };
    }

    _getInstanceId() {
        const {pm_id, name} = process.env;
        if(!pm_id || !name) {
            return 'unknown instance';
        }

        return `${name} #${pm_id}`;
    }

    async close() {
        if(!this.client) {
            return Promise.resolve();
        }
        return new Promise((resolve, reject) => {
            this.client.once('end', resolve);
            this.client.quit();
        });
    }
}

module.exports = new BulkLogger();
