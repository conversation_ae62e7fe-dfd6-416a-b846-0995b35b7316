const EventEmitter = require('eventemitter2').EventEmitter2,
    {DEFAULT_SENDER, EMAIL_TO_FORMAT} = require('../constants/emails'),
    util = require('util'),
    EMAIL_TEMPLATE = 'roster_team_custom';

function Sender() {
    EventEmitter.call(this);

    this.events = {
        'club.team.entry.removed': {
            message: 'Club Director removed team "{team}" ({code}) from division "{division}".<br/>' + 
                    'Please check and contact Club Director if needed:<br/>' +
                    '{name}<br/>{phone}<br/>{email}',
            subject: 'Team deleted from {event}',
            label: 'team.deleted'
        },
        'club.team.division.change': {
            message: 'Club Director changed division for team "{team}" ({code}).<br/>' +
                    'Current division is "{division}".<br/>' + 
                    'Please check and contact Club Director if needed:<br/>' +
                    '{name}<br/>{phone}<br/>{email}',
            subject: 'Division changed on {event}',         
            label: 'team.division.changed'
        },
        'club.team.entered': {
            label: 'team.entered'
        },
        'club.assigned': {
            label: 'club.entered'
        },
        'club.entry.removed': {
            label: 'club.deleted'
        }
    }
}

util.inherits(Sender, EventEmitter);

Sender.prototype.divisionChanged = function (event_id, data) {
    this.emit('club.team.division.change', event_id, data);
}

Sender.prototype.teamRemoved = function (event_id, data) {
    this.emit('club.team.entry.removed', event_id, data);
}

Sender.prototype.teamEntered = function (event_id, data) {
    this.emit('club.team.entered', event_id, data)
}

Sender.prototype.clubEntered = function (event_id, data) {
    this.emit('club.assigned', event_id, data)
}

Sender.prototype.clubRemoved = function (event_id, data) {
    this.emit('club.entry.removed', event_id, data)
}

var sender = new Sender();

// roster team division change
sender.on('club.team.division.change', async function (event_id, data) {
    await _notifyEO(event_id, this.events[this.event], data);
});

sender.on('club.team.division.change', function (event_id, data) {
    _addNotification(event_id, _.extend({ action: this.events[this.event].label }, data));
})

sender.on('club.team.entry.removed', async function (event_id, data) {
     await _notifyEO(event_id, this.events[this.event], data);
});

sender.on('club.team.entry.removed', function (event_id, data) {
    _addNotification(event_id, _.extend({ action: this.events[this.event].label }, data));
})

sender.on('club.team.entered', function (event_id, data) {
    _addNotification(event_id, _.extend({ action: this.events[this.event].label }, data));
});

sender.on('club.assigned', function (event_id, data) {
    _addNotification(event_id, _.extend({ action: this.events[this.event].label }, data));
});

sender.on('club.entry.removed', function (event_id, data) {
    _addNotification(event_id, _.extend({ action: this.events[this.event].label }, data));
});

function _addNotification(event_id, data) {
    eventNotifications.add_notification(
        event_id, data,
        function (err) {
            if(err) loggers.errors_log.error(err);
        }
    );
}

async function _notifyEO (event_id, settings, data) {
    try {
        const notificationData = await _getNotificationData(event_id, data.roster_team_id);

        const preparedNotificationData = _prepareNotificationData(notificationData, settings);

        return _sendEmail(preparedNotificationData);
    } catch (err) {
        loggers.errors_log.error(err, event_id, data)
    }
}

async function _getNotificationData (event_id, roster_team_id) {
    const query = `
        SELECT e.name event_name, e.event_id, u.email, FORMAT('%s %s', u.first, u.last) as "name",
            rt.organization_code code, rt.team_name, d.name as "division_name", e.notify_emails,
            (SELECT row_to_json(co)
                FROM (
                    SELECT u2.email, FORMAT('%s %s', u2.first, u2.last) as "name", u2.phone_mob as phone
                    FROM "user" u2
                    LEFT JOIN club_owner co
                        ON co.user_id = u2.user_id
                    LEFT JOIN roster_team rt2
                        ON rt2.club_owner_id = co.club_owner_id
                    WHERE rt2.roster_team_id = rt.roster_team_id
                ) co
            ) club_owner
        FROM "user" u
        LEFT JOIN event_owner eo
            ON eo.user_id = u.user_id
        LEFT JOIN "event" e
            ON e.event_owner_id = eo.event_owner_id
        LEFT JOIN roster_team rt
            ON rt.event_id = e.event_id
        LEFT JOIN division d
            ON d.division_id = rt.division_id
        WHERE rt.roster_team_id = $1 AND rt.event_id = $2`;

    const {rows: [data] = []} = await Db.query(query, [roster_team_id, event_id]);

    if(_.isEmpty(data)) {
        throw new Error('No receiver');
    }

    return data;
}

function _prepareNotificationData (notificationData, settings) {
    notificationData.to = '';

    if (notificationData.notify_emails) {
        const emails = notificationData.notify_emails.split(',');

        for (let i = 0, l = emails.length, email; i < l; ++i) {
            email = emails[i].trim();

            if (notificationData.to.length) {
                notificationData.to += ', ';
            }

            notificationData.to += EMAIL_TO_FORMAT.format({
                name: email,
                email: email
            })
        }
    } else {
        notificationData.to = EMAIL_TO_FORMAT.format({
            name: notificationData.name,
            email: notificationData.email
        })
    }

    if (!notificationData.to) {
        throw new Error('No emails found for specified data');
    }

    notificationData.text =  settings.message.format({
        team: notificationData.team_name,
        code: notificationData.code,
        division: notificationData.division_name,
        name: notificationData.club_owner.name,
        phone: notificationData.club_owner.phone,
        email: notificationData.club_owner.email
    });

    notificationData.subject = settings.subject.format({
        event: notificationData.event_name
    })

    if(settings.cc) {
        notificationData.cc = settings.cc;
    }

    return notificationData;
}

function _sendEmail (notificationData) {
    return EmailService.renderAndSend({
        template: EMAIL_TEMPLATE,
        data: {
            content_html: notificationData.text,
            content_txt: notificationData.text
        },
        from: DEFAULT_SENDER,
        to: notificationData.to,
        cc: notificationData.cc,
        subject: notificationData.subject
    })
}

module.exports = sender;
