
let APIService = require('./ncsa/_NCSAAPIService');
const {DEFAULT_SW_EVENT} = require('../constants/ncsa');
let athleteDataValidationSchema = require('../validation-schemas/ncsa').athleteSubmitSchema;

class NCSAService {
    constructor (APIService) {
        this.APIService = APIService;
    }

    async submitAthleteInfoToNCSA (eventID, athleteData) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        const { error } = athleteDataValidationSchema.validate(athleteData);

        if (error) {
            throw { success: false, message: error.details[0].message };
        }

        let formattedAthleteData = await this.__prepareAthleteSubmitRequest(eventID, athleteData);

        let responseAthlete = await this.APIService.submitAthlete(formattedAthleteData);

        await this.__saveRequestToDB(eventID, responseAthlete, formattedAthleteData);
    }

    __getEventNCSAData (eventID, gender) {
        let query = knex('event AS e')
            .select(
                'ns.ncsa_sport_id',
                knex.raw('COALESCE(na.ncsa_event_id, na_default.ncsa_event_id) AS ncsa_event_id'),
                knex.raw(`
                    concat_ws(', ', 
                        e.long_name,
                        concat_ws(' - ', 
                            TO_CHAR(e.date_start::TIMESTAMP, 'Mon DD'), 
                            TO_CHAR(e.date_end::TIMESTAMP, 'Mon DD')
                        ), 
                        TO_CHAR(NOW(), 'YYYY')
                    ) AS event_text_identifier
                `)
            )
            .join('ncsa_sport AS ns', function () {
                this.on('e.sport_id', 'ns.sw_sport_id')
                    .andOn(knex.raw('ns.gender = ?', gender))
            })
            .leftJoin('ncsa_event AS na', 'e.event_id', 'na.event_id')
            .leftJoin('ncsa_event AS na_default', {'na_default.event_id': DEFAULT_SW_EVENT})
            .where('e.event_id', eventID);

        return Db.query(query).then(result => result && result.rows[0] || {});
    }

     __saveRequestToDB (eventID, responseAthlete, formattedAthleteData) {
        let query= knex('ncsa_recruit_request').insert({
            event_id : eventID,
            response : responseAthlete,
            request  : formattedAthleteData
        })
        return Db.query(query);
    }

    async __prepareAthleteSubmitRequest (eventID, athleteData) {
        let {
            ncsa_sport_id: NCSASportID,
            ncsa_event_id: NCSAEventID,
            event_text_identifier: eventName
        } = await this.__getEventNCSAData(eventID, athleteData.gender);

        if(!NCSASportID) {
            throw { validation: 'NCSA Sport ID Not Found' };
        }

        if(!NCSAEventID) {
            throw { validation: 'NCSA Event ID Not Found' };
        }

        return {
            recruit: {
                athlete_first_name: athleteData.athlete_first,
                athlete_last_name : athleteData.athlete_last,
                athlete_email     : athleteData.parent_email,
                athlete_phone     : athleteData.parent_phone,
                graduation_year   : athleteData.athlete_gradyear,
                parent_first_name : athleteData.parent_first,
                parent_last_name  : athleteData.parent_last,
                parent_email      : athleteData.parent_email,
                parent_phone      : athleteData.parent_phone,
                event_id          : NCSAEventID,
                athlete_or_parent : this.APIService.DEFAULT_ATHLETE_OR_PARENT_VALUE,
                sport_id          : NCSASportID,
                zip               : athleteData.zip,
                additional_data   : {
                    partner_registration_info: eventName
                }
            }
        };
    }
}

module.exports = new NCSAService(APIService);
