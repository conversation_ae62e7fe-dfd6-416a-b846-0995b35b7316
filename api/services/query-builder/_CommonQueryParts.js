'use strict';

class CommonQueryParts {
    constructor () {}

    excludeUnsubscribedEmails (query, emailFieldName, eventFieldName) {
        let existsQuery = squel.select().from('email_unsubscribe_list', 'eul')
            .where(`LOWER(TRIM(eul."email")) = LOWER(TRIM(${emailFieldName}))`)
            .where(
                `(eul.event_id = ${eventFieldName} OR eul.event_id = ?)`
                , [EmailService.unsubscribeToken.ALL_EVENTS_UNSUBSCRIBE_EVENT_ID]
            );

        return query.where(squel.str('NOT EXISTS(?)', existsQuery));
    }
}

module.exports = new CommonQueryParts();
