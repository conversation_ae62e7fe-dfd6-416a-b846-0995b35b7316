class InvoiceCreatedNotificationService {
    constructor(NotificationsService) {
        this.NotificationsService = NotificationsService;
    }

    get TEMPLATE() {
        return "club-invoice/invoice-created";
    }

    async send(invoiceId) {
        const invoiceData = await this.#getInvoiceData(invoiceId);

        const notificationData = this.#prepareNotificationData(invoiceData);

        return Promise.all([
            this.#notifyCD(notificationData),
            this.#notifyAdmin(notificationData),
        ]);
    }

    #notifyCD(params) {
        const subject = params.subject;
        const email = params.director_club_email;

        if(!email) {
            return;
        }

        return this.NotificationsService.send(
            email,
            subject,
            params,
            this.TEMPLATE
        );
    }

    #notifyAdmin(params) {
        const subject = params.subject;
        const email = params.administrative_club_email;

        if(!email) {
            return;
        }

        return this.NotificationsService.send(
            email,
            subject,
            params,
            this.TEMPLATE
        );
    }

    #prepareNotificationData(params) {
        const subject = `Invoice generated for: ${params.event_name}`;
        const link_to_invoices = this.#getClubInvoicesLink();

        return {
            ...params,
            subject,
            link_to_invoices
        };
    }

    #getClubInvoicesLink () {
        return `${sails.config.urls.main_app.baseUrl}/#/club/invoices`;
    }

    async #getInvoiceData(purchaseId) {
        const query = knex('purchase as p')
            .select({
                description: 'p.notes',
                event_name: 'e.long_name',
                event_email: 'e.email',
                club_name: 'rc.club_name',
                administrative_club_email: 'mc.administrative_email',
                director_club_email: 'mc.director_email',
            })
            .join('event as e', 'e.event_id', 'p.event_id')
            .join('roster_club as rc', 'rc.roster_club_id', 'p.roster_club_id')
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .where('p.purchase_id', purchaseId);

        const {rows: [purchase] = []} = await Db.query(query);

        if(!purchase) {
            throw { validation: 'Club Invoice is not created.' };
        }

        return purchase;
    }
}

module.exports = InvoiceCreatedNotificationService;
