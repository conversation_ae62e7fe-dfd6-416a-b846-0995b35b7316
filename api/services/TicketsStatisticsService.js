'use strict';

const 
    moment  = require('moment'),
    utils   = require('../lib/swUtils'),
    co      = require('co'),
    knex    = require('knex')({client: 'pg'}),
    { spawn } = require('child_process'),
    path = require('path');
const { RECEIVER_TYPES } = require('../constants/common');
const { PAYMENT_PROVIDER } = require('../constants/payments');

const 
	STAT_TYPES = {
		PRIOR 			: 'prior',
		DURING 			: 'during',
		API 			: 'api',
		CASH 			: 'cash',
		CHECK 			: 'check',
		AUTO_TRANSFERED : 'auto_transfered',
		SW_ACC 			: 'sw_acc'
	},
	INPUT_DATE_FORMAT 		= 'MM/DD/YYYY HH:mm',
	OUTPUT_DATE_FORMAT 		= 'YYYY-MM-DD HH:mm:ss',
	STATS_DISALLOWED_TYPES 	= `'waitlist', 'check', 'cash'`,
	DEFAULT_TRANSFERS 		= {
        list    : [],
        total   : 0
    },
    DEFAULT_EVENT_BALANCE 	= {
    	current  	: 0,
    	target 	 	: 0,
    	collected 	: 0
    }

module.exports = {
	/*
		// Example result object:
		{
			"prior": {
				"tr" 			: number, // Transactions column
				"types" 		: [{ "total": number, "not_scanned": number, "sort_order": number }],
				"amount" 		: number,
				"stripe" 		: number,
				"sw" 			: number,
				"balance" 		: number
			}
		}
	*/
	getStatistics 		: countStatistics,
	/*
	* Card amount consists of amount of charges that were created via Stripe Connect feature and
	* amount of charges that were created directly on a Stripe Account
	*/
	getCardStatistics 	: function (eventID) {
		return Promise.all([
			getStats(eventID, null, null, STAT_TYPES.AUTO_TRANSFERED),
			getStats(eventID, null, null, STAT_TYPES.SW_ACC),
		]).then(data => {
			let [connected, standard] = data;

			let connectedTotalItemsQty = getPurchasedItemsQty(connected[1]);
			let standardTotalItemsQty = getPurchasedItemsQty(standard[1]);

			connected 			= connected[0];
			connected.items_qty = connectedTotalItemsQty;

			standard 			= standard[0];
			standard.items_qty 	= standardTotalItemsQty;

			return getCardStatisticsResult(connected, standard);
		})
	},
	getCashStatistics 	: function (eventID) {
		return getStats(eventID, null, null, STAT_TYPES.CASH)
		.then(data => {
			let [cash, items] 	= data;

			let boughtItemsQty 	= getPurchasedItemsQty(items);
			cash.items_qty 		= boughtItemsQty 

			return utils.propsToNumber(cash);
		})
	},
	getCheckStatistics 	: function (eventID) {
		return getStats(eventID, null, null, STAT_TYPES.CHECK)
		.then(data => {
			let [check, items] 	= data;

			let boughtItemsQty 	= getPurchasedItemsQty(items);
			check.items_qty 	= boughtItemsQty;

			return utils.propsToNumber(check);
		})
	},
	getStatisticsByType : getStats,
	getEventBalance  	: getEventBalance,
	getSWFees  			: getSWFees,
	getUniqueScans		: uniqueScansStatistics,
	getDetailedUniqueScans: detailedUniqueScansStatistics,
    /**
     * Export tickets statistics into file
     * @param eventId {number}
     * @param dateFrom {string}
     * @param dateTo {string}
     * @returns {Promise<string>} path to created file
     */
    exportStatistics(eventId, dateFrom, dateTo) {
        return new Promise((resolve, reject) => {
            let result = [];
            let error = [];

            const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

            let procParams = [
                'tickets-statistics-export.js',
                `--connection=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`,
                `--event=${eventId}`,
            ];
            if(dateFrom) {
                procParams.push(`--date-from=${dateFrom}`);
            }
            if(dateTo) {
                procParams.push(`--date-to=${dateTo}`);
            }

            let proc = spawn('node', procParams, {
                cwd     : path.resolve(__dirname, '../../sw-utils'),
                stdio   : 'pipe'
            });

            const onProcessEnd = (code) => {
                if(code > 0) {
                    const errorString = Buffer.concat(error).toString();
                    reject({error: errorString});
                } else {
                    try {
                        resolve(Buffer.concat(result).toString());
                    } catch (err) {
                        reject(err);
                    }
                }
            };

            proc.on('error', (err) => reject(err));

            proc.on('close', onProcessEnd);

            proc.stdout.on('data', (data) => result.push(data));
            proc.stderr.on('data', (err) => error.push(err));
        });
    },
}

function getPurchasedItemsQty (items) {
	return items.reduce((sum, item) => {
		let itemTotal = Number(item.total) || 0;
		return (sum + itemTotal);
	}, 0);
}

function getCardStatisticsResult (connected, standard) {
	let approx 		= utils.normalizeNumber.bind(utils);
	let result 		= {};
	let props 		= Object.keys(connected).concat(Object.keys(standard));
	let uniqueProps = new Set(props);

	uniqueProps.forEach(prop => {
		let cVal = Number(connected[prop]) || 0;
		let sVal = Number(standard[prop]) || 0;

		result[prop] = approx(cVal + sVal)
	})

	return result;
}

async function uniqueScansStatistics (eventId) {
	const scansByHourQuery = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			sah.scanner_location,
			sah.scanner_name,
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, (sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE ORDER BY sah.created)
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, COUNT(data.*) AS scannings_qty, data.hour
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 3, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;

	const scansByHourAndLocation = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			sah.scanner_location,
			sah.scanner_name,
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, (sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE ORDER BY sah.created)
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, data.scanner_location, COUNT(data.*) AS scannings_qty, data.hour
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 2, 4, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;

	const scansByScannerName = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			sah.scanner_location,
			sah.scanner_name,
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, (sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE ORDER BY sah.created)
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, data.scanner_location,  data.scanner_name, COUNT(data.*) AS scannings_qty, data.hour
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 2, 3, 5, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;


	const queries = [
		scansByHourQuery,
		scansByHourAndLocation,
		scansByScannerName
	];

	const promises = queries.map(query=> Db.query(query, [eventId])
		.then(({ rows }) => rows));

	return Promise.all(promises);
}

async function detailedUniqueScansStatistics (eventId) {
	const scansByHourQuery = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			et.ticket_type,
			et.label, 
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, sah.created::date ORDER BY sah.created) AS rank
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			JOIN purchase AS p ON p.ticket_barcode::text = sah.ticket_barcode
			JOIN purchase_ticket AS pt ON pt.purchase_id = p.purchase_id
			JOIN event_ticket AS et ON et.event_ticket_id = pt.event_ticket_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, data.ticket_type, data.label,  COUNT(*) AS scannings_qty, data.hour
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 2, 3, data.hour, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;

	const scansByHourAndLocation = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			sah.scanner_location,
			et.ticket_type, 
			et.label,
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, sah.created::date ORDER BY sah.created) AS rank
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			JOIN purchase AS p ON p.ticket_barcode::text = sah.ticket_barcode
			JOIN purchase_ticket AS pt ON pt.purchase_id = p.purchase_id
			JOIN event_ticket AS et ON et.event_ticket_id = pt.event_ticket_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, data.scanner_location, data.ticket_type, data.label, data.hour, COUNT(*) AS scannings_qty
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 2, 3, 4, 5, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;

	const scansByScannerName = `
		WITH data AS (
			SELECT sah.ticket_barcode, 
			sah.scanner_location,
			sah.scanner_name,
			et.ticket_type, 
			et.label, 
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH12:00 AM')   "hour",
			TO_CHAR((sah."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone"), 'HH24:00 AM')   "hour_for_ordering",
			RANK() OVER (PARTITION BY sah.ticket_barcode, sah.created::date ORDER BY sah.created) AS rank
			FROM swt_api_history sah
			JOIN event e ON e.event_id = sah.event_id
			JOIN purchase AS p ON p.ticket_barcode::text = sah.ticket_barcode
			JOIN purchase_ticket AS pt ON pt.purchase_id = p.purchase_id
			JOIN event_ticket AS et ON et.event_ticket_id = pt.event_ticket_id
			WHERE sah.event_id = $1
			AND sah.request_url = '/api/swt/scan'
		)
		SELECT data.date, data.scanner_location, data.scanner_name, data.ticket_type, data.label, data.hour, COUNT(*) AS scannings_qty
		FROM data
		WHERE data.rank = 1
		GROUP BY 1, 2, 3, 4, 5, 6, data.hour_for_ordering
		ORDER BY 1, data.hour_for_ordering;
	`;


	const queries = [
		scansByHourQuery,
		scansByHourAndLocation,
		scansByScannerName
	];

	const promises = queries.map(query=> Db.query(query, [eventId])
		.then(({ rows }) => rows));

	return Promise.all(promises);
}

async function countStatistics (eventId, dateFrom, dateTo) {
	let dateFromStr 	= dateFrom 	&& moment(dateFrom, INPUT_DATE_FORMAT).format(OUTPUT_DATE_FORMAT),
		dateToStr 		= dateTo 	&& moment(dateTo, INPUT_DATE_FORMAT).format(OUTPUT_DATE_FORMAT),
		datedNotPassed 	= !(dateFromStr || dateFromStr);

	let statistics = {
		prior 			: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.PRIOR),
		during 			: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.DURING),
		api 			: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.API),
		cash 			: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.CASH),
		totals 			: await getStats(eventId, dateFromStr, dateToStr),
		/* Actualy, Auto Transfered means "Is on Stripe Account of EO" */
		auto_trans 		: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.AUTO_TRANSFERED, true),
		sw_acc 			: await getStats(eventId, dateFromStr, dateToStr, STAT_TYPES.SW_ACC, true),
		transfers 		: datedNotPassed ? await getTransfers(eventId) : _.clone(DEFAULT_TRANSFERS),
		event_balance 	: datedNotPassed ? await getEventBalance(eventId) : _.clone(DEFAULT_EVENT_BALANCE),
		/** 
		* NOTE: "net_profit" field does not take into account "additional_fee_amount", 
		* so we have to substract "additional_fee_amount" from "auto_trans" (because, 
		* it's a sum of "net_profit")
		**/
		additional_fee  : await countAdditionalFee(eventId, dateFromStr, dateToStr),
		ticket_types 	: await getTicketTypesNames(eventId),
		filters 		: await getFilterStats(eventId),
		scanned_unique_tickets: await getScannedUniqueTickets(eventId),
        vip_filters     : await getVipFilterStats(eventId),
	};

	let result 				= formatResultObj(statistics);

	result.subtotals 		= getCreditSubtotal(_.pick(result, 'prior', 'during', 'api'));

	result.transfers 		= statistics.transfers;
	result.event_balance 	= statistics.event_balance;

	/* See the comment above */
	result.auto_trans.balance = utils.normalizeNumber(result.auto_trans.balance - statistics.additional_fee);

	/* SUM(additional_fee_amount) */
	result.collected 		= statistics.additional_fee;
	/**
	* For now, the overall amount of the additional fee to collect is the sum of event's target and 
	* SW fee for cash purchases
	**/
	result.need_to_collect  = utils.normalizeNumber(result.event_balance.target + result.cash.sw);

	result.net 				= utils.normalizeNumber(result.totals.balance - result.cash.amount);

	/* NOTE: should be equal to result.totals.balance */
	result.event_total_net_profit = 
		utils.normalizeNumber(
			result.net + result.cash.amount
		);

	/*
	* 26.08.2016: we do not need to take an account balance target
	* 17.01.2017: we do not need to add current "balance" value
	*/
	result.to_transfer 		= utils.normalizeNumber(
								result.net - result.auto_trans.balance - result.transfers.total
								); 

	result.ticket_types 	= statistics.ticket_types;

	result.filters 			= statistics.filters;

	result.scanned_unique_tickets = statistics.scanned_unique_tickets;

	result.vip_filters = statistics.vip_filters;

	return result;
}

function formatResultObj (statistics) {
	let statsKeys = Object.keys(_.omit(statistics, 'transfers', 'event_balance', 'ticket_types', 'filters', 'additional_fee', 'scanned_unique_tickets', 'vip_filters'));

	return statsKeys.reduce(function (resultObj, key) {
		let dbResults 			= statistics[key],

			trStatResult 		= dbResults[0],
			typesStatResult 	= dbResults[1];

		if(!trStatResult) {
			throw new Error('Statistics data not found');
		}

		Object.keys(trStatResult).forEach(function castToNum (statKey) {
			trStatResult[statKey] = +trStatResult[statKey];
		});

		resultObj[key] 			= _.clone(trStatResult);
		resultObj[key].types 	= typesStatResult.map(function castToNum (ticket) {

			ticket.total  			= +ticket.total;
			ticket.not_scanned 		= +ticket.not_scanned;
			ticket.order 			= +ticket.order;

			return ticket;
		});

		return resultObj;
	}, {});
}

// statistics under the table
function getFilterStats (eventId) {
    const isAssignedModeColumn = `(e."tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN`;
	let sql = 

		`SELECT (
			SELECT ROW_TO_JSON("d")
			FROM (
				SELECT 
                    COALESCE(SUM((
                        SELECT SUM(pt.quantity)
                        FROM "purchase" pp
                        INNER JOIN "purchase_ticket" pt ON pt.purchase_id = pp.purchase_id AND pt.canceled IS NULL
                        WHERE pp.is_ticket IS TRUE 
                            AND pp.canceled_date IS NULL
                            AND pp.status = 'paid'
                            AND pp.event_id = p.event_id
                            AND (
                                (p.is_ticket IS TRUE AND p.purchase_id = pp.purchase_id)
                                OR (p.is_ticket IS FALSE AND p.purchase_id = pp.linked_purchase_id)
                            )
                    )), 0) "qty",
                    COALESCE(SUM(p.amount), 0) "amount"
                FROM "purchase" p
				WHERE p.event_id = e.event_id
				    AND p.payment_for = 'tickets'
				    AND p.status = 'paid'
				    AND p.canceled_date IS NULL 
                    AND p."is_payment" IS TRUE
				    AND (
				        SELECT SUM(pt.quantity) 
				        FROM "purchase_ticket" pt 
                        INNER JOIN "purchase" 
                            ON "purchase"."purchase_id" = pt."purchase_id"
                            AND "purchase"."is_ticket" IS TRUE
				        WHERE pt.canceled IS NULL 
                            AND (
                                "purchase".purchase_id = p.purchase_id
                                OR 
                                "purchase"."linked_purchase_id" = p."purchase_id"
                            )
				    ) > 0
				    AND p.type <> 'free'
			) "d"
		 ) "paid", (
		 	SELECT ROW_TO_JSON("d")
            FROM (
                SELECT 
                    COALESCE(SUM(q.quantity), 0) "qty",
                    COALESCE(SUM(p.amount_refunded), 0) "amount"
                FROM "purchase" p
                INNER JOIN LATERAL (
                    SELECT COALESCE(SUM(pt.quantity), 0) "quantity"
                    FROM purchase_ticket pt
                    LEFT JOIN purchase p2 ON p.is_ticket IS FALSE AND p2.is_ticket IS TRUE AND p2.linked_purchase_id = p.purchase_id
                    WHERE pt.purchase_id = COALESCE(p2.purchase_id, p.purchase_id)
                        AND p2.event_id = e.event_id
                        AND p2.amount_refunded is not null
                    GROUP BY p.purchase_id
                ) q ON TRUE
                WHERE p.event_id = e.event_id 
                    AND p.payment_for = 'tickets'
                    AND p.date_refunded IS NOT NULL
                    AND p."is_payment" IS TRUE
            ) "d"
		 ) "refunded", (
		 	SELECT ROW_TO_JSON("d")
			FROM (
				SELECT 
                    COALESCE(COUNT(p.purchase_id), 0) "qty", 
                    COALESCE(SUM(p.amount), 0) "amount"
                FROM "purchase" p
				WHERE p.event_id = e.event_id 
					AND p.payment_for = 'tickets'
					AND p.dispute_created IS NOT NULL
                    AND p."is_payment" IS TRUE
			) "d"
		 ) "disputed", (
		 	SELECT ROW_TO_JSON("d")
			FROM (
				SELECT 
                    COALESCE(SUM(pt.quantity), 0) "qty", 
                    COALESCE(SUM(p.amount), 0) "amount"
                FROM "purchase" p
                INNER JOIN "purchase_ticket" pt ON pt.purchase_id = p.purchase_id
				WHERE p.event_id = e.event_id 
					AND p.payment_for = 'tickets'
					AND p.status = 'pending' 
					AND p.canceled_date IS NULL
                    AND p."is_payment" IS TRUE
                    AND ${isAssignedModeColumn} IS FALSE
			) "d"
		 ) "pending", (
            SELECT 
                COALESCE(SUM(pt.available), 0)
            FROM "purchase" p 
            JOIN purchase_ticket pt 
                ON pt.purchase_id = p.purchase_id and pt.canceled IS NULL
            JOIN "purchase" payment
                ON payment."is_payment" IS TRUE
                AND (
                    (p.is_payment IS FALSE AND payment."purchase_id" = p."linked_purchase_id")
                    OR 
                    (p.is_payment IS TRUE AND payment."purchase_id" = p."purchase_id")
                )
            WHERE p.event_id = e."event_id"
                AND p.payment_for = 'tickets'
                AND pt.available > 0
                AND p.canceled_date IS NULL
                AND p."is_ticket" IS TRUE
                AND payment."type" <> 'waitlist'
                AND (
                     CASE 
                         WHEN (p."status" = 'canceled') THEN p."status" 
                         ELSE payment."status"
                     END
                ) IS DISTINCT FROM 'canceled'
                AND p.type <> 'free'
         )::INTEGER "not_scanned", (
		 	SELECT 
			 	CASE COALESCE((tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, FALSE)
					WHEN TRUE THEN NULL
					ELSE COALESCE(SUM(pt.available), 0)
                END
            FROM "purchase" p
            INNER JOIN "purchase_ticket" pt ON pt.purchase_id = p.purchase_id
			WHERE p.event_id = e.event_id
			    AND p.payment_for = 'tickets'
			    AND p.canceled_date IS NULL 
			    AND p.status <> 'canceled'
                AND pt.canceled IS NULL
			    AND p.tickets_scan IS NOT NULL 
			    AND p.tickets_scan <> ''
                AND p."is_ticket" IS TRUE
			    AND (
			        SELECT SUM(pt.available) 
			        FROM "purchase_ticket" pt 
			        WHERE pt.purchase_id = p.purchase_id 
			            AND pt.canceled IS NULL
			    ) > 0
         )::INTEGER "unclaimed", (
         SELECT ROW_TO_JSON("d")
         FROM (
             SELECT
                 SUM((
                     SELECT COALESCE(SUM(pt.quantity), 0)
                     FROM "purchase" pp
                     INNER JOIN "purchase_ticket" pt ON pt.purchase_id = pp.purchase_id
                     INNER JOIN "event_ticket" et ON et.event_ticket_id = pt.event_ticket_id AND et.sub_type = 'parking'
                     WHERE pp.is_ticket IS TRUE AND (
                         (p.is_ticket IS TRUE AND p.purchase_id = pp.purchase_id)
                         OR (p.is_ticket IS FALSE AND p.purchase_id = pp.linked_purchase_id)
                     )
                 )) "qty",
                 COALESCE(SUM(p.amount), 0) "amount"
             FROM "purchase" p
             WHERE p.event_id = e.event_id
                 AND p.payment_for = 'tickets'
                 AND p.status = 'paid'
                 AND p.canceled_date IS NULL
                 AND p."is_payment" IS TRUE
                 AND (
                     SELECT SUM(pt.quantity)
                     FROM "purchase_ticket" pt
                         INNER JOIN "purchase"
                             ON "purchase"."purchase_id" = pt."purchase_id"
                             AND "purchase"."is_ticket" IS TRUE
                         INNER JOIN "event_ticket" et
                             ON et.event_ticket_id = pt.event_ticket_id
                             AND et.sub_type = 'parking'
                     WHERE pt.canceled IS NULL
                         AND (
                             "purchase".purchase_id = p.purchase_id
                             OR
                             "purchase"."linked_purchase_id" = p."purchase_id"
                         )
                 ) > 0
                 AND p.type <> 'free'
             ) "d"
         ) "parking"
		FROM "event" e 
		WHERE e.event_id = $1`;

	return Db.query(sql, [eventId]).then(result => {
		let filtersStats = result.rows[0];

		if(!filtersStats) {
			throw new Error('Statistics for folters not found');
		}

		return filtersStats;
	});
}


function getVipFilterStats(eventID) {
	const query = knex('event AS e')
		.select(
            {
				vip: knex.raw(`
					COUNT(p) FILTER (
						WHERE p.status = ?
						AND p.receiver_type IS NULL
						AND pt.canceled IS NULL
					)::INTEGER
				`, TicketsService.STATUS.PAID)
			},
            {
                for_exhibitors: knex.raw(`
					COUNT(p) FILTER (
						WHERE p.status = ?
						AND p.receiver_type = ?
						AND pt.canceled IS NULL
					)::INTEGER
				`, [TicketsService.STATUS.PAID, RECEIVER_TYPES.EXHIBITORS])
            },
			{
				not_scanned: knex.raw(`
					COUNT(pt) FILTER(
						WHERE pt.available > 0
						AND p.type <> ?
						AND p.status IS DISTINCT FROM ?
					)::INTEGER
				`, [TicketsService.TYPE.WAITLIST, TicketsService.STATUS.CANCELED])
			}
		)
		.join('purchase AS p', function() {
			this.on(knex.raw(`
				p.event_id = e.event_id
				AND p.is_ticket IS TRUE
				AND p.is_payment IS FALSE
				AND p.payment_for = 'tickets'
				AND p.canceled_date IS NULL
				AND p.type = ?
			`, TicketsService.TYPE.FREE))
		})
		.join('purchase_ticket AS pt', 'pt.purchase_id', 'p.purchase_id')
		.where('e.event_id', eventID)
		.whereRaw(`(e."tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE`);

	return Db.query(query).then(({ rows : [row] }) => row);
}

function getEventBalance (eventId) {
	/* Should we count pending ACH payments? */
	return Db.query(
        `SELECT 
            (COALESCE(e.tickets_sw_balance, 0) - COALESCE(e.tickets_sw_target_balance, 0)) "current", 
            COALESCE(e.tickets_sw_target_balance, 0) "target",
            COALESCE(e.tickets_sw_balance, 0) "collected", (
				SELECT 
            	    COALESCE(SUM(
            	    	COALESCE(p."collected_sw_fee", 0) + 
            	    	COALESCE(p."additional_fee_amount", 0)
            	    ), 0)
            	 FROM "purchase" p 
            	 WHERE p."event_id" = e."event_id"
            	    AND p."payment_for" = 'tickets' 
            	    AND p."status" IN ('paid', 'pending')
            	    AND p."type" NOT IN(${STATS_DISALLOWED_TYPES})
                    AND (
                        p.payment_provider = '${PAYMENT_PROVIDER.TILLED}'
                        OR p.stripe_payment_type = 'connect'
                    )
                    AND p."is_payment" IS TRUE
                    AND (p."dispute_status" <> 'lost' OR p.dispute_status IS NULL)
            ) "collected_amount"
         FROM "event" e 
         WHERE e.event_id = $1`,
        [eventId]
    ).then(result => {
        let row = result.rows[0];
        return {
            current     	 : utils.normalizeNumber(row.current)     		|| 0,
            target      	 : utils.normalizeNumber(row.target)      		|| 0,
            collected   	 : utils.normalizeNumber(row.collected)   		|| 0,
            collected_amount : utils.normalizeNumber(row.collected_amount)  || 0
        };
    })
}

function getSWFees (eventID) {
	return Db.query(
		`SELECT 
			e."tickets_sw_fee", (
				ARRAY_TO_JSON(ARRAY_AGG(DISTINCT et."application_fee" ORDER BY et."application_fee"))
			) "types_fees"
		 FROM "event" e 
		 LEFT JOIN "event_ticket" et 
		 	ON et."event_id" = e."event_id"
		 	AND et."application_fee" IS NOT NULL 
		 	AND et."application_fee" <> 0
		 WHERE e."event_id" = $1
		 GROUP BY e."event_id"`,
		[eventID]
	).then(res => {
		let row = res.rows[0] || {};

		return +row.tickets_sw_fee
	})
}

function getTicketTypesNames (eventId) {
	return Db.query(
        `SELECT et.label, et.event_ticket_id, et.sort_order "order"
         FROM event_ticket et
         WHERE et.event_id = $1
         ORDER BY et.sort_order, et.event_ticket_id`,
        [eventId]
    ).then(result => {
        return result.rows;
    });
}

function getTransfers (eventId) {
	return Db.query(
        `SELECT 
        	COALESCE(ARRAY_TO_JSON(ARRAY_AGG(transfers)), '[]'::JSON) "list", 
        	COALESCE(SUM(transfers.amount), 0)::NUMERIC "total"
         FROM (
             SELECT 
                 st.transfer_type "type",
                 TO_CHAR((st.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'MM/DD/YYYY HH12:MI AM') "created",
                 COALESCE(st.amount, 0)::NUMERIC "amount",
                 st.description,
                 st.check_num
             FROM "stripe_transfer" st
             LEFT JOIN "event" e
                 ON e.event_id = st.event_id
             WHERE st.event_id = $1
             	AND st.date_canceled IS NULL
             	AND st."payment_for" = 'tickets'
             	AND st."transfer_type" NOT IN('auto', 'pay_out', 'virtual_auto')
         ) "transfers"`,
        [eventId]
    ).then(function (result) {
        if(result.rows.length === 0) {
            return _.clone(DEFAULT_TRANSFERS);
        } else {
            let transfersData 	= result.rows[0];
            transfersData.total = +transfersData.total;
            return transfersData;
        }
    });
}

// sum of "during", "api", "prior"
function getCreditSubtotal (result) {
	let defaultTicketType = { total: 0, not_scanned: 0, order: 0 };

	return Object.keys(result).reduce(function (subtotal, key) {
		let currentStat = result[key];

		subtotal.tr 		= utils.normalizeNumber(subtotal.tr + currentStat.tr);
		subtotal.amount 	= utils.normalizeNumber(subtotal.amount + currentStat.amount);
		subtotal.balance 	= utils.normalizeNumber(subtotal.balance + currentStat.balance);
		subtotal.sw 		= utils.normalizeNumber(subtotal.sw + currentStat.sw);
        subtotal.payment_method_fee = utils.normalizeNumber(subtotal.payment_method_fee + currentStat.payment_method_fee);

		currentStat.types.forEach((ticket, index) => {
			let currentType = subtotal.types[index];

			if(!currentType) {
				subtotal.types.push(_.clone(defaultTicketType));
				currentType = subtotal.types[index];
			}

			currentType.total 			= utils.normalizeNumber(currentType.total + ticket.total);
			currentType.not_scanned 	= utils.normalizeNumber(currentType.not_scanned + ticket.not_scanned);
			currentType.order 			= ticket.order;
		});

		return subtotal;
	}, { tr: 0, types: [], amount: 0, payment_method_fee: 0, balance: 0, sw: 0 });
}

// TODO: refactor this!
// These might be replaced with DB views
function getStats (eventId, fromDate, toDate, statType, trOnly) {
	let { params, whereBlock } = getWhereBlock(eventId, fromDate, toDate, statType);

	let trSQL 		= getTransactionsSQL(whereBlock),
		typesSQL 	= getTypesSQL(whereBlock);

	return Promise.all([
		Db.query(trSQL, params).then(result => result.rows[0]),

		(trOnly) ? [] : Db.query(typesSQL, params).then(result => result.rows),
	]);
}

function getWhereBlock (eventId, fromDate, toDate, statType) {
	let params = [eventId], whereBlock;

	switch (statType) {
		case STAT_TYPES.PRIOR:
			whereBlock = 
				`
				AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) < e.date_start
				AND p.type NOT IN (${STATS_DISALLOWED_TYPES})
				AND p.source = 'site'`;
			break;
		case STAT_TYPES.DURING:
			whereBlock = 
				`
				AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) >= e.date_start
				AND p.type NOT IN (${STATS_DISALLOWED_TYPES})
				AND p.source = 'site'`;
			break;
		case STAT_TYPES.API:
			whereBlock = 
				`
				AND p.type NOT IN (${STATS_DISALLOWED_TYPES})
				AND p.source = 'api'`;
			break;
		case STAT_TYPES.CHECK:
			whereBlock = 
				`
				AND p."type" = 'check'
				AND p."status" = 'paid'`;
			break;
		case STAT_TYPES.CASH:
			whereBlock = 
				`
				AND p.type = 'cash'`;
			break;
		case STAT_TYPES.AUTO_TRANSFERED:
			whereBlock = 
				`
				AND p.status = 'paid'
                AND (
                    p.payment_provider = '${PAYMENT_PROVIDER.TILLED}'
                    OR p.stripe_payment_type = 'connect'
                )
				AND p.type NOT IN (${STATS_DISALLOWED_TYPES})`;
			break;
		case STAT_TYPES.SW_ACC:
			whereBlock =
				`
				AND p.type = 'card'
                AND p.stripe_payment_type <> 'connect'`;
			break;
		default:
			whereBlock = '';
			break;
	}

	if(fromDate) {
		params.push(fromDate);
		whereBlock += 
			`
			AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) >= ($${params.length})::TIMESTAMP`
	}

	if(toDate) {
		params.push(toDate);
		whereBlock += 
			`
			AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) <= ($${params.length})::TIMESTAMP`
	}

	return { params, whereBlock };
}

function countAdditionalFee (eventID, dateFrom, dateTo) {
	let params = [eventID];

	let query = 
		`SELECT COALESCE(SUM(p."additional_fee_amount"), 0) "fee"
		 FROM "purchase" p 
		 INNER JOIN "event" e 
		 	ON e."event_id" = p."event_id"
		 WHERE p.payment_for = 'tickets'
		    AND p.event_id = $1
		    AND p."status" = 'paid'
		    AND p.canceled_date IS NULL
		    AND p.type <> 'waitlist'
		    AND (p.dispute_status <> 'lost' or p.dispute_status IS NULL)
            AND p."is_payment" IS TRUE
		 	AND p.type NOT IN (${STATS_DISALLOWED_TYPES})`

	/* NOTE: CODE DUPLICATE!!! */
	if (dateFrom) {
		params.push(dateFrom);
		query += 
			`
			AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) >= ($${params.length})::TIMESTAMP`
	}

	if (dateTo) {
		params.push(dateTo);
		query += 
			`
			AND (p.created::TIMESTAMPTZ AT TIME ZONE e.timezone) <= ($${params.length})::TIMESTAMP`

	}

	return Db.query(query, params)
	.then(res => res.rows[0] && Number(res.rows[0].fee) || 0)
}

function getTransactionsSQL (whereBlock) {
	return (
		`WITH "sportwrench_fee" AS (
            SELECT
                SUM(COALESCE(NULLIF(pt.ticket_fee, 0), et.application_fee) * (
                CASE 
                    WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                    THEN 1
                    ELSE (CASE WHEN pt."canceled" IS NULL THEN pt.quantity ELSE 0 END)
                END)) "fee"
            FROM "purchase" ticket_row
            JOIN "purchase_ticket" pt 
                ON pt.purchase_id = ticket_row.purchase_id
            JOIN "purchase" p
                ON p."purchase_id" = COALESCE(ticket_row."linked_purchase_id", ticket_row."purchase_id")
                AND p."is_payment" IS TRUE
            LEFT JOIN  "event_ticket" et 
                ON et."event_ticket_id" = pt."event_ticket_id"
            INNER JOIN "event" e 
                ON e.event_id = ticket_row.event_id
            WHERE ticket_row.event_id = $1
                AND ticket_row.payment_for = 'tickets' 
                AND ticket_row."status" IS DISTINCT FROM 'canceled' 
                     AND p."status" IS DISTINCT FROM 'canceled'
                AND ticket_row.canceled_date IS NULL 
                AND ticket_row."type" <> 'waitlist'
                AND ticket_row."dispute_status" IS DISTINCT FROM 'lost'
                     AND p."dispute_status" IS DISTINCT FROM 'lost'
                AND ticket_row."is_ticket" IS TRUE
                ${whereBlock || ''}
        )
        SELECT 
		    COUNT(p.*) FILTER (WHERE p."is_payment" IS TRUE) "tr",
		    COALESCE(SUM(p.amount) FILTER (WHERE p."is_payment" IS TRUE), 0) "amount",
            COALESCE(SUM(ROUND(
                CASE
                    WHEN p.payment_provider = '${PAYMENT_PROVIDER.TILLED}'
                        THEN p.tilled_fee
                    ELSE p.stripe_fee
                END
            , 2)) FILTER (WHERE p."is_payment" IS TRUE), 0) "payment_method_fee",
		    COALESCE(SUM(p.net_profit) FILTER (WHERE p."is_payment" IS TRUE), 0)  "balance",
		    COALESCE((SELECT "fee" FROM "sportwrench_fee"), 0) "sw"
		FROM "purchase" p 
		INNER JOIN "event" e 
			ON e.event_id = p.event_id
		WHERE p.payment_for = 'tickets'
		    AND p.event_id = $1
		    AND p."status" IS DISTINCT FROM 'canceled' 
		    AND p.canceled_date IS NULL
		    AND p.type <> 'waitlist'
		    AND p."dispute_status" IS DISTINCT FROM 'lost'
		    ${whereBlock || ''}`
	);
}

function getTypesSQL (whereBlock) {
	return (
		`SELECT d."order", d."p"->'total' "total", d."p"->'not_scanned' "not_scanned"
         FROM (
             SELECT 
                 et.sort_order "order", 
                 (
                     SELECT 
                         JSON_BUILD_OBJECT(
                             'total', (CASE 
                                            WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                                            THEN count(p.*)
                                            ELSE COALESCE(SUM(pt.quantity), 0)
                                        END),    
                             'not_scanned', COALESCE(SUM(pt.available), 0)
                         )
                     FROM "purchase_ticket" pt
                     INNER JOIN LATERAL (
                         SELECT 
                             payment_row."is_payment",
                             payment_row."created",
                             payment_row."event_id",
                             payment_row."purchase_id",
                             payment_row."payment_for",
                             payment_row."canceled_date",
                             payment_row."type",
                             payment_row."stripe_payment_type",
                             payment_row."payment_provider",
                             payment_row."source", (
                                CASE 
                                    WHEN (ticket_row."status" = 'canceled') 
                                        THEN ticket_row."status" 
                                    ELSE payment_row."status"
                                END
                             ) "status"
                         FROM "purchase" "ticket_row"
                         INNER JOIN "purchase" "payment_row" 
                             ON "payment_row"."is_payment" IS TRUE
                             AND (
                                 "payment_row"."purchase_id" = "ticket_row"."linked_purchase_id"
                                 OR 
                                 "payment_row"."purchase_id" = "ticket_row"."purchase_id"
                             )
                             AND "payment_row".dispute_status IS DISTINCT FROM 'lost' 
                         WHERE ticket_row."purchase_id" = pt."purchase_id"
                             AND ticket_row."is_ticket" IS TRUE
                             AND ticket_row."status" IS DISTINCT FROM 'canceled'
                             AND ticket_row."canceled_date" IS NULL
                     ) "p" ON TRUE  
                     WHERE pt.event_ticket_id = et.event_ticket_id
                         AND p.payment_for = 'tickets'
                         AND p.event_id = e.event_id
                         AND p.canceled_date IS NULL
                         AND p.status IS DISTINCT FROM 'canceled'
                         AND p.type <> 'waitlist'
                         ${
                             /* whereBlock has checks that should apply to a "payment" row */
                             whereBlock || '' 
                         }
                 ) "p"
             FROM "event" e
             INNER JOIN "event_ticket" et 
                 ON et.event_id = e.event_id
             WHERE e.event_id = $1
             GROUP BY et.event_ticket_id, et.sort_order, e.event_id
         ) "d"`
	);
}

function getScannedUniqueTickets(eventID) {
	const query = `
		WITH ticket_scans AS (
			SELECT
				TO_CHAR((h. "created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
				h.ticket_barcode::int
			FROM
				"event" e
				INNER JOIN swt_api_history h ON h.event_id = e.event_id
			WHERE ((h. "request_url" = '/api/swt/scan'
					AND h. "ticket_barcode" IS NOT NULL))
			AND h. "success"
			AND (h.created AT TIME ZONE 'UTC' AT TIME ZONE e.timezone)::DATE >= e.date_start::DATE
			AND (h.created AT TIME ZONE 'UTC' AT TIME ZONE e.timezone)::DATE <= e.date_end::DATE
			AND e.event_id = $1
			UNION
			SELECT TO_CHAR((h."created" AT TIME ZONE 'UTC' AT TIME ZONE e."timezone")::DATE, 'Mon dd') "date",
				barcodes.ticket_barcode::int
			FROM
				"event" e
				INNER JOIN swt_api_history h ON h.event_id = e.event_id
				INNER JOIN LATERAL (
					SELECT value->>'barcode' "ticket_barcode"
						FROM
							jsonb_array_elements(h.request_body :: JSONB->'tickets')
						WHERE e.tickets_settings->>'require_recipient_name_for_each_ticket' = 'true'
						    AND (h.request_body :: JSONB->>'mark_as_scanned' = 'true'
							OR value->>'mark_as_scanned' = 'true')
                    UNION SELECT h.ticket_barcode
                    WHERE NOT (e.tickets_settings->>'require_recipient_name_for_each_ticket' = 'true')
					) barcodes ON TRUE
            WHERE 
				h."success" 
				AND h.response_body::JSONB ->> 'success' = 'true'
				AND (h.created::DATE AT TIME ZONE 'UTC' AT TIME ZONE e.timezone)::DATE >= e.date_start::DATE
				AND (h.created::DATE AT TIME ZONE 'UTC' AT TIME ZONE e.timezone)::DATE <= e.date_end::DATE
				AND e.event_id = $1
		)
		SELECT
            ticket_scans.date, count(DISTINCT ticket_scans.ticket_barcode)
		FROM
			ticket_scans
        WHERE ticket_scans.ticket_barcode IN (
            SELECT p.ticket_barcode FROM purchase p
            INNER JOIN purchase_ticket pt ON p.purchase_id = pt.purchase_id
            INNER JOIN event_ticket et ON et.event_ticket_id = pt.event_ticket_id
                                              AND et.sub_type = 'default'
                                              AND et.event_id = $1
        )
		GROUP BY
			1
		ORDER BY
			1;
	`

	return Db.query(query, [eventID]).then(({ rows }) => rows || []);
   }
