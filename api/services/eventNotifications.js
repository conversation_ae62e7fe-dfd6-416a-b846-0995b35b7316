'use strict';

const { ENTRY_STATUSES } = require('../constants/teams');
const {
    TEAM_STATUS_CHANGED_TO_DECLINED,
    TEAM_STATUS_CHANGED_TO_ACCEPTED,
    TEAM_STATUS_CHANGED_TO_PENDING,
    TEAM_STATUS_CHANGED_TO_WAITING,
    TEAM_ENTERED_ON_EVENT,
    TEAM_DELETED_FROM_EVENT,
    TEAM_CHANGED_DIVISION,
} = require('../constants/notification-actions');

const FROM = 'SportWrench <<EMAIL>>';
const RED_ROCK_EVENTS = [23008, 23009];
const actions = [
    TEAM_STATUS_CHANGED_TO_DECLINED,
    TEAM_STATUS_CHANGED_TO_ACCEPTED,
    TEAM_STATUS_CHANGED_TO_PENDING,
    TEAM_STATUS_CHANGED_TO_WAITING,
    TEAM_ENTERED_ON_EVENT,
    TEAM_DELETED_FROM_EVENT,
    TEAM_CHANGED_DIVISION
];

module.exports = {
    add_notification: function(event_id, data, cb) {
        const notification_data = data || {};

        const notification = fetchNotificationFromData(event_id, data);

        let query = generateQuery(notification_data.unique, notification);

        return Db.query(query).then(function() {
            if(cb) {
                cb();
            }
        }).catch(err => {
            if(cb) {
                cb(err);
            } else {
                throw err;
            }
        });
    },

    send_notifications_cd: function () {
        return send_to_cd()
            .catch(err => {
                loggers.event_notifications.error(err);

                throw err;
            });
    },

    sendNoticeOfRemoval: function (athletesIdentifiers, staffIdentifiers, masterClubId) {
        let inlineAthletesIdentifiers   = athletesIdentifiers.length?athletesIdentifiers.join(', '):null,
            inlineStaffIdentifiers      = staffIdentifiers.length?staffIdentifiers.join(', '):null;

        return Promise.all([
            Db.query(
                `SELECT "club".* FROM (
                    SELECT 
                        FORMAT(
                            '"%s %s" <%s>', 
                            mc.director_first, 
                            mc.director_last, 
                            mc.director_email
                        ) "receiver", (
                            SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("m")))
                            FROM (
                                SELECT FORMAT('%s %s (%s)', ma.first, ma.last, ma.organization_code) "name"
                                FROM "master_athlete" ma 
                                WHERE ma.master_club_id = mc.master_club_id
                                    AND ma.deleted IS NOT NULL
                                    AND ma.master_athlete_id IN (${inlineAthletesIdentifiers})
                                UNION ALL 
                                SELECT FORMAT('%s %s (%s)', first, last, organization_code) "name"
                                FROM "master_staff" ms
                                WHERE ms.master_club_id = mc.master_club_id
                                    AND ms.deleted IS NOT NULL
                                    AND ms.master_staff_id IN (${inlineStaffIdentifiers})
                            ) "m"
                        ) "members"
                    FROM "master_club" mc
                    WHERE mc."master_club_id" = $1
                ) "club" 
                WHERE "club".members IS NOT NULL`,
                [masterClubId]
            ), 
            Db.query(
                `SELECT * FROM (
                    SELECT (
                            CASE
                                WHEN e.notify_emails IS NOT NULL 
                                THEN e.notify_emails
                                ELSE u.email
                            END 
                        ) "receiver",
                        e.long_name "event_name", (
                            SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("m")))
                            FROM (
                                SELECT 
                                    FORMAT('%s %s (%s)', ma.first, ma.last, ma.organization_code) "name", 
                                    STRING_AGG(rt.team_name, ', ') "teams"
                                FROM "master_athlete" ma 
                                INNER JOIN "roster_athlete" ra 
                                    ON ra.master_athlete_id = ma.master_athlete_id
                                    AND ra.deleted IS NOT NULL
                                INNER JOIN "roster_team" rt 
                                    ON rt.roster_team_id = ra.roster_team_id
                                    AND rt.deleted IS NULL 
                                    AND rt.status_entry <> 11
                                    AND rt.event_id = e.event_id 
                                    AND rt.roster_club_id = rc.roster_club_id
                                WHERE ma.master_athlete_id IN (${inlineAthletesIdentifiers})
                                GROUP BY ma.master_athlete_id
                                UNION ALL 
                                SELECT 
                                    FORMAT('%s %s (%s)', ms.first, ms.last, ms.organization_code) "name",
                                    STRING_AGG(rt.team_name, ', ') "teams"
                                FROM "master_staff" ms 
                                INNER JOIN "roster_staff_role" rsr 
                                    ON rsr.master_staff_id = ms.master_staff_id
                                    AND rsr.deleted IS NOT NULL 
                                INNER JOIN "roster_team" rt 
                                    ON rt.roster_team_id = rsr.roster_team_id
                                    AND rt.deleted IS NULL 
                                    AND rt.status_entry <> 11
                                    AND rt.event_id = e.event_id 
                                    AND rt.roster_club_id = rc.roster_club_id
                                WHERE ms.master_staff_id IN (${inlineStaffIdentifiers})
                                GROUP BY ms.master_staff_id
                            ) "m"
                        ) "members",
                        rc.club_name
                    FROM "roster_club" rc
                    INNER JOIN "event" e 
                        ON e.event_id = rc.event_id 
                        AND e.date_start >= (NOW() AT TIME ZONE e.timezone) 
                    INNER JOIN "event_owner" eo 
                        ON eo.event_owner_id = e.event_owner_id
                    INNER JOIN "user" u 
                        ON u.user_id = eo.user_id
                    WHERE rc.master_club_id = $1
                ) "events"
                WHERE "events".members IS NOT NULL`,
                [masterClubId]
            )
        ]).then(results => {
            let clubDirector    = _.first(results[0].rows),
                eventOwners     = results[1].rows;

            return Promise.all([
                Promise.all(
                    eventOwners.map(eo => {
                        return removalNotification(
                            eo.receiver, `Removed Members from club on ${eo.event_name}`,
                            eo, 'notifications/member_removal/eo'
                        );
                    })
                ),
                (_.isEmpty(clubDirector)?null:removalNotification(
                    clubDirector.receiver, 'Members removed', clubDirector, 'notifications/member_removal/cd'))
            ]);
        });
    },

    setEventChangeRowsPublished: function (systemJobID) {
        return Db.query(`UPDATE event_change SET published = TRUE WHERE system_job_id = $1`, [systemJobID]);
    }
};

function generateQuery (uniqueFields, notification) {
    let fieldNames = Object.keys(notification);

    let subQuery = fieldNames.reduce((all, field) => {
        return all.field(squel.str('?', notification[field]));
    }, squel.select());

    if(!_.isEmpty(uniqueFields)) {
        let existsQuery = Object.keys(uniqueFields).reduce((all, field) => {
            return all.where(`ech.${field} = ?`, uniqueFields[field])
        }, squel.select().from('event_change', 'ech').limit(1));

        subQuery.where(`NOT EXISTS (${existsQuery})`);
    }

    return squel.insert().into('event_change').fromQuery(fieldNames, subQuery);
}

async function send_to_cd () {
    const clubs = await getClubsData();

    if(_.isEmpty(clubs)) {
        return;
    }

    return Promise.all(clubs.map(async (club) => {
        await sendNotification(club);
        updateEventChange(club)
    }))
}

function removalNotification (receiver, subject, templateData, template) {
    return EmailService.renderAndSend({
        template    : template,
        data        : templateData,
        from        : FROM,
        to          : receiver,
        subject     : subject
    });
}

function fetchNotificationFromData(event_id, data) {
    return Object.assign(
        {
            event_id,
            action: data.action || 'default action',
        },
        _.pick(data, [
            'user_id',
            'roster_club_id',
            'roster_team_id',
            'purchase_id',
            'division_id',
            'club_director_id',
            'event_owner_id',
            'comments',
            'official_id',
            'old_housing_status',
            'new_housing_status',
            'published',
            'event_email_id',
            'system_job_id',
            'master_staff_id',
            'new_data',
            'old_data',
        ])
    );
}

function getClubsData () {
    const query =
        `SELECT 
            rc.club_name, rc.roster_club_id, mc.director_first, mc.administrative_email,
            mc.director_last, mc.director_email, e.long_name AS event_name, 
            e.rules_website, e.website, e."event_id",
            MAX(ec.created) last_change, (
                CASE 
                    WHEN e.email IS NOT NULL 
                    THEN FORMAT('"%s" <%1$s>', e.email) 
                    ELSE NULL 
                END 
            ) "event_email", ( 
                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(teams))), '[]'::JSON)
                 FROM ( 
                     SELECT rt.team_name, d.name "division_name", status_entry  
                     FROM roster_team rt  
                     LEFT JOIN division d  
                         ON d.division_id = rt.division_id  
                     WHERE rc.roster_club_id = rt.roster_club_id  
                        AND rc.event_id = rt.event_id 
                        AND rt.deleted IS NULL  
                     ORDER BY (
                        CASE 
                            WHEN status_entry = ${ENTRY_STATUSES.ACCEPTED} THEN 1 
                            WHEN status_entry = ${ENTRY_STATUSES.PENDING} THEN 2 
                            WHEN status_entry = ${ENTRY_STATUSES.WAITLIST} THEN 3 
                            WHEN status_entry = ${ENTRY_STATUSES.DECLINED} THEN 4 
                        END
                     ), organization_code ASC 
                ) teams  
            ) "roster_teams" 
        FROM roster_club rc 
        INNER JOIN event_change ec 
            ON ec.roster_club_id = ec.roster_club_id 
            AND ec.created > (NOW() - '1 day'::INTERVAL) 
            AND ec."action" IN (
                '${actions.join("', '")}'
            ) 
            AND ec.sent_to_club IS NULL  
            AND ec.roster_club_id = rc.roster_club_id 
        LEFT JOIN master_club mc 
            ON mc.master_club_id = rc.master_club_id 
        LEFT JOIN "event" e 
            ON e.event_id = rc.event_id 
        GROUP BY rc.club_name, rc.roster_club_id, mc.director_first,  mc.administrative_email,  
            mc.director_last, mc.director_email, e.long_name,  
            e.rules_website, e.website, e.email, e."event_id"
        HAVING MAX(ec.created) < (NOW() - '10 min'::INTERVAL)`

    return Db.query(query).then(({rows}) => rows)
}

function sendNotification(club) {
    if (!club.roster_teams || club.roster_teams.length === 0) {
        return;
    }

    // Don't send email if director_email is empty
    if (!club.director_email) {
        return;
    }

    let receiver = `${club.director_first} ${club.director_last} <${club.director_email}>`;

    if(club.administrative_email) {
        receiver += `, ${club.director_first} ${club.director_last} <${club.administrative_email}>`;
    }

    let template = 'cd_notification'

    if(RED_ROCK_EVENTS.includes(club.event_id)) {
        template =  'cd_notification_red_rock'
    }

    return EmailService.renderAndSend({
        template,
        data: {
            club: club
        },
        from: 'SportWrench <<EMAIL>>',
        to: receiver,
        replyto: club.event_email,
        subject: 'Message from "' + club.event_name + '" about your teams'
    })
}

function updateEventChange(club) {
    loggers.sent_notifications.info(club);

    const upd_query =
        `UPDATE event_change
        SET sent_to_club = NOW()
        WHERE "action" IN ('${actions.join("', '")}')
            AND sent_to_club IS NULL
            AND roster_club_id = $1`;

    return Db.query(upd_query, [club.roster_club_id])
}

