
const AbstractStorageService = require('./AbstractStorageService');
const { refundSchema } = require('../../../validation-schemas/sales-hub/refund');

class RefundStorageService extends AbstractStorageService {
    constructor(DbClient) {
        super(DbClient);
    }

    async create(data) {
        const { error: validationError } = refundSchema.validate(data);

        if (validationError) {
            throw { validation: validationError.details[0].message };
        }

        const query = knex('sales_hub_refund')
            .insert({
                id_at_sales_hub: data.id,
                sales_hub_payment_id: data.paymentId,
                amount: data.amount,
                marketplace_fee: data.marketplaceFee,
                status: data.status,
                is_manual: data.isManual,
            });

        const { rowCount } = await this.DbClient.query(query);

        if(rowCount <= 0) {
            throw new Error('Sales Hub refund failed to create created');
        }
    }

}

module.exports = RefundStorageService;
