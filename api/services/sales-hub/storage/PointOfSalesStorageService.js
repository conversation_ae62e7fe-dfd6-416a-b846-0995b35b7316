
const AbstractStorageService = require('./AbstractStorageService');
const {DEFAULT_SW_EVENT} = require("../../../constants/ncsa");

class PointOfSalesStorageService extends AbstractStorageService{
    constructor(DbClient) {
        super(DbClient);
    }

    #UNIQUE_POINT_OF_SALES_TYPE_SW_EVENT_ID_CONSTRAINT = 'unique_point_of_sales_type_sw_event_id';
    #UNIQUE_POINT_OF_SALES_ID = 'unique_point_of_sales_id';

    async getEventDataForPointOfSales(eventID) {
        const query = knex('event as e')
            .select({
                name: 'e.long_name',
                shortName: 'e.name',
                marketplaceFee: knex.raw(`(COALESCE(e.tickets_sw_fee, 0) * 100)::INTEGER`),
                marketplaceFeePayer: 'e.tickets_sw_fee_payer',
                dateStart: knex.raw(`TO_CHAR(e.date_start::TIMESTAMP AT TIME ZONE e.timezone, 'YYYY-MM-DD HH24:MI:SS')`),
                dateEnd: knex.raw(`TO_CHAR(e.date_end::TIMESTAMP AT TIME ZONE e.timezone, 'YYYY-MM-DD HH24:MI:SS')`),
                salesStartDate: knex.raw(
                    `TO_CHAR(e.tickets_purchase_date_start::TIMESTAMP AT TIME ZONE e.timezone, 'YYYY-MM-DD HH24:MI:SS')`
                ),
                salesEndDate: knex.raw(
                    `TO_CHAR(e.tickets_purchase_date_end::TIMESTAMP AT TIME ZONE e.timezone, 'YYYY-MM-DD HH24:MI:SS')`
                ),
                published: 'e.tickets_published',
                show_ncsa_athlete_form: knex.raw(`COALESCE((e.tickets_settings ->> 'show_ncsa_athlete_form')::BOOLEAN, false)`),
                description: 'e.tickets_description',
                disclaimer: 'e.tickets_receipt_descr',
                timezone: 'e.timezone',
                location: 'e.tickets_locations',
                website: 'e.website',
                city: 'e.city',
                state: 'e.state',
                zip: 'e.zip',
                email: 'e.email',
                phone: 'e.hosting_org_phone',
                country: 'e.country',
                address: 'e.address',
                allow_point_of_sales: knex.raw(`
                    COALESCE((e.tickets_settings ->> 'allow_point_of_sales')::BOOLEAN, false)
                `),
                entryCodeRequired: knex.raw(`
                    COALESCE((SELECT eec.is_active
                    FROM event_ticket_buy_entry_code_settings eec
                    WHERE eec.event_id = e.event_id), FALSE)
                `)
            })
            .where('e.event_id', eventID);

        const { rows: [event] } = await this.DbClient.query(query);

        if(_.isEmpty(event)) {
            throw { validation: 'Event Not Found' };
        }

        return this.mapEventToPointOfSales(event);
    }

    mapEventToPointOfSales(eventRow) {
        const { show_ncsa_athlete_form, ...pointOfSales } = eventRow;

        return {
            ...pointOfSales,
            customSettings: {
                showNcsaAthleteForm: show_ncsa_athlete_form
            },
        }
    }

    async createEventPointOfSales(SWEventID, pointOfSalesID, pointOfSalesType) {
        const query = knex('event_point_of_sales')
            .insert({
                sw_event_id: SWEventID,
                point_of_sales_id: pointOfSalesID,
                point_of_sales_type: pointOfSalesType
            });

        try {
            const { rowCount } = await this.DbClient.query(query);

            if(rowCount <= 0) {
                throw new Error('Event Point Of Sales does not created');
            }
        } catch (err) {
            if(Db.utils.isUniqueConstraintViolation(err, this.#UNIQUE_POINT_OF_SALES_TYPE_SW_EVENT_ID_CONSTRAINT)) {
                throw {
                    validation:
                        `Point Of Sales with given type ${pointOfSalesType} already exists for event ${SWEventID}`
                };
            }

            if(Db.utils.isUniqueConstraintViolation(err, this.#UNIQUE_POINT_OF_SALES_ID)) {
                throw { validation: `Point Of Sales with ID ${pointOfSalesID} already exists` };
            }

            throw err;
        }
    }

    async getEventPointOfSales(eventID, pointOfSalesType) {
        const query = knex('event_point_of_sales')
            .select(
                'point_of_sales_id',
                'sw_event_id',
                'point_of_sales_type'
                )
            .where('sw_event_id', eventID)
            .where('point_of_sales_type', pointOfSalesType);

        const { rows: [pointOfSales] } = await this.DbClient.query(query);

        return pointOfSales;
    }
}

module.exports = PointOfSalesStorageService;
