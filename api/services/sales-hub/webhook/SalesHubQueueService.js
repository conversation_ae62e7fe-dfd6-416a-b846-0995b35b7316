const Queue = require('bull');
const { SALES_HUB_WEBHOOK_QUEUE } = require('../../../constants/workers-queue');

class SalesHubQueueService {
    constructor() {
        this.queue = new Queue(SALES_HUB_WEBHOOK_QUEUE, sails.config.redis_queue.workers_queue);
    }

    async addJob(webhookData, options = {}) {
        await this.queue.add(webhookData, {
            ...options,
            backoff: { type: 'webhookBackoff' },
        });
    }

    async moveToDeadLetterQueue(queueName, payload) {
        throw new Error('Not implemented');
    }

    async close() {
        return this.queue.close();
    }
}

module.exports = new SalesHubQueueService();
