'use strict';

const fetch         = require('node-fetch');
const querystring   = require('querystring');
const xml           = require('nodexml');

class WebpointReceiverV2 {

    constructor () {
        this.source = null;
    }

    get WP_URI () {
        return sails.config.webpoint.uri + '?' + sails.config.webpoint.api_key_param;
    }

    get MEMBER_ID_REGEX () {
        return /^[0-9]{7}$/;
    }

    get PARAM_NAME () {
        return {
            MEMBER_ID   : 'MemberID',
            CLUB_ID     : 'ClubID',
            USR_NAME    : 'UsrName',
            PASSWORD    : 'password',
            USAV_NUMBER : 'USAVNumber',
            CLUB_CODE   : 'Club_Code',
            CHANGE_DATE : 'ChangeDateFrom'
        }
    }

    get ERROR_MESSAGE () {
        return {
            INCORRECT_USAV              : 'Member USAV number is incorrect',
            USER_NAME_REQUIRED          : 'Club member user name required',
            PASSWORD_REQUIRED           : 'Club member password required',
            USAV_NAME_OR_PASS_NOT_MATCH : `USAV Username or Password doesn't match`,
            MEMBER_NOT_FOUND            : 'Member not found',
            CLUB_ID_NOT_FOUND           : 'Club ID not found'
        }
    }

    get DEFULT_WP_API_METHOD () {
        return 'GET';
    }

    get RESPONSE_KEY () {
        return 'RESPONSE';
    }

    get RESPONSE_CODE_KEY () {
        return 'Code';
    }

    get RESPONSE_MESSAGE_KEY () {
        return 'Msg';
    }

    get SUCCESS_CODE () {
        return '0';
    }

    get REQUEST_SOURCE () {
        return {
            CLUB    : 'club',
            MEMBER  : 'member'
        }
    }

    get WP_ERROR_CODE () {
        return {
            INSUFFICIENT_PARAMS_ERROR   : 3,
            NO_MEMBERS_FOUND            : 2,
            NO_RECORDS_FOUND            : 1,
            INVALID_PROXY_CODE          : 23
        }
    }

    set requestSource (source) {
        this.source = source
    }

    get requestSource () {
        return this.source;
    }

    getMember (usav) {
        usav = usav && WebpointUtility.getMembershipNumber(usav);

        if(!usav || !this.MEMBER_ID_REGEX.test(usav)) {
            return Promise.reject({ validation: this.ERROR_MESSAGE.INCORRECT_USAV });
        }

        this.requestSource = this.REQUEST_SOURCE.MEMBER;

        return this.__getWPData__({ [this.PARAM_NAME.MEMBER_ID]: usav })
            .then(WebpointUtility.convertMemberWPApiResponse);
    }

    getClubMembers (userName, password) {
        if(!userName) {
            return Promise.reject({ validation: this.ERROR_MESSAGE.USER_NAME_REQUIRED });
        }

        if(!password) {
            return Promise.reject({ validation: this.ERROR_MESSAGE.PASSWORD_REQUIRED });
        }

        this.requestSource = this.REQUEST_SOURCE.CLUB;

        return this.getClubOwnerData(userName, password)
            .then(response => this.getClubInfo(response && response[0][this.PARAM_NAME.CLUB_ID]));
    }

    __getWPData__ (params) {
        let options = { method  : this.DEFULT_WP_API_METHOD };

        return fetch(`${this.WP_URI}&${querystring.stringify(params)}`, options)
            .then(this.__parseWebpointResponse__.bind(this));
    }

    getClubOwnerData (userName, password) {
        return this.__getWPData__({
            [this.PARAM_NAME.USR_NAME]: userName,
            [this.PARAM_NAME.PASSWORD]: password
        }, false)
    }

    getClubInfo (clubID, changeDateFrom) {
        if(!clubID) {
            throw new Error(this.ERROR_MESSAGE.CLUB_ID_NOT_FOUND)
        }

        let params = { [this.PARAM_NAME.CLUB_ID]: clubID };

        if(changeDateFrom) {
            params[this.PARAM_NAME.CHANGE_DATE] = changeDateFrom;
        }

        return this.__getWPData__(params)
            .then(WebpointUtility.convertClubWPApiResponse);
    }

    __parseWebpointResponse__ (response) {
        return response.text()
            .then(text => {
                try {
                    let resp = JSON.parse(text);

                    if(resp.result === "error") {
                        return this.__processJsonError__(resp)
                    }

                    return Promise.resolve(resp);
                }
                catch (e) {
                    return this.__processError__(text);
                }
            })
    }

    __processJsonError__ (error) {
        // original error message "No matching records found" for code 1
        // original error message "Insufficient search parameters provided" for code 3
        if(Number(error.code) === this.WP_ERROR_CODE.NO_RECORDS_FOUND
            || Number(error.code) === this.WP_ERROR_CODE.INSUFFICIENT_PARAMS_ERROR) {

            let message = this.requestSource === this.REQUEST_SOURCE.CLUB
                ? this.ERROR_MESSAGE.USAV_NAME_OR_PASS_NOT_MATCH
                : this.ERROR_MESSAGE.MEMBER_NOT_FOUND;

            return Promise.reject({ validation: message });
        }

        if(Number(error.code) === this.WP_ERROR_CODE.INVALID_PROXY_CODE) {
            return Promise.reject({ validation: error.message });
        }

        // original error message "No members found"
        if(Number(error.code) === this.WP_ERROR_CODE.NO_MEMBERS_FOUND) {
            return Promise.reject({ validation: error.message });
        }
    }

    __extractClubIDFromResponse__ (response) {
        let clubID = response && response[0] && response[0][this.PARAM_NAME.CLUB_ID];

        if(!clubID) {
            throw new Error(this.ERROR_MESSAGE.CLUB_ID_NOT_FOUND)
        }

        return clubID;
    }

    __processError__ (responseText) {
        // sometime we got back XML object or even plain text
        // both possibly mean some error occurred
        let res = xml.xml2obj(responseText);

        if(this.__isErrorInResponseObject__(res)) {
            throw new Error(res[this.RESPONSE_KEY][this.RESPONSE_MESSAGE_KEY]);
        }

        /* some errors are of form
            [ Error 1 - No matching records found ]
            [ Error 3 - Insufficient search parameters provided. ]
            [ Error 309 - No members found ]
        */

        let errorPattern = /(\[ Error [^\]]+\])/g;
        let error = _.first(res.toString().match(errorPattern));

        if(error) {
            throw new Error(error);
        }

        // failed to humanize parse error
        throw new Error(responseText);
    }

    __isErrorInResponseObject__ (response) {
        return response[this.RESPONSE_KEY] && response[this.RESPONSE_KEY][this.RESPONSE_CODE_KEY] != this.SUCCESS_CODE
            && response[this.RESPONSE_KEY][this.RESPONSE_MESSAGE_KEY]
    }

}

module.exports = new WebpointReceiverV2();
