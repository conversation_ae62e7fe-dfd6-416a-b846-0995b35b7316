
const OfficialCheckinService = require('./event-checkin-api/__OfficialCheckinService');

const CHECKIN_TYPE = {
    OFFICIAL: 'official',
};

class EventCheckinAPIService {
    constructor () {
        this[this.CHECKIN_TYPE.OFFICIAL] = OfficialCheckinService;
    }

    get CHECKIN_TYPE () {
        return CHECKIN_TYPE;
    }

    isValidCheckinType (checkinType) {
        return !checkinType || Object.values(this.CHECKIN_TYPE).includes(checkinType);
    }

    getEvents (checkinType) {
        if(!this.isValidCheckinType(checkinType)) {
            throw { validation: 'Checkin type is not valid' };
        }

        return this[checkinType].eventsList();
    }

    async scan (checkinType, eventID, barcode) {
        if(!this.isValidCheckinType(checkinType)) {
            throw { validation: 'Checkin type is not valid' };
        }

        const official = await this[checkinType].scan(eventID, barcode);

        await this[checkinType].addScanHistory(eventID, barcode);

        return official;
    }
}

module.exports = new EventCheckinAPIService();
