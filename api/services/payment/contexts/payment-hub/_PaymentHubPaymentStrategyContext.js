const AbstractPaymentStrategyContext = require("../_AbstractPaymentStrategyContext");
const SucceededPaymentStrategy = require("./_SucceedPaymentStrategy");

class PaymentHubPaymentStrategyContext extends AbstractPaymentStrategyContext {
    constructor(sessionPayment, paymentData, webhookData) {
        super(sessionPayment, paymentData, webhookData) 

        switch (webhookData.data.status) {
            case 'settled':
                this.strategy = new SucceededPaymentStrategy(sessionPayment, paymentData, webhookData);
                break;
            default:
                throw new Error('Invalid payment status');
        }
    }
}

module.exports = PaymentHubPaymentStrategyContext