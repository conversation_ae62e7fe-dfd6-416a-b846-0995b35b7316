const swUtils = require('../../../../../lib/swUtils');
const StripeTeamsPaymentDataService = require('../../data/_StripeTeamsPaymentDataService');
const TeamsPaymentNotificationsService = require('../../notification/_TeamsPaymentNotificationsService');
const AbstractFailedPaymentProcessor = require('../_AbstractFailedPaymentProcessor');

class StripeFailedPaymentProcessor extends AbstractFailedPaymentProcessor {
    async __updatePaymentData(tr, webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData)
        const charge = this.__getCharge(webhookData);

        if (_.isEmpty(charge)) {
            throw new Error('Charge not found!');
        }

        return Promise.all([
            this.__updatePaymentIntent(tr, paymentIntent.id, charge),
            StripeService.webhook.charge.upsertStripeChargeRow(charge),
        ])
    }

    async __insertHistoryRow(tr, purchaseId, webhookData) {
        const charge = this.__getCharge(webhookData);

        const paymentMethodLabel = charge.payment_method_details.type === 'us_bank_account' ? 'ACH' : 'Card';

        let description = `${paymentMethodLabel} Payment failed with code: "${charge.failure_code}". 
            Reason: "${charge.failure_message}"`;

        let action = 'purchase.failed';

        return this.teamsPaymentDataService.insertHistoryRow(tr, { purchaseId, webhookData, action, description });
    }

    async __updatePaymentIntent(tr, paymentIntentID, charge) {
        return this.teamsPaymentDataService.updatePaymentData(
            tr, paymentIntentID,
            {
                payment_intent_status: 'requires_payment_method',
                stripe_charge_id: charge.id,
                stripe_fee: swUtils.normalizeNumber(charge.application_fee_amount / 100),
            }
        )
    }

    async __sendNotifications(purchase, webhookData) {
        return TeamsPaymentNotificationsService.sendFailedACHNotification(this.__getCharge(webhookData), purchase);
    }

    __getPaymentIntent(webhookData) {
        return webhookData.data.object;
    }

    __getCharge(webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData)
        return paymentIntent?.charges?.data?.[0];
    }
}

module.exports = new StripeFailedPaymentProcessor(StripeTeamsPaymentDataService);
