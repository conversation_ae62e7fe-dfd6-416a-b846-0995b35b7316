const StripeTeamsPaymentDataService = require("../../data/_StripeTeamsPaymentDataService");
const AbstractSuccessPendingPaymentProcessor = require('../_AbstractSuccessPendingPaymentProcessor');

class StripeSuccessPendingPaymentProcessor extends AbstractSuccessPendingPaymentProcessor {
    async __updatePaymentData(tr, webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData)
        const charge = this.__getCharge(webhookData);

        return Promise.all([
            StripeService.webhook.charge.updateRow({ chargeID: charge.id, stripeAccountID: charge.destination }),
            this.teamsPaymentDataService.updatePaymentData(tr, paymentIntent.id, { payment_intent_status: 'succeeded' })
        ])
    }

    __getPurchaseUpdateData(webhookData) {
        const charge = this.__getCharge(webhookData);

        return {
            stripe_charge_id: charge.id,
            email: charge.billing_details?.email,
        };
    }

    __getPaymentIntent(webhookData) {
        return webhookData.data.object;
    }

    __getCharge(webhookData) {
        const paymentIntent = this.__getPaymentIntent(webhookData)
        return paymentIntent?.charges?.data?.[0];
    }
}

module.exports = new StripeSuccessPendingPaymentProcessor(StripeTeamsPaymentDataService);
