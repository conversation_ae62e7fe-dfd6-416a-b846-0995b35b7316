const knex      = require('knex')({client: 'pg'});
const swUtils   = require('../../../../lib/swUtils');

class CashService {

    constructor(CancellationService) {
        this.CancellationService = CancellationService;
    }

    async refundPaidTickets (payment, ticketToRefund, otherTickets, eventID, userID) {
        if(payment.type !== this.CancellationService.PURCHASE_TYPE_CASH) {
            throw { validation: 'Only cash purchase can be canceled' }
        }

        let isLastTicketRefund = !otherTickets.length;

        let tr;

        try {
            tr = await Db.begin();

            if(isLastTicketRefund) {
                await this.__processFullCancellation({payment, ticketToRefund, eventID, userID, tr});
            } else {
                await this.__processTicketCancellation({payment, ticketToRefund, eventID, tr});
            }

            await this.CancellationService.savePurchaseCancellationActionToHistory(ticketToRefund, userID, tr);

            return tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err
        }
    }

    __processFullCancellation ({payment, ticketToRefund, eventID, userID, tr}) {
        let ticketPurchaseID    = ticketToRefund.purchase_id;
        let paymentPurchaseID   = payment.purchase_id;

        return Promise.all([
            this.CancellationService.cancelPurchase([ticketPurchaseID, paymentPurchaseID], tr),
            this.CancellationService.cancelPurchaseTickets([ticketPurchaseID], tr),
            this.__updateTicketsBalance(eventID, payment.collected_sw_fee, tr),
            this.CancellationService.savePurchaseCancellationActionToHistory(payment, userID, tr)
        ])
    }

    __processTicketCancellation ({payment, ticketToRefund, eventID, tr}) {
        let ticketPurchaseID = ticketToRefund.purchase_id;

        return Promise.all([
            this.__updatePaymentRow(payment, ticketToRefund, tr),
            this.CancellationService.cancelPurchaseTickets([ticketPurchaseID], tr),
            this.__updateTicketsBalance(eventID, ticketToRefund.app_fee, tr),
            this.CancellationService.cancelPurchase([ticketPurchaseID], tr)
        ])
    }

    __updatePaymentRow (payment, ticketToRefund, tr) {
        let applicationFee  = swUtils.normalizeNumber(payment.collected_sw_fee - ticketToRefund.app_fee);
        let amount          = swUtils.normalizeNumber(payment.amount - ticketToRefund.amount);
        let netProfit       = swUtils.normalizeNumber(amount - applicationFee);
        let amountRefunded  = swUtils.normalizeNumber(payment.amount_refunded + ticketToRefund.amount);

        let query = knex('purchase AS p')
            .update({
                collected_sw_fee: applicationFee,
                amount          : amount,
                net_profit      : netProfit,
                date_refunded   : knex.fn.now(),
                amount_refunded : amountRefunded
            })
            .where('p.purchase_id', payment.purchase_id)
            .where('p.status', '<>', this.CancellationService.PURCHASE_STATUS_CANCELED)
            .whereNull('canceled_date');

        return tr.query(query);
    }

    __updateTicketsBalance (eventID, swFeeCanceled, tr) {
        let query = knex('event AS e')
            .update({
                tickets_sw_balance: knex.raw(`COALESCE(e.tickets_sw_balance, 0) + ?`, [swFeeCanceled])
            })
            .where('e.event_id', eventID);

        return tr.query(query);
    }
}

module.exports = CashService;
