const DEFAULT_SENDER = '"SportWrench" <<EMAIL>>';
const SW_DEBUG_RECEIVER = '"sw debug" <<EMAIL>>';
const SW_ADMIN_RECEIVER = '<EMAIL>';

class UnresolvedMembershipNotificationService {
    constructor() {}

    get DEFAULT_SENDER() {
        return DEFAULT_SENDER;
    }
    get SW_DEBUG_RECEIVER() {
        return SW_DEBUG_RECEIVER;
    }
    get SW_ADMIN_RECEIVER() {
        return SW_ADMIN_RECEIVER;
    }

    async sendUnresolvedMembershipNotifications (unresolvedMemberships) {
        const memberships = await Promise.all(unresolvedMemberships.map(async (unresolvedMembership) => {
            return this._prepareUnresolvedMembershipData(unresolvedMembership)
        })).catch((error) => loggers.errors_log.error(error))

        return EmailService.renderAndSend({
            template    : 'notifications/unresolved_membership',
            from        : this.DEFAULT_SENDER,
            to          : this.SW_ADMIN_RECEIVER,
            cc          : this.SW_DEBUG_RECEIVER,
            subject     : 'Notification about not imported members from SE',
            data        : {memberships},
        })
        .catch(ErrorSender.defaultError.bind(ErrorSender));
    }

    async _prepareUnresolvedMembershipData(unresolvedMembership) {
        const {club_name, club_director_email} =
            await this._getMasterClubInfo(unresolvedMembership.region, unresolvedMembership.club_code)

        return {
            club_name,
            club_director_email,
            membership_number: unresolvedMembership.organization_code,
            membership_definition_id: unresolvedMembership.membership_definition_id,
            membership_name: unresolvedMembership.membership_name,
            membership_status: unresolvedMembership.membership_status,
            membership_end_date: unresolvedMembership.membership_end_date,
            member_type: unresolvedMembership.age_group,
            error_message: unresolvedMembership.error_message,
        };
    }

    _getMasterClubInfo (region, club_code) {
        let query = knex('master_club AS mc')
            .select(
                'mc.club_name',
                'mc.director_email as club_director_email',
            )
            .where('mc.region', region)
            .where('mc.code', club_code)

        return Db.query(query).then(({ rows: [club] } )=> {
            if (_.isEmpty(club)) {
                throw new Error(`Club not found (region: ${region}, club_code: ${club_code})`);
            }

            return club;
        });
    }

}

module.exports = UnresolvedMembershipNotificationService;
