class ImportProgressService {
    get PROCESSES() {
        return {
            MANUAL_VIP_TICKETS: 'manual-vip-tickets',
        };
    }
    get STATUSES() {
        return {
            RUNNING: 'running',
            FINISHED: 'finished',
            ERROR: 'error',
        };
    }

    async createImport(process, eventID, progress = null) {
        this._validateProcess(process);
        const { import_id } = await Db.query(
            knex('import').insert({
                process: knex.raw('?::import_process', process),
                event_id: eventID,
                progress,
            })
                .returning('import_id')
        ).then(result => result.rows[0]);

        return import_id;
    }

    async updateImport(id, status, progress=null, output=null) {
        this._validateStatus(status);
        const updatedFields = {
            status: knex.raw('?::import_status', status),
        };
        if(progress !== null) {
            updatedFields.progress = progress;
        }
        if(output !== null) {
            updatedFields.output = output;
        }
        await Db.query(
            knex('import').update(updatedFields)
                .where('import_id', id)
        )
    }

    async getImport(id, eventID) {
        const result = await Db.query(
                knex('import')
                    .select([
                        'progress',
                        'status',
                        'output',
                    ])
                    .where('import_id', id)
                    .where('event_id', eventID)
            );

        return result.rows[0];
    }

    _validateProcess(process) {
        if(!Object.values(this.PROCESSES).includes(process)) {
            throw { validation: 'invalid process name' };
        }
    }

    _validateStatus(status) {
        if(!Object.values(this.STATUSES).includes(status)) {
            throw { validation: 'invalid status' };
        }
    }
}

module.exports = new ImportProgressService();
