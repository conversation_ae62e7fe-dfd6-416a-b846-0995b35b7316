'use strict';

const config = sails.config.rekognition;
const fetch = require('node-fetch');

class RekognitionService {
    get VERIFICATION_ACTIONS() {
        return {
            ID: 'id',
            NEUTRAL_FACE: 'neutral',
            SMILING_FACE: 'smile',
        };
    }

    get RECOGNITION_ERROR_TEXT() {
        return {
            1001: 'Please rescan your ID. A face was not found.',
            1002: 'Please try verification again. ID / Selfie mismatch.',
            1003: 'Session not found. Please logout and back in.',
            1004: 'Failed to decode base64 data. Contact support.',
            1005: 'Bad quality of ID image. Please rescan.',
            1006: 'Failed to parse ID srv#1. Try again or contact support.',
            1007: 'Failed to parse ID srv#2. Try again or contact support.',
            1008: "Srv#1 didn't find any useful info on ID. Try again or contact support.",
            1009: "Srv#2 didn't find any useful info on ID. Try again or contact support.",
            1010: "Srv#1 didn't match Srv#2. Try again or contact support.",
            1011: 'Invalid image',
            1012: "Face wasn't detected.",
            1013: "Face emotion didn't match requested. Please Try again.",
            1014: 'Error storing face image',
            1015: 'Invalid API key',
            1016: 'Invalid request',
        };
    }

    init() {
        return this.__sendRequest({});
    }

    handleAction({ action, image, session_id }) {
        this.__validateAction(action);

        return this.__sendRequest({
            action,
            session: session_id,
            b64data: image,
        });
    }

    __validateAction(action) {
        const actions = Object.values(this.VERIFICATION_ACTIONS);

        const isValid = actions.some(
            (verificationAction) => verificationAction === action
        );

        if (!isValid) {
            throw new Error('Not a valid rekognition action');
        }
    }

    __getDefaultHeaderOptions() {
        const headers = new fetch.Headers();

        headers.set('Content-Type', 'application/json');
        headers.set(config.AUTH_HEADER_KEY, config.API_KEY);

        return headers;
    }

    async __sendRequest(payload) {
        const options = {
            headers: this.__getDefaultHeaderOptions(),
            body: JSON.stringify(payload),
            method: 'POST',
        };

        const response = await fetch(config.API_URL, options);

        if (!response.ok) {
            loggers.errors_log.error(`[REKOGNITION] Error sending request`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: options.body,
            });

            throw Error('Error sending request to rekognition');
        }

        const { ok, data } = await response.json();

        if (!ok) {
            return { error: this.__getErrorMessage(data) };
        }

        return { data };
    }

    __getErrorMessage(error) {
        const errorMessage =
            this.RECOGNITION_ERROR_TEXT[error.code] || error.message;

        if (!errorMessage) {
            return 'Unexpected error';
        }

        return errorMessage;
    }
}

module.exports = new RekognitionService();
