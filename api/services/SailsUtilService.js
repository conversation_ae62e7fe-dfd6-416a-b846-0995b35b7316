const nodeutil = require('util');

/**
 * SailsUtilService
 * @description :: This service is used to provide back propagation for the sails.js framework.
 */
class SailsUtilService {
    /**
     * Normalize an error or array of errors into an array of proper, readable Errors
     *
     * @param {String|Object|Error|Array} errOrErrs
     * @returns {Array[Error]}
     *
     * @api private
     */
    normalizeErrors(errOrErrs) {

        // If `errOrErrs` is not an array already, make it one
        const errorsToDisplay = _.isArray(errOrErrs) ? errOrErrs : [errOrErrs];

        // Ensure that each error is formatted correctly
        return _.map(errorsToDisplay, function (e, i) {
            let displayError;

            // Make error easier to read, and normalize its type
            if (e instanceof Error) {
                displayError = e;
            }

                // Create an error ad hoc
            // (but save reference to original)
            else {
                displayError = new Error( nodeutil.inspect(e) );
                displayError.original = e;
            }

            return displayError;
        });
    }


}

module.exports = new SailsUtilService();
