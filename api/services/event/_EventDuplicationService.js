'use strict';

const {RESOURCES_TO_DUPLICATE, EMAIL_TEMPLATE_INSERT_LIMIT} = require("../../constants/event-duplication");
const Queue = require("bull");
const {EVENT_DUPLICATION_QUEUE} = require("../../constants/workers-queue");

class EventDuplicationService {
	constructor () {
        this.queue = new Queue(EVENT_DUPLICATION_QUEUE, sails.config.redis_queue.workers_queue);
    }

    copyEmailTemplates(originEventId, destinationEventId, ownerId) {
        return this._addJob({
            origin_event_id: originEventId,
            destination_event_id: destinationEventId,
            event_owner_id: ownerId,
            resources_to_duplicate: [RESOURCES_TO_DUPLICATE.EMAIL_TEMPLATES]
        })
    }

    _addJob(eventDuplicationData, options = {}) {
        return this.queue.add(eventDuplicationData, options);
    }

    async process (eventDuplicationData) {
        const {resources_to_duplicate} = eventDuplicationData;

        if(resources_to_duplicate.includes(RESOURCES_TO_DUPLICATE.EMAIL_TEMPLATES)) {
            await this._duplicateEmailTemplates(eventDuplicationData)
        }
    }

    async _duplicateEmailTemplates (eventDuplicationData) {
        const {origin_event_id, destination_event_id, event_owner_id} = eventDuplicationData;

        const emailTemplatesForCopy = await this._getEmailTemplatesByEvent(origin_event_id, event_owner_id);

        if (_.isEmpty(emailTemplatesForCopy)) {
            return;
        }

        const prepareEmailTemplatesData = this._prepareEmailTemplatesData(
            emailTemplatesForCopy,
            destination_event_id,
            event_owner_id
        );

        await this._insertEmailTemplates(prepareEmailTemplatesData);
    }

    _getEmailTemplatesByEvent(eventId, eventOwnerId) {
        const query = `SELECT
                et."is_valid",
                et."email_html",
                et."email_subject",
                et."email_text",
                et."event_owner_id",
                et."sender_type",
                et."title",
                et."event_id",
                et."bee_json",
                et."bee_html",
                et."unlayer_json",
                et."img_name",
                et."email_template_type",
                et."email_template_group",
                et."published"
            FROM "email_template" et
            WHERE et."event_id" = $1
                AND et.event_owner_id = $2
                AND et."is_valid" IS TRUE
                AND et."published" IS TRUE
                AND et.deleted IS NULL`;

        return Db.query(query, [eventId, eventOwnerId]).then(result => result.rows || []);
    }

    _prepareEmailTemplatesData(emailTemplatesForCopy, destination_event_id, event_owner_id) {
        return emailTemplatesForCopy.map(template => {
            template.unlayer_json = template.unlayer_json ? JSON.stringify(template.unlayer_json) : null;
            template.bee_json = template.bee_json ? JSON.stringify(template.bee_json) : null;
            template.event_id = destination_event_id;
            template.event_owner_id = event_owner_id;

            return template;
        });
    }

    async _insertEmailTemplates(rows) {
        let tr = null;

        try {
            tr = await Db.begin();

            const chunks = _.chunk(rows, EMAIL_TEMPLATE_INSERT_LIMIT);

            await Promise.all(chunks.map((chunk) => {
                const query = knex('email_template').insert(chunk);

                return tr.query(query).then(response => response.rows);
            }));

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }
}

module.exports = new EventDuplicationService();
