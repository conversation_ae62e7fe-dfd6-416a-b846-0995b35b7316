const Tr = require('../../lib/db/Tr');


const tableTags = {
    event: [
        'event_id',
        'event_tickets_code',
    ],
};
const globalInvalidationTables = [
    'event',
    'event_ticket',
    'event_media',
    'ticket_discount',
];

class TagInvalidator {
    async dbTable(name, result, db = undefined) {
        const errorHandler = this.errorHandler.bind(this, name);
        try {
            if(!_.isObject(result) || !result.rowCount || !result.rows || !result.command) {
                throw new Error('Invalid result parameter value');
            }
            if (!( name in tableTags )) {
                throw new Error(`Table ${name} doesn't have tag dependencies`);
            }
            if (result.rowCount === 0) {
                loggers.debug_log.verbose(`Skipped invalidating "${name}" table without affected rows`);
                return;
            }
            loggers.debug_log.verbose(`Invalidating "${name}" comand: ${result.command}; rows: ${JSON.stringify(result.rows)};`);
            let tagsToInvalidate = new Set();
            if (['UPDATE', 'DELETE'].includes(result.command)) {
                for (const row of result.rows) {
                    for (const field of tableTags[name]) {
                        if(!row[field]) {
                            loggers.errors_log.error(`Field ${field} not found in ${result.command} query for "${name}" table`);
                            continue;
                        }
                        tagsToInvalidate.add(
                            Cache.tag.dbTable(name, { [field]: row[field] })
                        )
                    }
                }
            }
            const invalidate = async () => {
                if(!tagsToInvalidate || tagsToInvalidate.size === 0) {
                    return;
                }
                await Cache.invalidateTags(Array.from(tagsToInvalidate)).catch(errorHandler);
                tagsToInvalidate = undefined;
            };
            if (db instanceof Tr) {
                db.once(db.EVENT_COMMITTED, () => invalidate().catch(errorHandler));
            }
            else {
                await invalidate();
            }
        }
        catch(err) {
            errorHandler(err);
        }
    }

    async initGlobalInvalidation(db) {
        const shouldInvalidate = ({command, rowCount, tableName}) => rowCount > 0 && tableName && ['INSERT', 'UPDATE', 'DELETE'].includes(command) && globalInvalidationTables.includes(tableName)

        db.on(db.EVENT_AFTER_RUN_SQL, ({command, rowCount, tableName}) => {
            if(!shouldInvalidate({command, rowCount, tableName})) {
                return;
            }
            const tag = Cache.tag.dbTable(tableName);
            Cache.invalidateTags([tag]).catch(err => this.errorHandler(tableName, err));
        })
        db.on(db.EVENT_BEGIN_TRANSACTION, ({tr}) => {
            const updatedTables = new Set();
            tr.on(tr.EVENT_AFTER_RUN_SQL, ({command, rowCount, tableName}) => {
                if(!shouldInvalidate({command, rowCount, tableName})) {
                    return;
                }
                updatedTables.add(tableName);
            });
            tr.on(tr.EVENT_COMMITTED, () => {
                if(updatedTables.size === 0) {
                    return;
                }
                Cache.invalidateTags(
                    Array.from(updatedTables, tableName => Cache.tag.dbTable(tableName))
                ).catch(
                    err => this.errorHandler(Array.from(updatedTables).join(', '), err)
                );
            });
        });
    }

    errorHandler(target, err) {
        loggers.errors_log.error(
            {
                message: `Error invalidating tags for ${target}`,
                err,
            }
        )
    }
}

module.exports = new TagInvalidator();
