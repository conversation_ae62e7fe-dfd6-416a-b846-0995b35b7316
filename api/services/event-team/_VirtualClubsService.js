'use strict';

const co = require('co');

class VirtualClubsService {
    constructor() {}

    get DEFAULTS() {
        return {
            ADDRESS         : 'Virtual Club',
            CITY            : 'Virtual City',
            CD_ID           : 0,
            CODE            : '00000',
            REGION          : ''
        }
    }

    createVirtualClub (eventID, eventData) {
        let tr = null;

        return co(function* () {
            if(!eventID) {
                throw new Error('Event Id required');
            }

            tr = yield Db.begin();

            let master_club_id = yield this.__createVirtualMasterClub(tr, eventData);

            if(!master_club_id) {
                throw new Error('Master Club not created');
            }

            let roster_club_id = yield this.__createVirtualRosterClub(tr, master_club_id, eventData);

            yield tr.commit();

            return { master_club_id, roster_club_id };
        }.bind(this))
            .catch(err => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }

                throw err;
            })
    }

    getVirtualClub (eventID) {
        if(!eventID) {
            return Promise.reject(new Error('Event ID required'));
        }

        let query = squel.select().from('roster_club', 'rc')
            .field('rc.roster_club_id')
            .field('mc.master_club_id')
            .join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .where('rc.event_id = ?', eventID)
            .where('rc.deleted IS NULL')
            .where('rc.is_virtual IS TRUE');

        return Db.query(query).then(result => result.rows[0] || null);
    }

    __createVirtualMasterClub(tr, eventData) {
        let query = squel.insert().into('master_club', 'mc')
            .set('club_owner_id'    , this.DEFAULTS.CD_ID)
            .set('code'             , this.DEFAULTS.CODE)
            .set('region'           , this.DEFAULTS.REGION)
            .set('city'             , this.DEFAULTS.CITY)
            .set('address'          , this.DEFAULTS.ADDRESS)
            .set('has_male_teams'   , eventData.has_male_teams)
            .set('has_female_teams' , eventData.has_female_teams)
            .set('has_coed_teams'   , eventData.has_coed_teams)
            .set('zip'              , eventData.eventID)
            .set('country'          , eventData.country)
            .set('state'            , eventData.state)
            .set('club_name'        , eventData.event_name)
            .set('sport_id'         , eventData.sport_id)
            .returning('master_club_id');

        return tr.query(query).then(result => result.rows[0] && result.rows[0].master_club_id || null)
    }

    __createVirtualRosterClub (tr, masterClubID, eventData) {
        let query = squel.insert().into('roster_club', 'rc')
            .set('master_club_id'   , masterClubID)
            .set('event_id'         , eventData.eventID)
            .set('club_name'        , eventData.event_name)
            .set('zip'              , eventData.eventID)
            .set('country'          , eventData.country)
            .set('state'            , eventData.state)
            .set('region'           , this.DEFAULTS.REGION)
            .set('city'             , this.DEFAULTS.CITY)
            .set('address'          , this.DEFAULTS.ADDRESS)
            .set('code'             , this.DEFAULTS.CODE)
            .set('is_virtual'       , true)
            .returning('roster_club_id');

        return tr.query(query).then(result => result.rows[0] && result.rows[0].roster_club_id || null);
    }
}


module.exports = new VirtualClubsService();
