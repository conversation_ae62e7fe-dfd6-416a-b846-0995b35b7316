
class CustomCode {
    get NAME () {
        return 'custom';
    }

    async search (eventID, code) {
        let query = knex('ticket_buy_entry_code AS tbec')
            .select('*')
            .where(knex.raw(`LOWER(tbec.code) = LOWER(?)`, [code]))
            .where('tbec.event_id', eventID);

        let found = await Db.query(query).then(result => result?.rows?.[0]);

        return !!found;
    }

    list (eventID, filters) {
        let query = knex('ticket_buy_entry_code AS tbec')
            .select({
                ticket_buy_entry_code: knex.raw(`UPPER(tbec.code)`),
                additional_fields: knex.raw(`jsonb_agg('{}'::JSONB)`)
            })
            .where('tbec.event_id', eventID)
            .groupBy('tbec.code')

        if(!_.isEmpty(filters)) {
            if(filters.search) {
                query.where(builder => {
                    let search = `%${filters.search}%`;

                    builder.where(knex.raw(`tbec.code ILIKE ?`, [search]));
                })
            }
        }

        return query;
    }
}

module.exports = new CustomCode();
