'use strict';

const RefundWebhookService = require('./__RefundWebhookService');

class StripeDashboardRefundService extends RefundWebhookService {
    process(chargeID, refundAmount, additionalFeeAmount) {
        return super.process({ chargeID, amountRefunded: refundAmount, additionalFeeAmount })
    }

    __validateParams(data) {
        if (!data.chargeID) {
            throw new Error('Stripe Charge ID required');
        }
    }

    __getPurchaseUpdateQuery(data) {
        return this.__getBasePurchaseUpdateQuery(data).where(
            'p.stripe_charge_id',
            data.chargeID
        );
    }
}

module.exports = StripeDashboardRefundService