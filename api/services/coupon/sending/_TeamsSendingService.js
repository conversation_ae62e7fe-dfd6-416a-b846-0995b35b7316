
const TEAMS_EMAIL_RECIPIENT_TYPE = {
    CLUB_DIRECTOR: 'club_director',
    HEAD_COACH: 'head_coach',
}

class TeamsSendingService {

    get TEAMS_EMAIL_RECIPIENT_TYPE () {
        return TEAMS_EMAIL_RECIPIENT_TYPE;
    }

    get TEAMS_EMAIL_RECIPIENT_TYPES () {
        return Object.values(this.TEAMS_EMAIL_RECIPIENT_TYPE);
    }

    get TEAMS_RECEIVERS_DATA_SOURCES () {
        return {
            [this.TEAMS_EMAIL_RECIPIENT_TYPE.CLUB_DIRECTOR]: this.__cdMessagesData.bind(this),
            [this.TEAMS_EMAIL_RECIPIENT_TYPE.HEAD_COACH]: this.__hcMessagesData.bind(this),
        };
    }

    async getMessagesData ({ eventID, filters, teamsRecipientTypes = this.TEAMS_EMAIL_RECIPIENT_TYPES, isResend }) {
        if(!Array.isArray(teamsRecipientTypes) || !teamsRecipientTypes.length) {
            throw { validation: 'Coupon recipients are required' };
        }

        let messagesData = await Promise.all(
            teamsRecipientTypes.map(type => this.TEAMS_RECEIVERS_DATA_SOURCES[type](eventID, filters, isResend))
        )

        return _.flatten(messagesData);
    }

    __cdMessagesData (eventID, filters, isResend) {
        let query = this.__cdMessagesQuery(eventID, filters, isResend);

        return Db.query(query).then(r => r && r.rows);
    }

    __hcMessagesData (eventID, filters, isResend) {
        let query = this.__hcMessagesQuery(eventID, filters, isResend);

        return Db.query(query).then(r => r && r.rows);
    }

    __cdMessagesQuery (eventID, filters, isResend) {
        let selectedColumns = this.__selectedColumns();
        let baseCouponsQuery = this.__baseCouponsQuery(eventID, filters);

        let query = baseCouponsQuery.clone()
            .join('roster_club as rc', 'rc.roster_club_id', 'rt.roster_club_id')
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .groupBy('mc.director_email', 'e.event_id', 'rc.roster_club_id', 'mc.administrative_email')
            .select({
                email: knex.raw(
                    `concat_ws(
                        '${EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING}', 
                        mc.director_email, 
                        mc.administrative_email
                    )`
                ),
                ...selectedColumns,
                roster_club_id: 'rc.roster_club_id'
            });

        if(!isResend) {
            query.whereNotExists(
                this.__emailHistorySubquery().whereRaw('ee.email_to = mc.director_email')
            );
        }

        if(filters && filters.email) {
            query.where('mc.director_email', filters.email);
        } else {
            query.whereNotNull('mc.director_email');
        }

        this.__addSearch(query, filters, 'mc.director_email');

        return query;
    }

    __hcMessagesQuery (eventID, filters, isResend) {
        let selectedColumns = this.__selectedColumns();
        let baseCouponsQuery = this.__baseCouponsQuery(eventID, filters);

        let query = baseCouponsQuery.clone()
            .join('roster_staff_role as rsr', function () {
                this.on('rsr.roster_team_id', 'rt.roster_team_id')
                    .andOnNull('rsr.deleted')
                    .andOnNull('rsr.deleted_by_user')
            })
            .leftJoin('master_staff_role as msr', function () {
                this.on('msr.master_team_id', 'rsr.master_team_id')
                    .andOn('msr.master_staff_id', 'rsr.master_staff_id')
            })
            .leftJoin('master_staff as ms', function () {
                this.on('ms.master_staff_id', 'rsr.master_staff_id')
                    .andOnNull('ms.deleted')
            })
            .groupBy('ms.email', 'e.event_id', 'ms.master_staff_id')
            .whereRaw('COALESCE(NULLIF(rsr.role_id, ?), msr.role_id) = ?', [0, 4])
            .select({
                email: 'ms.email',
                master_staff_id: 'ms.master_staff_id',
                ...selectedColumns,
            });

        if(!isResend) {
            query.whereNotExists(
                this.__emailHistorySubquery().whereRaw('ee.email_to = ms.email')
            );
        }

        if(filters && filters.email) {
            query.where('ms.email', filters.email);
        } else {
            query.whereNotNull('ms.email');
        }

        this.__addSearch(query, filters, 'ms.email');

        return query;
    }

    __selectedColumns () {
        return {
            long_name: 'e.long_name',
            event_id: 'e.event_id',
            recipient_type: knex.raw(`'club'`),
            coupons: knex.raw('to_jsonb(array_agg( (select to_jsonb(r) from (?) r) ))', this.__couponsSubquery()),
            event_tickets_code: 'e.event_tickets_code'
        };
    }

    __couponsSubquery () {
        return knex.queryBuilder()
            .select(
                'rt.team_name','tc.code','tc.quantity', 'tc.ticket_coupon_id',
                knex.raw(`
                    (SELECT ARRAY_AGG(
                        TO_CHAR(TO_TIMESTAMP(vd, 'YYYY-MM-DD'), 'Dy, Mon DD')
                    ) FROM JSONB_OBJECT_KEYS(et.valid_dates) vd) "valid_dates"
                `)
            )
    }

    __baseCouponsQuery (eventID, filters) {
        const baseCouponsQuery = knex('ticket_coupon_receiver as tcr')
            .join('ticket_coupon AS tc', 'tc.ticket_coupon_id', 'tcr.ticket_coupon_id')
            .join('event_ticket as et', 'et.event_ticket_id', 'tc.event_ticket_id')
            .join('event as e', 'e.event_id', 'et.event_id')
            .join('roster_team as rt', 'rt.roster_team_id', 'tcr.roster_team_id')
            .where('e.event_id', eventID);

        CouponService.list.addFilters(baseCouponsQuery, filters);

        return baseCouponsQuery;
    }

    __addSearch (query, filters, emailFieldName) {
        if(filters.search) {
            query.where(builder => {
                let search = `%${filters.search}%`;

                builder.where('tc.code', 'ILIKE', search)
                    .orWhere(emailFieldName, 'ILIKE', search)
                    .orWhere('rt.team_name','ILIKE', search);
            })
        }
    }

    __emailHistorySubquery () {
        let query = knex('event_email AS ee')
            .whereRaw('ee.event_id = e.event_id')
            .whereRaw(`ee.ticket_coupons @> to_jsonb(tc.ticket_coupon_id)`)
            .whereRaw(`ee.recipient_type = 'club'`)

        return query.clone();
    }
}

module.exports = new TeamsSendingService();
