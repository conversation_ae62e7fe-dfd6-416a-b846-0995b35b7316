const uaExportSchemas = require('../validation-schemas/ua-export');

class UAExportService {
    constructor() {}

    getSchedule(params) {
        const { error, value } = uaExportSchemas.scheduleParams.validate(params);
        if (error) {
            throw { validation: error.details[0].message };
        }

        const {
            eventID,
            clubPrivateRegCode,
            gender,
            startFrom,
            startTill,
            teamCode,
            requireBothTeams = false,
            orderBy
        } = value;

        let query = squel
            .select()
            .from('matches', 'm');

        let t1SubQuery = squel
            .expr()
            .and('t1.roster_team_id = m.team1_roster_id')
            .and('t1.deleted IS NULL')
            .and('t1.status_entry = 12');

        let t2SubQuery = squel
            .expr()
            .and('t2.roster_team_id = m.team2_roster_id')
            .and('t2.deleted IS NULL')
            .and('t2.status_entry = 12');

        let trSubQuery = squel
            .expr()
            .and('tr.roster_team_id = m.ref_roster_id')
            .and('tr.deleted IS NULL')
            .and('tr.status_entry = 12');

        if (gender) {
            t1SubQuery.and('t1.gender = ?', gender);
            t2SubQuery.and('t2.gender = ?', gender);
            trSubQuery.and('tr.gender = ?', gender);
        } else {
            query.field('tr.gender');
            query.field('d.name', 'division_name');
            query.field('d.division_id');
            query.left_join('division', 'd', 'd.division_id = m.division_id');
        }

        if(requireBothTeams) {
            query.where('t1.roster_team_id IS NOT NULL AND t2.roster_team_id IS NOT NULL');
        } else {
            query.where('t1.roster_team_id IS NOT NULL OR t2.roster_team_id IS NOT NULL');
        }

        if(startFrom) {
            query.where(`m.secs_start >= ? AT TIME ZONE e.timezone`, startFrom);
        }

        if(startTill) {
            query.where(`m.secs_start <= ? AT TIME ZONE e.timezone`, startTill);
        }

        if(teamCode) {
            query.where('t1.organization_code = ? OR t2.organization_code = ?', teamCode, teamCode);
        }

        query
            .field('m.match_id', 'match_uuid')
            .field('m.event_id', 'event')
            .field(`m.event_id || '_' || m.division_short_name || '_' || m.display_name`, 'match_id')
            .field('m.division_short_name', 'div')
            .field('m.day')
            .field(`to_char(m.secs_start, 'YYYY-MM-DD HH24:MI:SS')`, 'date_time')
            .field('c.sort_priority', 'court')
            .field('c.short_name', 'court_alpha')
            .field('pb.display_name', 'pool')
            .field('m.team1_roster_id')
            .field('m.team2_roster_id')
            .field('m.ref_roster_id')
            .field('t1.team_name', 'team_1_name')
            .field('t2.team_name', 'team_2_name')
            .field('t1.master_team_id', 'master_team_id_1')
            .field('t2.master_team_id', 'master_team_id_2')
            .field('tr.team_name', 'ref_name')
            .field('m.type', 'match_type')
            .field(`(SELECT jsonb_object_agg(key, value)
             FROM jsonb_each_text(m.results) 
             WHERE key LIKE 'set%') as results`)
            .join('event', 'e', 'e.event_id = m.event_id')
            .left_join('poolbrackets', 'pb', 'pb.uuid = m.pool_bracket_id')
            .left_join('courts', 'c', 'c.uuid = m.court_id')
            .left_join('roster_team', 't1', t1SubQuery)
            .left_join('roster_team', 't2', t2SubQuery)
            .left_join('roster_team', 'tr', trSubQuery)

        const buildWhereClause = () => {
            const whereExpr = squel.expr();

            if (clubPrivateRegCode) {
                whereExpr.or('e.club_private_reg_code = ?', clubPrivateRegCode);
            }
            else if (eventID) {
                whereExpr.or('e.event_id = ?', eventID);
            }

            return whereExpr;
        };

        query.where(buildWhereClause());

        if(orderBy) {
            query.order(orderBy);
        }

        return Db.query(query).then(({ rows }) => rows);
    }
}

module.exports = new UAExportService();
