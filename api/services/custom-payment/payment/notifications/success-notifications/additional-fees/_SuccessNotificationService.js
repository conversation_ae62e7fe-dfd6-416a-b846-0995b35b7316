const { PAYMENT_FOR } = require('../../../../../../constants/payments');

class SuccessNotificationService {
    constructor (NotificationsService) {
        this.NotificationsService = NotificationsService;
    }

    #TEMPLATES = {
        [PAYMENT_FOR.TICKETS]: 'custom-offline-payment/ticket-lost-dispute-fee-failed-ach-fee-payment',
        [PAYMENT_FOR.TEAMS]: 'custom-offline-payment/team-lost-dispute-fee-failed-ach-fee-payment',
    }

    async send (params) {
        const {
            email,
            payment_for_type
        } = params;

        const subject = this.#getSubject(params);
        const template = this.#TEMPLATES[payment_for_type];
        const data = await this.#prepareNotificationData(params, payment_for_type);

        return this.NotificationsService.send(email, subject, data, template);
    }

    async #prepareNotificationData (params, paymentTypeFor) {
        const {
            event_id,
            event_name,
            event_short_name,
            payment_method_type,
            card_last_4,
            totals,
            balance,
        } = params;

        const {
            lostDisputesBalance,
            failedACHFeesBalance,
        } = balance;

        const notificationParams = {
            event_name,
            event_short_name,
            payment_method_type,
            card_last_4,
            current_year: new Date().getFullYear(),
        };

        const lostDisputesCharges = lostDisputesBalance?.chargeIDs || [];
        const failedACHCharges = failedACHFeesBalance?.chargeIDs || [];

        if(paymentTypeFor === PAYMENT_FOR.TICKETS) {
            notificationParams.ticket_details = await this.#getTicketDetails(event_id, lostDisputesCharges);
        }

        if(paymentTypeFor === PAYMENT_FOR.TEAMS) {
            notificationParams.club_details = await this.#getTeamsDetails(
                event_id,
                [...failedACHCharges, ...lostDisputesCharges]
            );
        }

        return {
            ...notificationParams,
            hasLostDisputes: lostDisputesCharges.length,
            hasFailedACHPayments: failedACHCharges.length,
            payment_details: this.#getPaymentDetails(lostDisputesBalance, failedACHFeesBalance, totals),
            accounting_details: this.#getAccountingDetails(lostDisputesBalance),
        }
    }

    #getPaymentDetails (lostDisputes, failedACHPayments, totals) {
        if(_.isEmpty(totals)) {
            throw new Error(`Totals shouldn't be empty`);
        }

        const details = {
            merchant_processing_fee: totals.merchant_fee,
            total_charge: totals.total,
        };

        if(!_.isEmpty(failedACHPayments)) {
            details.failed_ach_processing_fee = failedACHPayments.totals.totalAmount;
        }

        if(!_.isEmpty(lostDisputes)) {
            details.dispute_lost_merchant_fee = lostDisputes.totals.totalStripeFee;
            details.dispute_processing_fee = lostDisputes.totals.totalPenalties;
        }

        return details;
    }

    #getAccountingDetails (lostDisputes) {
        if(!_.isEmpty(lostDisputes)) {
            return {
                amount_refunded_from_connected_account: lostDisputes.totals.totalDisputedAmount,
                counts_disputed: lostDisputes.totals.count,
            }
        }
    }

    async #getTicketDetails(eventID, chargeIDs = []) {
        if(!chargeIDs.length) {
            return;
        }

        const query = knex('purchase as payment')
            .select({
                purchaser_name: knex.raw(`CONCAT(payment.first, ' ', payment.last)`),
                purchaser_phone: 'payment.phone',
                purchaser_email: 'payment.email',
                ticket_holders: knex.raw(`
                    JSON_AGG(JSONB_BUILD_OBJECT(
                        'name', CONCAT(ticket.first, ' ', ticket.last), 
                        'ticket_barcode', ticket.ticket_barcode
                    ))
                `),
            })
            .join('purchase AS ticket', (join) =>
                join.on(knex.raw(`COALESCE(ticket.linked_purchase_id, ticket.purchase_id) = payment.purchase_id`))
                    .andOn(knex.raw(`ticket.is_ticket IS TRUE`))
            )
            .whereRaw('payment.is_payment IS TRUE')
            .where('payment.event_id', eventID)
            .whereIn('payment.stripe_charge_id', chargeIDs)
            .groupBy('payment.first', 'payment.last', 'payment.phone', 'payment.email');

        const { rows: paymentsData } = await Db.query(query);

        if(_.isEmpty(paymentsData)) {
            throw new Error('Tickets not found');
        }

        const {
            purchaser_name,
            purchaser_email,
            purchaser_phone,
            ticket_holders
        } = paymentsData[0];

        const ticketBarcodes = paymentsData.reduce((tickets, payment) => {
            tickets = tickets.concat(payment.ticket_holders.map(ticket => ticket.ticket_barcode));
            return tickets;
        }, []);

        return {
            purchaser_name,
            purchaser_email,
            purchaser_phone,
            ticket_holders: ticket_holders.map(ticket => ticket.name).join(', '),
            ticket_codes_disputed: this.#getTicketCodesDisputed(eventID, ticketBarcodes)
        }
    }

    async #getTeamsDetails(eventID, chargeIDs) {
        if(!chargeIDs.length) {
            return;
        }

        const query = knex('purchase as p')
            .select({
                club_name: 'mc.club_name',
                director_full_name: knex.raw(`CONCAT(mc.director_first, ' ', mc.director_last)`),
                director_email: 'mc.director_email',
                director_phone: knex.raw(`regexp_replace(mc.director_phone, '(\\d{3})(\\d{3})(\\d{4})', '+1 (\\1) \\2-\\3')`),
                team_name: 'rt.team_name',
                purchase_id: 'p.purchase_id',
            })
            .join('purchase_team as pt', 'pt.purchase_id', 'p.purchase_id')
            .join('roster_club AS rc', 'rc.roster_club_id', 'p.roster_club_id')
            .join('roster_team as rt', 'rt.roster_team_id', 'pt.roster_team_id')
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .whereIn('p.stripe_charge_id', chargeIDs);

        const { rows: paymentsData } = await Db.query(query);

        if(_.isEmpty(paymentsData)) {
            throw new Error('Teams not found');
        }

        const firstPurchase = paymentsData[0];

        return {
            director_full_name: firstPurchase.director_full_name,
            club_name: firstPurchase.club_name,
            director_email: firstPurchase.director_email,
            director_phone: firstPurchase.director_phone,
            teams_disputed: this.#getTeamDisputed(eventID, paymentsData)
        }
    }

    #getTicketCodesDisputed (eventId, ticketCodes) {
        return ticketCodes.map((ticket_barcode) => ({
            ticket_barcode,
            link: TicketsService.metadata.getPurchasePage(eventId, ticket_barcode),
        }));
    }

    #getTeamDisputed (eventId, data) {
        return data.map(({purchase_id, team_name}) => ({
            team_name,
            link: TeamsPaymentService.metadata.getPurchasePage(eventId, purchase_id),
        }))
    }

    #getSubject (params) {
        const {
            event_name: eventName,
            balance,
            payment_for_type: paymentForType,
        } = params;

        const penaltyType = this.#getPenaltyTypeSubjectPart(balance);

        if (paymentForType === PAYMENT_FOR.TICKETS) {
            return `Tickets ${penaltyType} – ${eventName}`;

        } else if (paymentForType === PAYMENT_FOR.TEAMS) {
            return `Team ${penaltyType} – ${eventName}`;
        }

        return `${penaltyType} – ${eventName}`;
    }

    #getPenaltyTypeSubjectPart (balance) {
        const {
            lostDisputesBalance,
            failedACHFeesBalance,
        } = balance;

        const hasLostDisputes = lostDisputesBalance?.totals?.count > 0;
        const hasFailedACHPayments = failedACHFeesBalance?.totals?.count > 0;

        let text = '';

        if(hasLostDisputes && hasFailedACHPayments) {
            text = 'Dispute and Failed ACH payments penalty';
        } else if(hasLostDisputes) {
            text = 'Dispute';
        } else if(hasFailedACHPayments) {
            text = 'Failed ACH payments penalty';
        }

        return text;
    }
}

module.exports = SuccessNotificationService;
