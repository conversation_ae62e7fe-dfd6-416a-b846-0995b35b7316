
const PartialCustomPaymentRefund = require('./refund/_PartialCustomPaymentRefund');
const FullCustomPaymentRefund = require('./refund/_FullCustomPaymentRefund');

class EventCustomPaymentRefund {
    get REFUND_SOURCE_TYPE () {
        return {
            DASHBOARD: 'dashboard'
        }
    }

    get ALLOWED_REFUND_SOURCES () {
        return [this.REFUND_SOURCE_TYPE.DASHBOARD];
    }

    proceed (amountRefunded, payment, refundSource) {
        if(_.isEmpty(payment) || !_.isObject(payment)) {
            return Promise.reject('Payment is empty');
        }

        if(!this.ALLOWED_REFUND_SOURCES.includes(refundSource)) {
            return Promise.reject('Refund source is not allowed');
        }

        if(!amountRefunded) {
            return Promise.reject('Amount refunded not passed or is 0');
        }

        let RefundTypeInstance = this.__getRefundTypeInstance(amountRefunded, payment, refundSource);

        return RefundTypeInstance.proceed(amountRefunded);
    }

    __getRefundTypeInstance (amountRefunded, payment, refundSource) {
        if(this.__isFullRefund(amountRefunded, payment)) {
            return new FullCustomPaymentRefund(payment, refundSource);
        } else {
            return new PartialCustomPaymentRefund(payment, refundSource);
        }
    }

    __isFullRefund (amountRefunded, payment) {
        return amountRefunded === (payment.amount + payment.amount_refunded);
    }
}

module.exports = new EventCustomPaymentRefund();
