'use strict';

/**
 * Returns data for "Totals & Net Profit" component 
 * Docs (rus): https://docs.google.com/document/d/1GAO160tIAHJbmDxBYURgr4tNkkfbusql_7Q58pNuY24/edit#bookmark=id.xxbh3kcjuzkt
 */

const co = require('co');

const knex = require('knex')({client: 'pg'});

const utils = require('../../lib/swUtils');

const AccCommon = require('./_AccountingCommonService');

const { CARD, ACH, CHECK } = AccCommon;

// TODO: move to a separate file
const COLUMNS = require('./totalsAndNetProfitColumns.json');

/**
 * Returns Totals & Net Profit statistics for a selected item (paymentFor)
 *
 * Response structure: 
 * (Please, see Docs above to understand the structure)
 *
 * {
 *     "stats": [
 *         "section": "merchant",
 *         "label": "MERCHANT TRANSACTIONS", // may vary depending on the item 
 *         "rows": [
 *             { 
 *                 "label"      : "" , // Credit, ACH, Check, Cash, etc. 
 *                 "caption"    : "", // e.g. 'ONLINE PRIOR TO THE EVENT'. This will group rows and
 *                                     // create a separate row (that contains a caption) on the UI
 *                 ... Other Columns' values ... // see COLUMNS constant above
 *             }
 *         ]
 *     ]
 * }
 * 
 * @param  {Number} eventID    - Event identifier
 * @param  {String} paymentFor - Selling item "teams/tickets/booths"
 * @return {Object}            
 */
module.exports.getTotals = function (eventID, paymentFor) {
    return co(function* () {

        if (paymentFor === AccCommon.TEAMS_PAYMENT_FOR_TYPE) {

            let stats = yield this.getTeamsStatistics(eventID, paymentFor); /* jshint ignore:line */

            return Object.assign({ columns: COLUMNS }, stats);
        } else if (paymentFor === AccCommon.TICKETS_PAYMENT_FOR_TYPE) {
            /* TODO */
        } else if (paymentFor === AccCommon.BOOTHS_PAYMENT_FOR_TYPE) {
            /* TODO */
        } else {
            throw new Error(`No such item ${paymentFor}`);
        }
    }.bind(this));
}

module.exports.makeSectionRow = function (stats, label, caption, params = {}) {
    /* To handle "null" */
    caption = caption || '';
    label   = label || ''

    return Object.assign({ label, caption }, params, stats);
}

module.exports.formatTeamsStatsSections = function (data) {
    let {
        merchantTotals,
        totalsAndNetProfit,
        seasonTotals,
        lostDisputesAdjustments,
        unpaidTeams
    } = data;

    /* === Merchant Section === */

    let merchantSection = {
        section     : 'merchant',
        label       : 'MERCHANT TRANSACTIONS / Teams',
        rows: [
            this.makeSectionRow(data[CARD], 'Credit', null, { show_items_qty: true }),
            this.makeSectionRow(data[ACH], 'ACH', null, { show_items_qty: true }),
            this.makeSectionRow(merchantTotals, 'Merchant Totals')
        ]
    }

    /* === */

    /* === Direct to Event Transactions Section === */

    let directTransactionsSection = {
        section     : 'direct_transactions',
        label       : 'DIRECT TO EVENT TRANSACTIONS',
        rows: [
            this.makeSectionRow(data[CHECK], 'CHECK', null, { show_items_qty: true })
        ]
    }

    /* === */

    /* === Totals === */

    
    let totalsAndNetProfitSection = {
        section     : 'season_totals',
        label       : '',
        bg          : 'info',
        rows: [
            this.makeSectionRow(totalsAndNetProfit, 'Totals & Net Profit')
        ]
    }

    /* === */

    /* === Adjustments === */

    let adjustmentsSection = {
        section : 'adjustments',
        label   : 'ADJUSTMENTS BY SW',
        rows    : [
            this.makeSectionRow(lostDisputesAdjustments, 'Lost Disputes with Stripe'),
            this.makeSectionRow(unpaidTeams, 'Accepted but not paid teams', null, { show_items_qty: true })
        ]
    }

    /* === */

    /* === SEASON TOTALS === */

    let seasonTotalsSection = {
        section     : 'season_totals',
        label       : 'Season END Totals & Net Profit',
        rows: [
            this.makeSectionRow(seasonTotals)
        ]
    }

    /* === */


    return [
        merchantSection, 
        directTransactionsSection, 
        totalsAndNetProfitSection,
        adjustmentsSection,
        seasonTotalsSection
    ]
}

/**
 * Totals & Net Profit Statistics of Teams' entry
 */
module.exports.getTeamsStatistics = function (eventID, paymentFor) {
    return co(function* () {

        const getStats = (type) => {
            return AccCommon.merchantStat(eventID, type, paymentFor);
        }

        /* === Statistics Data === */

        let statsData = yield ({
            /* jshint ignore:start */
            [CARD]                  : getStats(CARD),
            [ACH]                   : getStats(ACH),
            [CHECK]                 : getStats(CHECK),
            lostDisputesAdjustments : this.getAjustments(eventID, paymentFor), 
            unpaidTeams             : this.getAcceptedAndNotPaidTeams(eventID)

            /* jshint ignore:end */
        });

        let merchantTotals      = this.findTotals(statsData, [CARD, ACH], COLUMNS);
        let totalsAndNetProfit  = this.findTotals(statsData, [CARD, ACH, CHECK], COLUMNS);

        let {
            lostDisputesAdjustments,
            unpaidTeams
        } = statsData;

        let seasonTotals = this.calcSeasonTotals(totalsAndNetProfit, {
            number_of_transactions: true
        },lostDisputesAdjustments, unpaidTeams);

        /* === */

        return { 
            stats: this.formatTeamsStatsSections(Object.assign({}, statsData, {
                merchantTotals,
                totalsAndNetProfit,
                seasonTotals,
            }))
        }
    }.bind(this));
}

module.exports.getAcceptedAndNotPaidTeams = async function (eventID) {
    let query = knex('roster_team AS rt')
        .count('rt.*', {as: 'qty'})
        .select('e.teams_entry_sw_fee AS sw_fee')
        .leftJoin('event AS e', 'e.event_id', 'rt.event_id')
        .where('rt.event_id', eventID)
        .whereNull('rt.deleted')
        .where(builder => {
            builder.andWhereRaw(`rt.status_entry = ANY (?) AND rt.status_paid = ANY (?)`,
                [[12 /* accepted */], [21 /* not paid */, 24 /* pending */, 25 /* refunded */]])
                .orWhereRaw(`
                             EXISTS(SELECT 1
                                        FROM purchase_team pt
                                        JOIN purchase p ON p.purchase_id = pt.purchase_id
                                        WHERE pt.event_id = rt.event_id
                                        AND pt.roster_team_id = rt.roster_team_id
                                        AND pt.canceled IS NULL
                                        AND p.status = 'canceled') 
                             AND rt.status_entry = ? AND rt.status_paid = ?
                    `, [12 /* accepted */ , 22  /* paid */])
        })
        .groupBy('e.teams_entry_sw_fee');

    let {qty, sw_fee} = await Db.query(query).then(({rows}) => rows[0] || { qty: 0, sw_fee: 0 });

    let totalSWFee = -utils.normalizeNumber(qty * sw_fee);

    return {
        items_qty       : Number(qty),
        sw_service_fees : totalSWFee,
        balance         : totalSWFee
    }
};

module.exports.getAjustments = function (eventID, paymentFor) {
    return AccCommon.lostDisputesStat(eventID, paymentFor)
    .then(lostDisputesStat => {
        let adjustments = {
            number_of_transactions  : lostDisputesStat.number_of_lost_disputes,
            entry_fee_amount        : lostDisputesStat.entry_fee_amount,
            balance                 : (
                lostDisputesStat.stripe_penalty_fee +
                lostDisputesStat.returned_stripe_fee + 
                lostDisputesStat.returned_sw_service_fees
            ),
            sw_service_fees         : lostDisputesStat.returned_sw_service_fees,
            /* both items should be shown in "Stripe Transaction Fees" column */
            stripe_transaction_fees : [
                lostDisputesStat.stripe_penalty_fee,
                lostDisputesStat.returned_stripe_fee
            ]
        }

        return adjustments;
    })
}

module.exports.calcSeasonTotals = function (totals, colsToSkipAdj, ...adjustments) {
    colsToSkipAdj = colsToSkipAdj || {};

    let seasonTotals = {};

    for (let { field: col } of COLUMNS) {

        if (colsToSkipAdj[col]) {
            seasonTotals[col] = totals[col];
            continue;
        }


        for (let adj of adjustments) {
            let totalsValue = seasonTotals[col] || totals[col];

            let adjustmentsValue = adj[col] || 0;

            adjustmentsValue = 
                Array.isArray(adjustmentsValue)
                    ? adjustmentsValue.reduce((sum, item) => (sum + item), 0)
                    : adjustmentsValue;

            seasonTotals[col] = utils.normalizeNumber(totalsValue + adjustmentsValue);
        }
    }

    return seasonTotals;
}

module.exports.findTotals = function (stats, types, columns) {
    const approx = utils.normalizeNumber.bind(utils);

    let totals = {};

    for (let col of columns) {
        totals[col.field] = 0;
    }

    for (let t of types) {

        let typeStats = stats[t];

        for (let col of columns) {
            totals[col.field] = approx(totals[col.field] + typeStats[col.field]);
        }
    }

    return totals;
}
