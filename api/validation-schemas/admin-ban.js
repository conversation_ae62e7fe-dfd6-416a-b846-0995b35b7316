const Joi = require('joi');

const { EMAIL_REGEX } = require('../lib/joi-constants');

const bansEmail = Joi.object().keys({
    email                 : Joi.string().pattern(EMAIL_REGEX).required().label('Email'),
    reason                : Joi.string().required().valid('dispute', 'other').label('Reason'),
});

const bansFingerprint = Joi.object().keys({
    fingerprint           : Joi.string().length(16).required().label('Fingerprint'),
    reason                : Joi.string().required().valid('dispute', 'other').label('Reason'),
})

module.exports.bansEmail                    = bansEmail;
module.exports.bansFingerprint              = bansFingerprint;

