

const Joi = require('joi');


const refundItemSchema = Joi.object({
    amount: Joi.number().required().min(1).label('Refund Amount'),
    quantity: Joi.number().integer().required().min(0).label('Refund Item Quantity'),
    marketplaceFee: Joi.number().required().min(0).label('Refund Marketplace Fee'),
}).label('Refund Item');

const refundSchema = Joi.object({
    id: Joi.string().required().label('Refund ID'),
    paymentId: Joi.string().required().label('Payment ID'),
    amount: Joi.number().required().min(0).label('Refund Amount'),
    marketplaceFee: Joi.number().required().min(0).label('Refund Marketplace Fee'),
    items: Joi.array().items(refundItemSchema).min(1).required().label('Refunded Items'),
    status: Joi.string().required().label('Refund Status'),
    isManual: Joi.boolean().required().label('Manual Refund Flag'),
});

const createRefundSchema = Joi.object().keys({
    paymentId: Joi.string().required().label('Payment ID'),
    amount: Joi.number().required().min(1).label('Refund Amount'),
    marketplaceFee: Joi.number().required().min(0).label('Refund Marketplace Fee'),
    items: Joi.array()
        .items(
            Joi.object().keys({
                orderItemId: Joi.string().required().label('Order Item ID'), 
                amount: Joi.number().required().min(1).label('Refunded Amount'), 
                quantity: Joi.number().required().min(0).label('Refunded Quantity'), 
                marketplaceFee: Joi.number().optional().min(0).label('Marketplace Fee Refund'), 
            })
        )
        .min(1)
        .required()
        .label('Refunded Items'),
});

const createFullRefundSchema = Joi.object().keys({
    paymentId: Joi.string().required().label('Payment ID'),
});

module.exports = {
    refundSchema,
    createSchema: createRefundSchema,
    createFullRefundSchema,
};
