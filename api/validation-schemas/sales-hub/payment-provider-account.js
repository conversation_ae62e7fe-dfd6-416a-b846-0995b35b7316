
const Joi = require('joi');

const { PAYMENT_PROVIDER_ACCOUNT_TYPE } = require('../../constants/sales-hub');

const creationSchema = Joi.object().keys({
    paymentProviderType: Joi.string()
        .valid(PAYMENT_PROVIDER_ACCOUNT_TYPE.PAYMENT_HUB)
        .required()
        .label('Payment Provider Type'),
    idAtProvider: Joi.string().required().label('Id at Payment Provider'),
    isActive: Joi.boolean().required().label('Is Active'),
});

const updateSchema = Joi.object().keys({
    isActive: Joi.boolean().required().label('Is Active'),
})

module.exports = {
    creationSchema,
    updateSchema,
}
