const Joi = require('joi');

const saveRateSchema = Joi.object().keys({
    rates: Joi.array().items(
        Joi.object().keys({
            event_official_rate_id: Joi.number().min(1).required().label('Event Official Rate Identifier'),
            rate: Joi.number().min(0).required().label('Rate'),
            official_additional_role_id: Joi.number().allow(null).label('Official additional role ID')
        })
    ).required().label('Rates')
})

module.exports = {
    saveRateSchema,
}
