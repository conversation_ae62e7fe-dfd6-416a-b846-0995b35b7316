const Joi = require('joi');
const { EMAIL_REGEX } = require("../lib/joi-constants");

const sharedSchema = Joi.object().keys({
    name: Joi.string().max(20).required().label('Event Short Name'),
    address: Joi.string().optional().max(200).allow(null).label('Address'),
    custom_housing_company: Joi.string().optional().max(200).allow(null).label('Custom Housing Company'),
    email: Joi.string().pattern(EMAIL_REGEX).optional().max(200).allow(null).label('Email'),
    host: Joi.string().optional().max(200).allow(null).label('Host'),
    hosting_org_address: Joi.string().optional().max(200).allow(null).label('Hosting Address'),
    hosting_org_city: Joi.string().optional().max(200).allow(null).label('Hosting City'),
    hosting_org_name: Joi.string().optional().max(200).allow(null).label('Hosting Name'),
    location: Joi.object().optional().allow(null).label('Location'),
    payment_address: Joi.string().optional().max(200).allow(null).label('Payment Address'),
    payment_city: Joi.string().optional().max(200).allow(null).label('Payment City'),
    payment_name: Joi.string().optional().max(200).allow(null).label('Payment Name'),
    admin_security_pin: Joi.string().optional().allow(null).label('Admin Security PIN'),
    stripe_teams_private_key: Joi.string().optional().allow(null).label('Stripe Teams Private Key'),
    stripe_tickets_private_key: Joi.string().optional().allow(null).label('Stripe Tickets Private Key'),
    official_additional_role_enable: Joi.boolean().optional().allow(null).label('Official Additional Role'),
    available_officials_sanctionings: Joi.array().items(Joi.string()).min(1).required().label('Officials enter to the event'),
    team_fees_notification_email:
        Joi.string().pattern(EMAIL_REGEX).optional().max(200).allow(null, '').label('SW Team Fees Notifications Email')
});

/**
 * https://github.com/hapijs/joi/blob/v9.0.4/API.md#objectunknownallow
 */
const createSchema = sharedSchema.unknown();
const updateSchema = sharedSchema.unknown();

module.exports = {
    createSchema,
    updateSchema,
}
