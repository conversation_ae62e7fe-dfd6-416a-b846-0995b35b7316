const {HOUSING_POLICY_PROVIDER} = require("../../constants/housing");

module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    const authorizationHashParams = {
        url: req.url,
        timestamp: req.get('Timestamp'),
        consumerName: HOUSING_POLICY_PROVIDER.HOUSING_COMPANIES,
    }

    try {
        HousingService.verifyAuthValidation(authorization, authorizationHashParams);

        next();
    } catch (err) {
        return res.status(401).json(err);
    }
}
