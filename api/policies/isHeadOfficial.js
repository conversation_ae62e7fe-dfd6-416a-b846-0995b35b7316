'use strict';

module.exports = function isHeadOfficial (req, res, next) {
    let officialID = req.session.passport.user.official_id,
        eventID    = req.params.event;

    OfficialsService.isHeadOfficial(eventID, officialID)
    .then(allow => {
        if (allow) {
            req.options.isHeadOfficial = true;

            next();
        } else {
            res.forbidden();
        }
    }).catch(res.customRespError);    
}
