BEGIN;

-- CREATE FIELD "tickets_check_payment_details" ----------------
ALTER TABLE "public"."event" ADD COLUMN "tickets_check_payment_details" Text COLLATE "pg_catalog"."default";COMMENT ON COLUMN "public"."event"."tickets_check_payment_details" IS 'Text to show on SWT payment form under "Check Payments Details" section';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE UNIQUE "unique_user_email" ---------------------------
ALTER TABLE "public"."user" ADD CONSTRAINT "unique_user_email" UNIQUE( "email" );
-- -------------------------------------------------------------

COMMIT;


-- Adding column "published" to the "event_official_schedule" table
ALTER TABLE public.event_official_schedule ADD published BOOLEAN DEFAULT FALSE NOT NULL;
COMMENT ON COLUMN public.event_official_schedule.published IS 'Publishing flag for official assignment. Not published assignments are not visible at SW Officials app.';