BEGIN;


-- UPDATE assigned tickets additional field ---------------------
UPDATE "purchase"
SET "tickets_additional" = (
    TO_JSONB(COALESCE("purchase"."tickets_additional", '{}')) || TO_JSONB("payment"."tickets_additional")
)::JSON
FROM (
    SELECT
        p."tickets_additional", p."purchase_id"
    FROM "purchase" p
    WHERE p."is_ticket" IS NOT TRUE
        AND p."is_payment" IS TRUE
        AND p."payment_for" = 'tickets'
        AND p."event_id" = 18017
) "payment"
WHERE "purchase"."linked_purchase_id" = "payment"."purchase_id"
    AND "purchase"."is_ticket" IS TRUE
    AND "purchase"."is_payment" IS NOT TRUE;
-----------------------------------------------------------------


COMMIT;
