BEGIN;


-- Remove system_job_email_sending_id_event_id_recipient_type_key constraint --------------------------
ALTER TABLE system_job DROP CONSTRAINT system_job_email_sending_id_event_id_recipient_type_key;
-------------------------------------------------------------------------------------------------------


-- Create partial index for custom lists recipients ---------------------------------------------------
CREATE UNIQUE INDEX system_job_email_sending_id_event_id_recipient_type_custom_recipients_list_id_key
	ON system_job (email_sending_id, event_id, recipient_type, custom_recipients_list_id)
WHERE custom_recipients_list_id IS NOT NULL;
-------------------------------------------------------------------------------------------------------


-- Create partial index for group recipients ----------------------------------------------------------
CREATE UNIQUE INDEX system_job_email_sending_id_event_id_recipient_type_key
	ON system_job (email_sending_id, event_id, recipient_type)
WHERE custom_recipients_list_id IS NULL;
-------------------------------------------------------------------------------------------------------


COMMIT;
