BEGIN;

-- CREATE FIELD "created" --------------------------------------
ALTER TABLE "public"."event_user" ADD COLUMN "created" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

-- CREATE FIELD "modified" -------------------------------------
ALTER TABLE "public"."event_user" ADD COLUMN "modified" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

COMMIT;


BEGIN;

CREATE TRIGGER update_event_user_modified
BEFORE UPDATE ON "public"."event_user" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;
