BEGIN;


-- CREATE FIELD "live_to_public" -------------------------------
ALTER TABLE "public"."event" ADD COLUMN "live_to_public" Boolean DEFAULT 'false' NOT NULL;
-- -------------------------------------------------------------;

-- CREATE FIELD "tickets_email_text" ---------------------------
ALTER TABLE "public"."event" ADD COLUMN "tickets_email_text" Text;
-- -------------------------------------------------------------;

-- CREATE FIELD "tickets_locations" ----------------------------
ALTER TABLE "public"."event" ADD COLUMN "tickets_locations" JSON;
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CHANGE "NAME" OF "FIELD "tickets_email_text" ----------------
ALTER TABLE "public"."event" RENAME COLUMN "tickets_email_text" TO "tickets_description";
-- -------------------------------------------------------------;

COMMIT;


