-- CREATE TYPE "reg_method" ------------------------------------
CREATE TYPE "public"."reg_method" AS Enum( 'club', 'doubles' );
-- -------------------------------------------------------------

BEGIN;


-- CREATE FIELD "registration_method" --------------------------
ALTER TABLE "public"."event" ADD COLUMN "registration_method" "public"."reg_method" DEFAULT 'club';
-- -------------------------------------------------------------

-- CREATE FIELD "usav_required" --------------------------------
ALTER TABLE "public"."event" ADD COLUMN "usav_required" Boolean DEFAULT 'false';
-- -------------------------------------------------------------;

COMMIT;
