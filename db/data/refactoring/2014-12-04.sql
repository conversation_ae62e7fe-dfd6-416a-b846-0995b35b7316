---
--- Creating table event_official
---

BEGIN;

-- CREATE TABLE "event_official" -------------------------------
CREATE TABLE "public"."event_official" (
	"event_official_id" Serial NOT NULL UNIQUE,
	"created" TIMESTA<PERSON> WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"official_id" INTEGER NOT NULL,
	"event_id" INTEGER NOT NULL,
	"travel_method" CHARACTER VARYING( 20 ),
	"able_to_transport_others" BOOLEAN,
	"need_hotel_room" BOOLEAN,
	"rommate_preference" CHARACTER VARYING( 200 ),
	"schedule_availability" JSON,
	"departure_time" CHARACTER VARYING( 200 ),
	"additional_restrictions" CHARACTER VARYING( 200 ),
	"is_staff" BOOLEAN,
	"is_official" BOOLEAN,
 PRIMARY KEY ( "event_official_id" )
, CONSTRAINT "unique_event_official_id" UNIQUE( "event_official_id" ) );
CREATE INDEX "index_event_official_event_id" ON "public"."event_official" USING btree( "event_id" );


CREATE INDEX "index_sponsor_id" ON "public"."event_official" USING btree( "official_id" );

COMMENT ON TABLE  "public"."event_official" IS 'Event Official Check-ins and event related data';
-- Set comments for fields
COMMENT ON COLUMN "public"."event_official"."official_id" IS 'Staff / Official unique ID';
COMMENT ON COLUMN "public"."event_official"."event_id" IS 'Event ID to check-in';
COMMENT ON COLUMN "public"."event_official"."travel_method" IS 'Travel Method: Car, Air, other';
COMMENT ON COLUMN "public"."event_official"."able_to_transport_others" IS '(If Travel Method is Car) Are you able to transport other officials  to play sites if necessary: YES/NO';
COMMENT ON COLUMN "public"."event_official"."need_hotel_room" IS 'Requesting a Hotel Room: YES/NO';
COMMENT ON COLUMN "public"."event_official"."rommate_preference" IS 'Rommmate Preference';
COMMENT ON COLUMN "public"."event_official"."schedule_availability" IS 'Array with Schedule Availability at 1st, 2nd... days of events';
COMMENT ON COLUMN "public"."event_official"."departure_time" IS '(If 3rd, or last, Day is Yes) Departure Time  Required from the Facility';
COMMENT ON COLUMN "public"."event_official"."additional_restrictions" IS 'Additional Schedule Restrictions';
COMMENT ON COLUMN "public"."event_official"."is_staff" IS 'Is Staff (Y/N)';
COMMENT ON COLUMN "public"."event_official"."is_official" IS 'Is Official (Y/N)';
-- -------------------------------------------------------------;

COMMIT;

---
--- Adding country field
---

BEGIN;

-- CREATE FIELD "country" --------------------------------------
ALTER TABLE "public"."official" ADD COLUMN "country" CHARACTER VARYING( 2 );COMMENT ON COLUMN "public"."official"."country" IS 'Country value';
-- -------------------------------------------------------------;

COMMIT;


