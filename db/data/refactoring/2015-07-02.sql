BEGIN;

ALTER TABLE courts ALTER COLUMN modified DROP NOT NULL;
ALTER TABLE courts ALTER COLUMN modified DROP DEFAULT;
ALTER TABLE courts ALTER COLUMN modified TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(modified) AT TIME ZONE 'UTC';


ALTER TABLE matches ALTER COLUMN modified DROP NOT NULL;
ALTER TABLE matches ALTER COLUMN modified DROP DEFAULT;
ALTER TABLE matches ALTER COLUMN modified TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(modified) AT TIME ZONE 'UTC';

ALTER TABLE matches ALTER COLUMN secs_start DROP NOT NULL;
ALTER TABLE matches ALTER COLUMN secs_start DROP DEFAULT;
ALTER TABLE matches ALTER COLUMN secs_start TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(secs_start) AT TIME ZONE 'UTC';

ALTER TABLE matches ALTER COLUMN secs_end DROP NOT NULL;
ALTER TABLE matches ALTER COLUMN secs_end DROP DEFAULT;
ALTER TABLE matches ALTER COLUMN secs_end TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(secs_end) AT TIME ZONE 'UTC';

ALTER TABLE matches ALTER COLUMN secs_finished DROP NOT NULL;
ALTER TABLE matches ALTER COLUMN secs_finished DROP DEFAULT;
ALTER TABLE matches ALTER COLUMN secs_finished TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(secs_finished) AT TIME ZONE 'UTC';

ALTER TABLE poolbrackets ALTER COLUMN modified DROP NOT NULL;
ALTER TABLE poolbrackets ALTER COLUMN modified DROP DEFAULT;
ALTER TABLE poolbrackets ALTER COLUMN modified TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(modified) AT TIME ZONE 'UTC';


ALTER TABLE results ALTER COLUMN moment DROP NOT NULL;
ALTER TABLE results ALTER COLUMN moment DROP DEFAULT;
ALTER TABLE results ALTER COLUMN moment TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(moment) AT TIME ZONE 'UTC';

ALTER TABLE rounds ALTER COLUMN modified DROP NOT NULL;
ALTER TABLE rounds ALTER COLUMN modified DROP DEFAULT;
ALTER TABLE rounds ALTER COLUMN modified TYPE TIMESTAMP WITHOUT TIME ZONE USING to_timestamp(modified) AT TIME ZONE 'UTC';


COMMIT;

UPDATE matches SET secs_finished = NULL
WHERE secs_finished = '1970-01-01 00:00:00';

