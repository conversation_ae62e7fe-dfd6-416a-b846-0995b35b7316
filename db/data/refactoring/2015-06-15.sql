BEGIN;


-- CREATE FIELD "application_fee" ------------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "application_fee" Numeric DEFAULT '0';
-- -------------------------------------------------------------;

COMMIT;


CREATE SEQUENCE "public"."purchase_ticket_id_seq"
INCREMENT 1
MINVALUE 1
MAXVALUE 9223372036854775807
START 1
CACHE 1;


BEGIN;


-- CREATE TABLE "purchase_ticket" ------------------------------
CREATE TABLE "public"."purchase_ticket" ( 
    "purchase_ticket_id" INTEGER DEFAULT nextval('purchase_ticket_id_seq'::regclass) NOT NULL UNIQUE, 
    "created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now(), 
    "modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL, 
    "purchase_id" INTEGER NOT NULL, 
    "amount" NUMERIC NOT NULL, 
    "ticket_price" NUMERIC NOT NULL, 
    "quantity" INTEGER NOT NULL, 
    "canceled" TIMESTAMP WITHOUT TIME ZONE
, CONSTRAINT "unique_purchase_ticket_id" UNIQUE( "purchase_ticket_id" ) );


BEGIN;


-- CREATE FIELD "available" ------------------------------------
ALTER TABLE "public"."purchase_ticket" ADD COLUMN "available" Integer DEFAULT '0' NOT NULL;
-- -------------------------------------------------------------;

COMMIT;
