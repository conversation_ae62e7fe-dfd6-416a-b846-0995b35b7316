BEGIN;

-- CREATE FIELD "tickets_discount" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "tickets_discount" JSONB;
COMMENT ON COLUMN "public"."event"."tickets_discount" IS 'This field contains SWT discount that applies to the total payment amount taking into account picked items quantity or/and result amount';
-- -------------------------------------------------------------

-- CREATE FIELD "purchase_discount" ----------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "purchase_discount" Numeric DEFAULT 0 NOT NULL;
COMMENT ON COLUMN "public"."purchase"."purchase_discount" IS 'Discount that might be applied to the payment according to payment''s amount and/or picked items';
-- -------------------------------------------------------------

COMMIT;
