---
--- Creating table email_template
---

BEGIN;

-- CREATE TABLE "email_template" -------------------------------
CREATE TABLE "public"."email_template" (
	"created" TIMES<PERSON>MP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"email_html" TEXT NOT NULL,
	"email_subject" CHARACTER VARYING( 250 ),
	"email_template_id" Serial NOT NULL,
	"email_text" TEXT,
	"event_owner_id" INTEGER NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"recipient_type" CHARACTER VARYING( 20 ) NOT NULL,
	"sender_type" CHARACTER VARYING( 20 ) NOT NULL,
	"title" CHARACTER VARYING( 200 ) NOT NULL,
 PRIMARY KEY ( "email_template_id" )
 );
CREATE INDEX "index_event_owner_id1" ON "public"."email_template" USING btree( "event_owner_id" );


-- Set comments for fields
COMMENT ON COLUMN "public"."email_template"."email_html" IS 'Email Body HTML Version';
COMMENT ON COLUMN "public"."email_template"."email_subject" IS 'Email Subject';
COMMENT ON COLUMN "public"."email_template"."email_text" IS 'Email Body Plain Text Version';
COMMENT ON COLUMN "public"."email_template"."event_owner_id" IS 'Event Owner ID';
COMMENT ON COLUMN "public"."email_template"."recipient_type" IS 'Recipient Type: ''club'', ''official'', etc.';
COMMENT ON COLUMN "public"."email_template"."sender_type" IS 'Sender Type: ''event'', etc.';
COMMENT ON COLUMN "public"."email_template"."title" IS 'Email Template Title';
-- -------------------------------------------------------------;

COMMIT;



---
--- Adding columns to event table to link to Email Templates needed
---

BEGIN;

-- CREATE FIELD "official_applied_email_template_id" -----------
ALTER TABLE "public"."event" ADD COLUMN "official_applied_email_template_id" INTEGER;
COMMENT ON COLUMN "public"."event"."official_applied_email_template_id" IS 'Email Template ID for Official Applied Email';
-- -------------------------------------------------------------

-- CREATE FIELD "official_accepted_email_template_id" ----------
ALTER TABLE "public"."event" ADD COLUMN "official_accepted_email_template_id" INTEGER;
COMMENT ON COLUMN "public"."event"."official_accepted_email_template_id" IS 'Email Template ID for Official Accepted Email';
-- -------------------------------------------------------------

-- CREATE FIELD "official_waitlisted_email_template_id" --------
ALTER TABLE "public"."event" ADD COLUMN "official_waitlisted_email_template_id" INTEGER;
COMMENT ON COLUMN "public"."event"."official_waitlisted_email_template_id" IS 'Email Template ID for Official Waitlisted Email';
-- -------------------------------------------------------------

-- CREATE FIELD "official_declined_email_template_id" ----------
ALTER TABLE "public"."event" ADD COLUMN "official_declined_email_template_id" INTEGER;
COMMENT ON COLUMN "public"."event"."official_declined_email_template_id" IS 'Email Template ID for Official Declined Email';
-- -------------------------------------------------------------;

COMMIT;

