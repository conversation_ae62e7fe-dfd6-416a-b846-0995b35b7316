--
-- changing 'date' column from boolean to Timestamp for roster tables
--


-- === roster_team ===

-- CHANGE "NULLABLE" OF "FIELD "deleted" -----------------------
ALTER TABLE "public"."roster_team" ALTER COLUMN "deleted" DROP NOT NULL;
-- -------------------------------------------------------------;

-- CHANGE "COMMENT" OF "FIELD "deleted" ------------------------
COMMENT ON COLUMN "public"."roster_team"."deleted" IS 'Has timrstamp if row deleted. NULL means row is in roster.';
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "deleted" ------------------
ALTER TABLE "public"."roster_team" ALTER COLUMN "deleted" DROP DEFAULT ;
-- -------------------------------------------------------------;

-- CHANGE "TYPE" OF "FIELD "deleted" ---------------------------
ALTER TABLE "public"."roster_team" ALTER COLUMN "deleted" TYPE TIMESTAMP WITHOUT TIME ZONE USING NULL;
-- -------------------------------------------------------------


-- === roster_club ===

-- CHANGE "NULLABLE" OF "FIELD "deleted" -----------------------
ALTER TABLE "public"."roster_club" ALTER COLUMN "deleted" DROP NOT NULL;
-- -------------------------------------------------------------;

-- CHANGE "COMMENT" OF "FIELD "deleted" ------------------------
COMMENT ON COLUMN "public"."roster_club"."deleted" IS 'Has timrstamp if row deleted. NULL means row is in roster.';
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "deleted" ------------------
ALTER TABLE "public"."roster_club" ALTER COLUMN "deleted" DROP DEFAULT ;
-- -------------------------------------------------------------;

-- CHANGE "TYPE" OF "FIELD "deleted" ---------------------------
ALTER TABLE "public"."roster_club" ALTER COLUMN "deleted" TYPE TIMESTAMP WITHOUT TIME ZONE USING NULL;
-- -------------------------------------------------------------


-- === roster_athlete ===

-- CHANGE "NULLABLE" OF "FIELD "deleted" -----------------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "deleted" DROP NOT NULL;
-- -------------------------------------------------------------;

-- CHANGE "COMMENT" OF "FIELD "deleted" ------------------------
COMMENT ON COLUMN "public"."roster_athlete"."deleted" IS 'Has timrstamp if row deleted. NULL means row is in roster.';
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "deleted" ------------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "deleted" DROP DEFAULT ;
-- -------------------------------------------------------------;

-- CHANGE "TYPE" OF "FIELD "deleted" ---------------------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "deleted" TYPE TIMESTAMP WITHOUT TIME ZONE USING NULL;
-- -------------------------------------------------------------


-- === roster_staff ===

-- CHANGE "NULLABLE" OF "FIELD "deleted" -----------------------
ALTER TABLE "public"."roster_staff" ALTER COLUMN "deleted" DROP NOT NULL;
-- -------------------------------------------------------------;

-- CHANGE "COMMENT" OF "FIELD "deleted" ------------------------
COMMENT ON COLUMN "public"."roster_staff"."deleted" IS 'Has timrstamp if row deleted. NULL means row is in roster.';
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "deleted" ------------------
ALTER TABLE "public"."roster_staff" ALTER COLUMN "deleted" DROP DEFAULT ;
-- -------------------------------------------------------------;

-- CHANGE "TYPE" OF "FIELD "deleted" ---------------------------
ALTER TABLE "public"."roster_staff" ALTER COLUMN "deleted" TYPE TIMESTAMP WITHOUT TIME ZONE USING NULL;
-- -------------------------------------------------------------


-- === Adding indexes to roster tables ===

-- CHANGE "INDEXED" OF "FIELD "deleted" ------------------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CREATE INDEX "index_roster_team_deleted" --------------------------------
CREATE INDEX "index_roster_team_deleted" ON "public"."roster_team" USING btree( "deleted" );
-- -------------------------------------------------------------;

-- CREATE INDEX "index_roster_club_deleted" --------------------
CREATE INDEX "index_roster_club_deleted" ON "public"."roster_club" USING btree( "deleted" );
-- -------------------------------------------------------------;

-- CREATE INDEX "index_roster_staff_deleted" --------------------
CREATE INDEX "index_roster_staff_deleted" ON "public"."roster_staff" USING btree( "deleted" );
-- -------------------------------------------------------------;

-- CREATE INDEX "index_roster_athlete_deleted" --------------------
CREATE INDEX "index_roster_athlete_deleted" ON "public"."roster_athlete" USING btree( "deleted" );
-- -------------------------------------------------------------;


-- === Creating column 'deleted' in schedule table ===

-- CREATE FIELD "deleted" --------------------------------------
ALTER TABLE "public"."schedule" ADD COLUMN "deleted" TIMESTAMP WITHOUT TIME ZONE;COMMENT ON COLUMN "public"."schedule"."deleted" IS 'Has timrstamp if row deleted. NULL means row is active.';
-- -------------------------------------------------------------

-- CREATE INDEX "index_schedule_deleted" -----------------------
CREATE INDEX "index_schedule_deleted" ON "public"."schedule" USING btree( "deleted" );
-- -------------------------------------------------------------;
