BEGIN;


-- CREATE FIELD "number" ---------------------------------------
ALTER TABLE "public"."event_location" ADD COLUMN "number" INTEGER DEFAULT '1';COMMENT ON COLUMN "public"."event_location"."number" IS 'Location number within event. Main location should have 1.';
-- -------------------------------------------------------------;

COMMIT;


--
-- creating a table for sport variations available for club
--
BEGIN;
-- CREATE TABLE "master_club_sport_variation" ----------------------
CREATE TABLE "public"."master_club_sport_variation" (
	"master_club_sport_variation_id" Serial NOT NULL UNIQUE,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIM<PERSON><PERSON>MP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"master_club_id" INTEGER NOT NULL,
	"sport_variation_id" INTEGER NOT NULL,
 PRIMARY KEY ( "master_club_sport_variation_id" )
, CONSTRAINT "unique_master_club_sport_variation_id" UNIQUE( "master_club_sport_variation_id" ) );

COMMENT ON TABLE  "public"."master_club_sport_variation" IS 'List of available sport variations for a club';
-- -------------------------------------------------------------;

-- CREATE UNIQUE "master_club_sport_variation_unique" ----------
ALTER TABLE "public"."master_club_sport_variation" ADD CONSTRAINT "master_club_sport_variation_unique" UNIQUE( "master_club_id","sport_variation_id" );
-- -------------------------------------------------------------;

COMMIT;



---
--- Adding has_gender_teams fields to master_club table
---

BEGIN;

-- CREATE FIELD "has_male_teams" -------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "has_male_teams" BOOLEAN DEFAULT 'false' NOT NULL;
-- -------------------------------------------------------------

-- CREATE FIELD "has_female_teams" -----------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "has_female_teams" BOOLEAN DEFAULT 'false' NOT NULL;
-- -------------------------------------------------------------

-- CREATE FIELD "has_coed_teams" -------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "has_coed_teams" BOOLEAN DEFAULT 'false' NOT NULL;
-- -------------------------------------------------------------;

COMMIT;


---
--- Changes to Events table
---
BEGIN;


-- CHANGE "COMMENT" OF "FIELD "host" ---------------------------
COMMENT ON COLUMN "public"."event"."host" IS '-- deprecated. replaced to fields: hosting_org_*';
-- -------------------------------------------------------------

-- CREATE FIELD "has_late_reg" ---------------------------------
ALTER TABLE "public"."event" ADD COLUMN "has_late_reg" BOOLEAN DEFAULT 'false';COMMENT ON COLUMN "public"."event"."has_late_reg" IS 'True if this event has values for Late Registration Date and Late Registration Penalty';
-- -------------------------------------------------------------

-- CREATE FIELD "housing_company_id" ---------------------------
ALTER TABLE "public"."event" ADD COLUMN "housing_company_id" INTEGER DEFAULT '0';COMMENT ON COLUMN "public"."event"."housing_company_id" IS 'Housing Company ID';
-- -------------------------------------------------------------

-- CREATE FIELD "custom_housing_company" -----------------------
ALTER TABLE "public"."event" ADD COLUMN "custom_housing_company" CHARACTER VARYING( 200 );COMMENT ON COLUMN "public"."event"."custom_housing_company" IS 'Custom Housing Company name - available only if Housing Company is ''Other'' (housing_company_id = 0)';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_name" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_name" CHARACTER VARYING( 200 );COMMENT ON COLUMN "public"."event"."hosting_org_name" IS 'Hosting Organization Name';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_address" --------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_address" CHARACTER VARYING( 200 );COMMENT ON COLUMN "public"."event"."hosting_org_address" IS 'Hosting Organization Address';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_city" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_city" CHARACTER VARYING( 200 );COMMENT ON COLUMN "public"."event"."hosting_org_city" IS 'Hosting Organization City';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_state" ----------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_state" CHARACTER VARYING( 2 );COMMENT ON COLUMN "public"."event"."hosting_org_state" IS 'Hosting Organization State';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_zip" ------------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_zip" CHARACTER VARYING( 10 );COMMENT ON COLUMN "public"."event"."hosting_org_zip" IS 'Hosting Organization ZIP';
-- -------------------------------------------------------------

-- CREATE FIELD "hosting_org_phone" ----------------------------
ALTER TABLE "public"."event" ADD COLUMN "hosting_org_phone" CHARACTER VARYING( 100 );COMMENT ON COLUMN "public"."event"."hosting_org_phone" IS 'Hosting Organization ZIP';
-- -------------------------------------------------------------;

COMMIT;


---
--- Creating table for Housing Companies
---

BEGIN;

-- CREATE TABLE "housing_company" ------------------------------
CREATE TABLE "public"."housing_company" (
	"housing_company_id" Serial NOT NULL UNIQUE,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"name" CHARACTER VARYING( 200 ) NOT NULL
, CONSTRAINT "unique_housing_company_id" UNIQUE( "housing_company_id" ) );COMMENT ON TABLE  "public"."housing_company" IS 'Housing Companies';
-- -------------------------------------------------------------;

COMMIT;

