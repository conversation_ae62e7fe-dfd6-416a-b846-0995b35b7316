BEGIN;

-- CREATE FIELD "allow_check_payments" -------------------------
ALTER TABLE "public"."event" ADD COLUMN "allow_check_payments" Boolean DEFAULT false;COMMENT ON COLUMN "public"."event"."allow_check_payments" IS 'Allows to create CHECK payments for teams entry';
-- -------------------------------------------------------------

-- CREATE FIELD "allow_ach_payments" ---------------------------
ALTER TABLE "public"."event" ADD COLUMN "allow_ach_payments" Boolean DEFAULT false;COMMENT ON COLUMN "public"."event"."allow_ach_payments" IS 'Allows to create ACH payments for teams entry';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE FIELD "teams_use_connect" ----------------------------
ALTER TABLE "public"."event" ADD COLUMN "teams_use_connect" Boolean DEFAULT false;
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE FIELD "plaid_teams_public_key" -----------------------
ALTER TABLE "public"."event" ADD COLUMN "plaid_teams_public_key" Text;
-- -------------------------------------------------------------

-- CREATE FIELD "plaid_teams_private_key" ----------------------
ALTER TABLE "public"."event" ADD COLUMN "plaid_teams_private_key" Text;
-- -------------------------------------------------------------

COMMIT;
