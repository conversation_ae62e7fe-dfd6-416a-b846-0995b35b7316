BEGIN;


-- RENAME old ENUM -----------------------------------------------------
ALTER TYPE "public"."system_job_status"
  RENAME TO system_job_status_old;
------------------------------------------------------------------------

-- CREATE new ENUM with correct values order ---------------------------
CREATE TYPE "public"."system_job_status" AS ENUM ('scheduled', 'started', 'recipients_list_generation', 'sending_emails', 'done');
------------------------------------------------------------------------

-- RENAME table column that used old ENUM ------------------------------
ALTER TABLE "public"."system_job"
  RENAME COLUMN "status" to "old_status";
------------------------------------------------------------------------

-- CREATE new table column that will use new ENUM ----------------------
ALTER TABLE "public"."system_job"
  ADD "status" system_job_status NOT NULL DEFAULT 'scheduled' :: system_job_status;
------------------------------------------------------------------------

-- MOVE values from old column to new one ------------------------------
UPDATE "public"."system_job"
SET "status" = "old_status" :: text :: system_job_status;
------------------------------------------------------------------------

-- DELETE old table column ---------------------------------------------
ALTER TABLE "public"."system_job"
  DROP COLUMN "old_status";
------------------------------------------------------------------------

-- DELETE old ENUM -----------------------------------------------------
DROP TYPE system_job_status_old;
------------------------------------------------------------------------


COMMIT;
