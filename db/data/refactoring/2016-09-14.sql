BEGIN;

-- DROP FIELD "stripe_publishable_key" -------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_publishable_key";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_secret_key" ------------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_secret_key";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_email" -------------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_acc_email";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_date_modified" ---------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_date_modified";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_name" --------------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_acc_name";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_id" ----------------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_acc_id";
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- DROP FIELD "stripe_secret_key" ------------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_secret_key";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_publishable_key" -------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_publishable_key";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_email" -------------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_acc_email";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_date_modified" ---------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_date_modified";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_name" --------------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_acc_name";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_statement" ---------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_acc_statement";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_acc_id" ----------------------------------
ALTER TABLE "public"."event_owner" DROP COLUMN "stripe_acc_id";
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- DROP FIELD "stripe_teams_public_key" ------------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_teams_public_key";
-- -------------------------------------------------------------

-- DROP FIELD "stripe_tickets_public_key" ----------------------
ALTER TABLE "public"."event" DROP COLUMN "stripe_tickets_public_key";
-- -------------------------------------------------------------

COMMIT;


-- Updating schedule_name for event_official table
UPDATE event_official
SET schedule_name = (
select (u.first || ' ' || substr(u.last, 1, 1) || '.') schedule_name
from  official o
INNER JOIN "user" u ON u.user_id = o.user_id
where o.official_id = event_official.official_id
);
