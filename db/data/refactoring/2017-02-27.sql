-------------------------- update old evant_change actions to new format ---------------------------------------

UPDATE event_change ec
  SET action = (
      SELECT (CASE
        WHEN ech.action = 'Club deleted' THEN 'club.deleted'
        WHEN ech.action = 'Club entered' THEN 'club.entered'
        WHEN ech.action = 'Club entered the event' THEN 'club.entered'
        WHEN ech.action = 'Invoice created' THEN 'payment.check.pending'
        WHEN ech.action = 'Paid by card' THEN 'payment.paid.card'
        WHEN ech.action = 'Paid by check' THEN 'payment.paid.check'
        WHEN ech.action = 'Roster team is changed' THEN 'team.roster.changed'
        WHEN ech.action = 'Team accepted' THEN 'team.entry.accepted'
        WHEN ech.action = 'Team added to roster' THEN 'team.entered'
        WHEN ech.action = 'Team declined' THEN 'team.entry.declined'
        WHEN ech.action = 'Team deleted' THEN 'team.deleted'
        WHEN ech.action = 'Team entered' THEN 'team.entered'
        WHEN ech.action = 'Team entry pending' THEN 'team.entry.pending'
        WHEN ech.action = 'Team is waiting for entrance' THEN 'team.entry.waiting'
        WHEN ech.action = 'Team pending' THEN 'team.entry.pending'
        WHEN ech.action = 'Team waiting' THEN 'team.entry.waiting'
        WHEN ech.action = 'TeamCheckInStatusCheckedIn' THEN 'team.checkin.checkedin'
        WHEN ech.action = 'TeamCheckInStatusNotCheckedIn' THEN 'team.checkin.notcheckedin'
        WHEN ech.action = 'club deleted from the event' THEN 'club.deleted'
        WHEN ech.action = 'TeamCheckInStatusPending' THEN 'team.checkin.pending'
        WHEN ech.action = 'club reentered the  event' THEN 'club.entered'
        WHEN ech.action = 'team deleted' THEN 'team.deleted'
        WHEN ech.action = 'Team division changed' THEN 'team.division.changed'
        WHEN ech.action = 'division changed' THEN 'team.division.changed'
        WHEN ech.action = 'Club marked as local' THEN 'club.marked.local'
        WHEN ech.action = 'Email sent' THEN 'email.sent'
        WHEN ech.action = 'Club marked as not local' THEN 'club.marked.notlocal'
        END) as action
      FROM event_change ech
      WHERE ech.event_change_id = ec.event_change_id
  )
WHERE ec.action IN ('Club deleted', 'Club entered', 'Club entered the event', 'Invoice created', 'Paid by card',
'Paid by check', 'Roster team is changed', 'Team is waiting for entrance', 'Team is waiting for entrance',
'Team is waiting for entrance', 'Team pending', 'Team waiting', 'TeamCheckInStatusCheckedIn', 'TeamCheckInStatusNotCheckedIn',
'club deleted from the event', 'TeamCheckInStatusPending', 'club reentered the  event', 'team deleted', 'Team division changed',
'division changed', 'Club marked as local', 'Email sent', 'Team added to roster', 'Team accepted', 'Team declined',
'Team entry pending', 'Club marked as not local', 'Team entered', 'Team deleted')
