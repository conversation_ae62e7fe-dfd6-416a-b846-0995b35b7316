---
--- Adding column to indicate if event has players roster available
---
BEGIN;

-- CREATE FIELD "has_rosters" ----------------------------
ALTER TABLE "public"."event" ADD COLUMN "has_rosters" B<PERSON><PERSON><PERSON>N DEFAULT 'false';
-- -------------------------------------------------------------

COMMIT;


---
--- Creating event_journal table
---
CREATE TABLE "public"."event_journal" (
	"id" UUid DEFAULT uuid_generate_v4() NOT NULL UNIQUE,
	"moment" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"event_id" INTEGER NOT NULL,
	"table_name" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"method" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"created_by" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"data" CHARACTER VARYING( 2044 ) COLLATE "pg_catalog"."default" NOT NULL
, CONSTRAINT "event_journal_id_key" UNIQUE( "id" ) );


--- Table event_journal Trigger ---

CREATE OR REPLACE FUNCTION public.event_journal_sync_function()
RETURNS TRIGGER AS $$
DECLARE
    sql text;
    sql_params text;
    obj json;
    where_obj json;
    m text;
    iter integer;
    method text;
BEGIN
    method := lower(NEW.method);
    IF method = 'insert' THEN
        obj := NEW.data::json->'data';
        sql_params := array_to_string(array(SELECT * FROM json_object_keys(obj)), ',');
        sql := format('INSERT INTO %s ( %s, event_id ) (SELECT %s, %s FROM json_populate_record(NULL::%s, $1))',
                NEW.table_name, sql_params, sql_params, NEW.event_id, NEW.table_name);
        RAISE NOTICE '%', sql_params;
        EXECUTE sql USING  obj;
    ELSEIF method = 'update' THEN
        iter := 0;
        obj := NEW.data::json->'data';
        where_obj := NEW.data::json->'where';
        sql:= format('UPDATE %s tn SET event_id = %s, ', NEW.table_name, NEW.event_id);
        foreach m in array array(SELECT * FROM json_object_keys(obj))
        loop
            if iter > 0 then
                sql := format('%s, "%s" = sq."%s" ', sql, m, m);
            else
                sql := format('%s "%s" = sq."%s" ', sql, m, m);
            end if;
            iter := iter + 1;
        end loop;
        sql :=
            format('%s FROM ( select %s from json_populate_record(NULL::%s, $1)) AS sq WHERE cast(tn.match_id as text) = $2',
                    sql, array_to_string(array(SELECT * FROM json_object_keys(obj)), ', '),
                    NEW.table_name
                );
        EXECUTE sql USING obj, where_obj->>'id';
    ELSEIF method = 'delete' THEN
        where_obj := NEW.data::json->'where';
        sql := format('DELETE FROM %s WHERE cast(match_id as text) = $1', NEW.table_name);
        EXECUTE sql USING where_obj->>'id';
    END IF;
    RETURN NEW;
END
$$
LANGUAGE plpgsql

DROP TRIGGER IF EXISTS event_journal_trigger ON event_journal;

CREATE TRIGGER event_journal_trigger
AFTER INSERT
ON event_journal FOR EACH ROW
EXECUTE PROCEDURE event_journal_sync_function();


-----------------------------------------------------------------
