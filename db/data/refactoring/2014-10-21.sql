---
--- Adding assigned Sales Manager field to events table
---
BEGIN;

-- CREATE FIELD "sales_manager_id" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "sales_manager_id" INTEGER;
COMMENT ON COLUMN "public"."event"."sales_manager_id" IS 'Sales Manager ID assigned to this event';
-- -------------------------------------------------------------;

COMMIT;


---
--- Adding event_owner_id to tables: purchase and sponsor
---
BEGIN;

-- CHANGE "COMMENT" OF "FIELD "sales_manager_id" ---------------
COMMENT ON COLUMN "public"."purchase"."sales_manager_id" IS 'Sales Manager ID for Exhibitor purchases created by SM';
-- -------------------------------------------------------------

-- CREATE FIELD "event_owner_id" -------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "event_owner_id" INTEGER;
COMMENT ON COLUMN "public"."purchase"."event_owner_id" IS 'Event Owner ID for Exhibitor purchases created by EO';
-- -------------------------------------------------------------;


-- CREATE FIELD "added_by_event_id" ----------------------------
ALTER TABLE "public"."sponsor" ADD COLUMN "added_by_event_id" INTEGER;
COMMENT ON COLUMN "public"."sponsor"."added_by_event_id" IS 'Event Owner ID who created this Sponsor/Exhibitor';
-- -------------------------------------------------------------;

COMMIT;
