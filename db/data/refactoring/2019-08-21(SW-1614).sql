BEGIN;

-- official_additional_payment_category table --
CREATE TABLE "public"."official_additional_payment_category" (
	"official_additional_payment_category_id" Serial,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	"modified" TIMES<PERSON>MP WITHOUT TIME ZONE DEFAULT NOW(),
	"category" VARCHAR(50) NOT NULL,
	"event_id" INTEGER,
	PRIMARY KEY ("official_additional_payment_category_id"),
	CONSTRAINT "official_additional_payment_category_unique" UNIQUE("category", "event_id")
);
COMMENT ON COLUMN official_additional_payment_category.category is 'Name of caterogy e.g. Parking, Meals, Travel';
COMMENT ON COLUMN official_additional_payment_category.event_id is 'If the event_id is filled it means that the category belongs to event, if the event_id is null - category belongs to all events';

-- event_official_additional_payment table --
CREATE TABLE "public"."event_official_additional_payment" (
	"event_official_additional_payment_id" Serial,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	"amount" NUMERIC(10, 2),
	"official_additional_payment_category_id" INTEGER NOT NULL,
	"event_official_id" INTEGER NOT NULL,
	"event_id" INTEGER NOT NULL,
	PRIMARY KEY ("event_official_additional_payment_id"),
	CONSTRAINT "event_official_additional_payment_unique" UNIQUE ("official_additional_payment_category_id", "event_official_id", "event_id")
);

COMMENT ON COLUMN event_official_additional_payment.amount IS 'Amount to pay for the category';
COMMENT ON COLUMN event_official_additional_payment.official_additional_payment_category_id IS 'Foreign key';
COMMENT ON COLUMN event_official_additional_payment.event_official_id IS 'Foreign key';
COMMENT ON COLUMN event_official_additional_payment.event_id IS 'Foreign key';

-- defaults additional categories --
INSERT INTO official_additional_payment_category (category)
    VALUES('Parking'), ('Meals'), ('Travel'), ('Miscellaneous');

COMMIT;