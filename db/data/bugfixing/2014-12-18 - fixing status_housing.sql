--- We should not have status_housing = 0 anymore

--- -- Query to check that:
-- SELECT *
-- FROM roster_team
-- WHERE status_housing = 0
-- AND COALESCE(is_local, FALSE) = FALSE
-- ORDER BY total_accepted DESC;


UPDATE roster_team
SET status_housing = 31
WHERE status_housing = 0
AND COALESCE(is_local, FALSE) = FALSE;


-- --- Check if db has teams with wrong status_housing
-- SELECT rt.roster_team_id, e.name, rt.team_name, rt.is_local, rc.is_local, rt.status_housing, rt.total_accepted, e.housing_nights_required,
-- COALESCE((
-- SELECT CASE WHEN rt2.total_accepted < e.housing_nights_required THEN 31
-- 		    WHEN rt2.total_accepted >= e.housing_nights_required THEN 32
-- 		    ELSE 31 END
-- FROM roster_team rt2
--   INNER JOIN "event" e ON e.event_id = rt.event_id
--   WHERE rt2.roster_team_id = rt.roster_team_id
--   ), 34) new_housing
--   FROM roster_team rt
--   INNER JOIN roster_club rc ON rc.roster_club_id = rt.roster_club_id
--   INNER JOIN "event" e ON e.event_id = rt.event_id
--   WHERE (rt.is_local IS NULL OR rt.is_local = FALSE)
--   AND COALESCE(rt.is_local, FALSE) = FALSE
--   AND e.has_status_housing = TRUE
--   AND rt.status_housing <>
--   COALESCE((
-- SELECT CASE WHEN rt2.total_accepted < e.housing_nights_required THEN 31
-- 		    WHEN rt2.total_accepted >= e.housing_nights_required THEN 32
-- 		    ELSE 31 END
-- FROM roster_team rt2
--   INNER JOIN "event" e ON e.event_id = rt.event_id
--   WHERE rt2.roster_team_id = rt.roster_team_id
--   ), 34)
--   LIMIT 100;


-- Update status_housing and date_housing in roster_team table
UPDATE roster_team _rt
SET status_housing =
	COALESCE((
		SELECT CASE WHEN e.has_status_housing = FALSE THEN NULL
			WHEN rt2.total_accepted < e.housing_nights_required THEN 31
		    WHEN rt2.total_accepted >= e.housing_nights_required THEN 32
		    ELSE 31 END
		FROM roster_team rt2
		  INNER JOIN "event" e ON e.event_id = rt.event_id
		  WHERE rt2.roster_team_id = rt.roster_team_id
	), 31),
  date_housing = (
		CASE WHEN rt.total_accepted < e.housing_nights_required THEN NULL
			 ELSE (SELECT tb.ths_when_accepted
                         FROM ths_booking tb
                         WHERE tb.ths_hotel_status IN ('Confirmed', 'Accepted')
                           AND tb.roster_team_id = rt.roster_team_id
                           ORDER BY tb.ths_when_accepted
                         LIMIT 1)
             END
  )
  FROM roster_team rt
  INNER JOIN "event" e ON e.event_id = rt.event_id
  WHERE _rt.roster_team_id = rt.roster_team_id
  AND COALESCE(rt.is_local, FALSE) = FALSE
  AND e.has_status_housing = TRUE