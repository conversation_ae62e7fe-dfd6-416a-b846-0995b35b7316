--- UPDATE jerseys
UPDATE master_athlete _ma
SET jersey = ma2.jersey
FROM master_athlete ma
INNER JOIN roster_team rt ON rt.master_team_id = ma.master_team_id
INNER JOIN master_athlete ma2 ON ma2.first = ma.first AND ma2.last = ma.last AND ma2.birthdate = ma.birthdate AND ma2.master_club_id = ma.master_club_id

WHERE _ma.master_athlete_id = ma.master_athlete_id
AND rt.event_id = 21
AND ma.deleted IS NULL
AND ma2.deleted IS NOT NULL

AND ma.jersey IS NULL
AND ma2.jersey IS NOT NULL


--- UPDATE positions
UPDATE master_athlete _ma
SET sport_position_id = ma2.sport_position_id
FROM master_athlete ma
INNER JOIN roster_team rt ON rt.master_team_id = ma.master_team_id
INNER JOIN master_athlete ma2 ON ma2.first = ma.first AND ma2.last = ma.last AND ma2.birthdate = ma.birthdate AND ma2.master_club_id = ma.master_club_id

WHERE _ma.master_athlete_id = ma.master_athlete_id
AND rt.event_id = 21
AND ma.deleted IS NULL
AND ma2.deleted IS NOT NULL
AND ma.sport_position_id IS NULL
AND ma2.sport_position_id IS NOT NULL


---

SELECT ma.jersey, ma2.jersey, ma.*, ma2.*
FROM master_athlete ma
INNER JOIN roster_team rt ON rt.master_team_id = ma.master_team_id
INNER JOIN master_athlete ma2 ON ma2.first = ma.first AND ma2.last = ma.last AND ma2.master_club_id = ma.master_club_id

WHERE rt.event_id = 21
AND ma.deleted IS NULL
AND ma2.deleted IS NOT NULL
AND ma2.gradyear != ma.gradyear
AND ma.jersey IS NULL
AND ma2.jersey IS NOT NULL



SELECT ma.jersey, ma2.jersey, ma.*, ma2.*
FROM master_athlete ma
INNER JOIN roster_team rt ON rt.master_team_id = ma.master_team_id
INNER JOIN master_athlete ma2 ON ma2.first = ma.first AND ma2.last = ma.last AND ma2.gradyear = ma.gradyear AND ma2.master_club_id = ma.master_club_id

WHERE rt.event_id = 21
AND ma.deleted IS NULL
AND ma2.deleted IS NOT NULL

AND ma.jersey IS NULL
AND ma2.jersey IS NOT NULL


SELECT ma.master_club_id, ma.first, ma.last
FROM roster_team rt
INNER JOIN master_athlete ma ON rt.master_team_id = ma.master_team_id
WHERE rt.event_id = 21
AND ma.deleted IS NULL
GROUP BY ma.master_club_id, ma.first, ma.last, ma.birthdate
HAVING count(ma.master_athlete_id) > 1
LIMIT 1000


SELECT a.master_club_id, count (*), mc.club_name
FROM
(SELECT ma.master_club_id, ma.first, ma.last, ma.birthdate
FROM roster_team rt
INNER JOIN master_athlete ma ON rt.master_team_id = ma.master_team_id
WHERE rt.event_id = 21
AND ma.deleted IS NULL
GROUP BY ma.master_club_id, ma.first, ma.last, ma.birthdate
HAVING count(ma.master_athlete_id) > 1
LIMIT 1000) a
LEFT JOIN master_club mc ON mc.master_club_id = a.master_club_id
GROUP BY a.master_club_id, mc.club_name