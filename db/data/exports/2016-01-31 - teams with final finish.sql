select rc.club_name, rt.team_name, rt.organization_code team_code, d.name division, ds.rank
from roster_team rt
  INNER JOIN division d on d.division_id = rt.division_id
  INNER JOIN roster_club rc on rc.roster_club_id = rt.roster_club_id
  LEFT JOIN division_standing ds on ds.event_id = rt.event_id and ds.team_id = rt.roster_team_id
where rt.event_id = 20
and status_entry = 12
ORDER BY ds.rank, d.name