-- Head Coaches in more than one team in BSQ 2016
SELECT d.name, ms.master_staff_id, ms.first, ms.last,
            count(rsr.roster_staff_role_id),
            string_agg(rt.team_name, ', ')
         FROM "roster_team" rt
         INNER JOIN "roster_staff_role" rsr
             ON rt.roster_team_id = rsr.roster_team_id
         inner JOIN division d ON d.division_id = rt.division_id
             --AND rt.event_id = 42
         INNER JOIN event e ON e.event_id = rt.event_id
         INNER JOIN "master_staff" ms
             ON ms.master_staff_id = rsr.master_staff_id
         INNER JOIN "master_staff_role" msr
             ON msr.master_staff_id = rsr.master_staff_id
             AND msr.master_team_id = rsr.master_team_id
         -- LEFT JOIN "role" rr    ON rr.role_id = rsr.role_id
         -- LEFT JOIN "role" mr ON mr.role_id = msr.role_id
         WHERE e.event_id = 42
           AND rsr.deleted IS NULL
           AND rsr.deleted_by_user IS NULL
           AND rt.status_entry = 12
           --and status_checkin <> 'checkedin'
           AND (COALESCE(rsr.role_id, msr.role_id) IN (4) )
  GROUP BY d.name, ms.master_staff_id, ms.first, ms.last
    HAVING count(rsr.roster_staff_role_id) > 1
ORDER BY d.name