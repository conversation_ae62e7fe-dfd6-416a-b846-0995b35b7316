exports.up = function(knex) {
    return knex.raw(`
        UPDATE email_template_group
        SET "variables" = '[
            {
                "field": "event_name",
                "description": "Event Long Name",
                "title": "Event Name",
                "pattern": "{event_name}",
                "is_available_for_subject": true,
                "custom_action": false
            },
            {
                "field": "event_short_name",
                "description": "Event Short Name",
                "title": "Event Short Name",
                "pattern": "{event_short_name}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "team_charge_datetime",
                "description": "Time of charge for Uncollected SW Team Fees/Booth Fee",
                "title": "Team Charge Datetime",
                "pattern": "{team_charge_datetime}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "ticket_charge_datetime",
                "description": "Time of charge for Uncollected SW Ticket Fee",
                "title": "Ticket Charge Datetime",
                "pattern": "{ticket_charge_datetime}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "payment_method",
                "description": "Payment Method: Card / ACH",
                "title": "Payment Method",
                "pattern": "{payment_method}",
                "is_available_for_subject": false,
                "custom_action": true
            },
            {
                "field": "card_last_4",
                "description": "Card Last 4 Number",
                "title": "Card Last 4 Number",
                "pattern": "{card_last_4}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "current_year",
                "description": "Current Year",
                "title": "Current Year",
                "pattern": "{current_year}",
                "is_available_for_subject": false,
                "custom_action": false
            }
        ]'
        WHERE "group" = 'upcoming_uncollected_fee_payments';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE email_template_group
        SET "variables" = '[
            {
                "field": "event_name",
                "description": "Event Long Name",
                "title": "Event Name",
                "pattern": "{event_name}",
                "is_available_for_subject": true,
                "custom_action": false
            },
            {
                "field": "event_short_name",
                "description": "Event Short Name",
                "title": "Event Short Name",
                "pattern": "{event_short_name}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "team_charge_datetime",
                "description": "Time of charge for Uncollected SW Team Fees/Booth Fee",
                "title": "Team Charge Datetime",
                "pattern": "{team_charge_datetime}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "ticket_charge_datetime",
                "description": "Time of charge for Uncollected SW Ticket Fee",
                "title": "Ticket Charge Datetime",
                "pattern": "{ticket_charge_datetime}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "payment_method",
                "description": "Payment Method: Card / ACH",
                "title": "Payment Method",
                "pattern": "{payment_method}",
                "is_available_for_subject": false,
                "custom_action": true
            },
            {
                "field": "card_last_4",
                "description": "Card Last 4 Number",
                "title": "Card Last 4 Number",
                "pattern": "{card_last_4}",
                "is_available_for_subject": false,
                "custom_action": false
            },
            {
                "field": "season",
                "description": "Event Season",
                "title": "Event Season",
                "pattern": "{season}",
                "is_available_for_subject": false,
                "custom_action": false
            }
        ]'
        WHERE "group" = 'upcoming_uncollected_fee_payments';
    `);
};
