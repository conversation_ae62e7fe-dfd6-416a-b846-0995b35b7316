
exports.up = function(knex) {
    return knex.raw(`
        -- Remove club sport sanctioning duplicates ----------------------------------------------------------------
        WITH ordered AS (
          SELECT master_club_sanctioning_id,
            rank() OVER (PARTITION BY master_club_id, sport_sanctioning_id ORDER BY master_club_sanctioning_id) AS rnk
          FROM master_club_sanctioning
        ),
        to_delete AS (
          SELECT master_club_sanctioning_id
          FROM   ordered
          WHERE  rnk > 1
        )
        DELETE FROM master_club_sanctioning WHERE master_club_sanctioning_id IN (SELECT * FROM to_delete);
        -- ---------------------------------------------------------------------------------------------------------
        
        -- Add constraint for master_club_sanctioning on master_club_id and sport_sanctioning_id -------------------
        ALTER TABLE master_club_sanctioning
          ADD CONSTRAINT "master_club_sanctioning_sport_sanctioning_id_master_club_id" 
          UNIQUE (master_club_id, sport_sanctioning_id);
        -- ---------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE master_club_sanctioning
            DROP CONSTRAINT IF EXISTS "master_club_sanctioning_sport_sanctioning_id_master_club_id";
    `)
};
