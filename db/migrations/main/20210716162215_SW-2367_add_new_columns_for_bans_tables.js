
exports.up = function(knex) {
    return knex.schema.raw(`
      ALTER TABLE "public"."banned_email"
          ADD COLUMN "event_id" INTEGER DEFAULT NULL,
          ADD COLUMN "purchase_id" INTEGER DEFAULT NULL;
          
      ALTER TABLE "public"."banned_fingerprint"
          ADD COLUMN "event_id" INTEGER DEFAULT NULL,
          ADD COLUMN "purchase_id" INTEGER DEFAULT NULL;
  `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
      ALTER TABLE "public"."banned_email"
          DROP COLUMN IF EXISTS "event_id" INTEGER DEFAULT NULL,
          DROP COLUMN IF EXISTS "purchase_id" INTEGER DEFAULT NULL;
          
      ALTER TABLE "public"."banned_fingerprint"
          DROP COLUMN IF EXISTS "event_id" INTEGER DEFAULT NULL,
          DROP COLUMN IF EXISTS "purchase_id" INTEGER DEFAULT NULL;
  `)
};
