exports.up = function (knex) {
    return knex.raw(`
        ALTER TABLE ticket_coupon ADD active BOOLEAN DEFAULT TRUE NOT NULL;
        CREATE TABLE ticket_coupon_history
        (
            ticket_coupon_history_id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            created T<PERSON><PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified TIM<PERSON>TAMP WITHOUT TIME ZONE DEFAULT NULL,
            ticket_coupon_id int NOT NULL,
            action TEXT NOT NULL,
            data JSONB DEFAULT '{}'::JSONB NOT NULL
        );
        CREATE INDEX ticket_coupon_history_ticket_coupon_id_ticket_coupon_history_id_index ON public.ticket_coupon_history (ticket_coupon_id, ticket_coupon_history_id DESC);
        CREATE TRIGGER "update_ticket_coupon_history_modified"
            BEFORE UPDATE
            ON "public"."ticket_coupon_history"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
    `);
};

exports.down = function (knex) {
    return knex.raw(`
        DROP TABLE ticket_coupon_history;
        ALTER TABLE ticket_coupon DROP COLUMN active;
    `);
};
