
exports.up = function(knex) {
    return knex.schema.raw(String.raw`
        -- Create tickets.payments template group ------------------------------------------------------------
        INSERT INTO public.email_template_group ("group", title, description, variables, usage_restrictions)
        VALUES ('tickets.payments', 'Tickets Payments',
                '<i>Email templates related to tickets payments that can be sent automatically</i>', '[
            {
              "field": "event_name",
              "title": "Event Name",
              "pattern": "{event_name}",
              "is_available_for_subject": true
            },
            {
              "field": "tickets_receipt_descr",
              "title": "Tickets Receipt Description",
              "pattern": "{tickets_receipt_descr}",
              "custom_action": true
            },
            {
              "field": "payer",
              "title": "Payer''s Name",
              "pattern": "{payer}",
              "is_available_for_subject": true
            },
            {
              "field": "tickets_names_list",
              "title": "Bought Ticket Types List",
              "pattern": "{tickets_names_list}",
              "custom_action": true
            },
            {
              "field": "tickets_links",
              "title": "Tickets Links",
              "pattern": "{tickets_links}",
              "custom_action": true
            },
            {
              "field": "total_amount",
              "title": "Total Purchase Amount",
              "pattern": "{total_amount}"
            },
            {
              "field": "service_fee",
              "title": "Purchase Service Fee",
              "pattern": "{service_fee}"
            },
            {
              "field": "credit_card_merchant_fee",
              "title": "Credit Card Merchant Fee",
              "pattern": "{credit_card_merchant_fee}"
            },
            {
              "field": "social_icons",
              "title": "Social Icons",
              "pattern": "{social_icons}",
              "custom_action": true
            },
            {
              "field": "facebook_icon",
              "title": "Facebook Icon",
              "pattern": "{facebook_icon}",
              "custom_action": true
            },
            {
              "field": "twitter_icon",
              "title": "Twitter Icon",
              "pattern": "{twitter_icon}",
              "custom_action": true
            },
            {
              "field": "instagram_icon",
              "title": "Instagram Icon",
              "pattern": "{instagram_icon}",
              "custom_action": true
            },
            {
              "field": "snapchat_icon",
              "title": "Snapchat Icon",
              "pattern": "{snapchat_icon}",
              "custom_action": true
            }
          ]', '{
            "roles": [
              "any"
            ]
          }');
        -- ---------------------------------------------------------------------------------------------------
        
        WITH main_template AS (
            INSERT INTO public.email_template (email_html, email_subject, email_text,
                                               event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
                                               img_name, email_template_type, is_valid, email_template_group, published,
                                               deleted) VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        
        <head>
            <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
            <meta name="viewport" content="width=device-width">
            <!--[if !mso]><!-->
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <!--<![endif]-->
            <title></title>
            <!--[if !mso]><!-->
            <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">
            <!--<![endif]-->
            <style type="text/css">
                body {
                    margin: 0;
                    padding: 0;
                }
        
                table,
                td,
                tr {
                    vertical-align: top;
                    border-collapse: collapse;
                }
        
                * {
                    line-height: inherit;
                }
        
                a[x-apple-data-detectors=true] {
                    color: inherit !important;
                    text-decoration: none !important;
                }
            </style>
            <style type="text/css" id="media-query">
                @media (max-width: 660px) {
        
                    .block-grid,
                    .col {
                        min-width: 320px !important;
                        max-width: 100% !important;
                        display: block !important;
                    }
        
                    .block-grid {
                        width: 100% !important;
                    }
        
                    .col {
                        width: 100% !important;
                    }
        
                    .col_cont {
                        margin: 0 auto;
                    }
        
                    img.fullwidth,
                    img.fullwidthOnMobile {
                        width: 100% !important;
                    }
        
                    .no-stack .col {
                        min-width: 0 !important;
                        display: table-cell !important;
                    }
        
                    .no-stack.two-up .col {
                        width: 50% !important;
                    }
        
                    .no-stack .col.num2 {
                        width: 16.6% !important;
                    }
        
                    .no-stack .col.num3 {
                        width: 25% !important;
                    }
        
                    .no-stack .col.num4 {
                        width: 33% !important;
                    }
        
                    .no-stack .col.num5 {
                        width: 41.6% !important;
                    }
        
                    .no-stack .col.num6 {
                        width: 50% !important;
                    }
        
                    .no-stack .col.num7 {
                        width: 58.3% !important;
                    }
        
                    .no-stack .col.num8 {
                        width: 66.6% !important;
                    }
        
                    .no-stack .col.num9 {
                        width: 75% !important;
                    }
        
                    .no-stack .col.num10 {
                        width: 83.3% !important;
                    }
        
                    .video-block {
                        max-width: none !important;
                    }
        
                    .mobile_hide {
                        min-height: 0px;
                        max-height: 0px;
                        max-width: 0px;
                        display: none;
                        overflow: hidden;
                        font-size: 0px;
                    }
        
                    .desktop_hide {
                        display: block !important;
                        max-height: none !important;
                    }
        
                    .img-container.big img {
                        width: auto !important;
                    }
                }
            </style>
        </head>
        
        <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #F7F7F7;">
            <!--[if IE]><div class="ie-browser"><![endif]-->
            <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #F7F7F7; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#F7F7F7" valign="top">
                <tbody>
                    <tr style="vertical-align: top;" valign="top">
                        <td style="word-break: break-word; vertical-align: top;" valign="top">
                            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#F7F7F7"><![endif]-->
                            <div style="background-color:transparent;">
                                <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
                                    <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
                                        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:15px; padding-bottom:15px;"><![endif]-->
                                        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
                                            <div class="col_cont" style="width:100% !important;">
                                                <!--[if (!mso)&(!IE)]><!-->
                                                <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:15px; padding-bottom:15px; padding-right: 0px; padding-left: 0px;">
                                                    <!--<![endif]-->
                                                    <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
                                                    <div style="color:#555555;font-family:Arial, ''Helvetica Neue'', Helvetica, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                                                        <div class="txtTinyMce-wrapper" style="font-size: 12px; line-height: 1.2; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif; color: #555555; mso-line-height-alt: 14px;">
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><strong><span style="font-size: 18px;">{event_name}&nbsp;</span></strong></p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">{tickets_names_list}</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Purchaser:&nbsp;{payer}&nbsp;</span></p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Total Price:&nbsp;<strong>{total_amount}</strong></span></p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;"><strong><span style="font-size: 18px;">List of all ticket holders on this purchase:</span></strong> <br><span style="font-size: 14px;">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p>
                                                            <p style="margin: 0; font-size: 16px; text-align: center; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: center; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">{tickets_links}&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: center; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: left; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: left; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: left; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">{tickets_receipt_descr}&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; text-align: left; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">{social_icons}&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                                                            <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;">© SportWrench Inc. 2021</p>
                                                        </div>
                                                    </div>
                                                    <!--[if mso]></td></tr></table><![endif]-->
                                                    <!--[if (!mso)&(!IE)]><!-->
                                                </div>
                                                <!--<![endif]-->
                                            </div>
                                        </div>
                                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                                    </div>
                                </div>
                            </div>
                            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
            <!--[if (IE)]></div><![endif]-->
        </body>
        
        </html>', 'Receipt for Ticket Purchase: {event_name}', '{event_name}
        Thank you for your purchase! This email contains confirmation of your purchase details.
        A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device along with a Government photo ID to gain entry.
        {tickets_names_list}
        Purchaser: {payer}
        Total Price: {total_amount}
        List of all ticket holders on this purchase: (Click on a name to access each ticket if you do not receive the individual QR code ticket email)
        {tickets_links}
        Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.
        {tickets_receipt_descr}
        {social_icons}
        © SportWrench Inc. 2021', null, null, null, 'Assigned Tickets Receipt', null, '{
              "page": {
                "body": {
                  "type": "mailup-bee-page-proprerties",
                  "content": {
                    "style": {
                      "color": "#000000",
                      "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                    },
                    "computedStyle": {
                      "linkColor": "#0000FF",
                      "messageWidth": "640px",
                      "messageBackgroundColor": "#FFFFFF"
                    }
                  },
                  "webFonts": [
                    {
                      "url": "https://fonts.googleapis.com/css?family=Roboto",
                      "name": "Roboto",
                      "family": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif",
                      "fontName": "Roboto",
                      "fontFamily": "''Roboto'', Tahoma, Verdana, Segoe, sans-serif"
                    }
                  ],
                  "container": {
                    "style": {
                      "background-color": "#F7F7F7"
                    }
                  }
                },
                "rows": [
                  {
                    "type": "two-columns-4-8-empty",
                    "uuid": "4e5841a6-8a0a-4522-8db5-5c8efe8cc05e",
                    "columns": [
                      {
                        "uuid": "d89d3f1a-f479-4bea-9fe8-217166bed265",
                        "style": {
                          "border-top": "0px solid transparent",
                          "border-left": "0px solid transparent",
                          "padding-top": "15px",
                          "border-right": "0px solid transparent",
                          "padding-left": "0px",
                          "border-bottom": "0px solid transparent",
                          "padding-right": "0px",
                          "padding-bottom": "15px",
                          "background-color": "transparent"
                        },
                        "modules": [
                          {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "26cdba25-2ed1-404d-b30f-697c0794ce22",
                            "descriptor": {
                              "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Arial, ''Helvetica Neue'', Helvetica, sans-serif;\"><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Name\">{event_name}</code>&nbsp;</span></strong></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Thank you for your purchase! This email contains confirmation of your purchase details.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">A separate email with an individual QR code for each ticket holder will follow. They will need to present the QR code on a mobile device <u><strong>along with a Government photo ID</strong></u> to gain entry.</span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">{tickets_names_list}</code></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Purchaser:&nbsp;</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Payer''s Name\">{payer}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">&nbsp;</code></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Bought Ticket Types List\">Total Price:&nbsp;</code><strong><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Total Purchase Amount\">{total_amount}</code></strong></span></p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><strong><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">List of all ticket holders on this purchase:</span></strong> <br><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></span></p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: center; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. If necessary, you can print the QR code and present a paper copy.</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event Disclaimer\">{tickets_receipt_descr}</code>&nbsp;</p><p style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\" data-mce-style=\"font-size: 16px; text-align: left; line-height: 19px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code>&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 16px; line-height: 19px; word-break: break-word; text-align: center;\">© SportWrench Inc. 2021</p></div>",
                                "style": {
                                  "color": "#555555",
                                  "font-family": "Arial, ''Helvetica Neue'', Helvetica, sans-serif",
                                  "line-height": "120%"
                                },
                                "computedStyle": {
                                  "linkColor": "#0000FF"
                                }
                              },
                              "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                              },
                              "computedStyle": {
                                "hideContentOnMobile": false
                              }
                            }
                          }
                        ],
                        "grid-columns": 12
                      }
                    ],
                    "content": {
                      "style": {
                        "color": "#333",
                        "width": "640px",
                        "background-color": "#FFFFFF",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                      }
                    },
                    "container": {
                      "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                      }
                    }
                  }
                ],
                "title": "BF-basic-newsletter",
                "template": {
                  "name": "template-base",
                  "type": "basic",
                  "version": "0.0.1"
                },
                "description": "BF-basic-newsletter"
              },
              "comments": {}
            }', null, 'tickets.assigned.receipt', true, 'tickets.payments', true, null)
                RETURNING email_template_id
        ),
             insert_type AS (
        -- Create template type tickets.assigned.receipt -----------------------------------------------------
                 INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                         default_email_template_id)
                     VALUES ('tickets.assigned.receipt', 'tickets.payments', 'Receipt for Assigned Tickets',
                             '<em>Receipt for Assigned Tickets</em>', 'Receipt for Assigned Tickets', true,
                             (SELECT email_template_id FROM main_template))
        -- ---------------------------------------------------------------------------------------------------
             )
             
        -- Create event trigger action for tickets.assigned.receipt type -------------------------------------
        INSERT
        INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('tickets.assigned.receipt', 'tickets.payments', (SELECT email_template_id FROM main_template), 0);
        -- ---------------------------------------------------------------------------------------------------
            
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM email_template WHERE email_template_group = 'tickets.payments';
        DELETE FROM event_email_trigger WHERE email_template_group = 'tickets.payments';
        DELETE FROM email_template_type WHERE email_template_group = 'tickets.payments';
        DELETE FROM  email_template_group WHERE "group" = 'tickets.payments';
    `)
};
