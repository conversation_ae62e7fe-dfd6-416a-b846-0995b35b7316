
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TYPE "public"."official_payment_option" ADD VALUE IF NOT EXISTS 'no_payment_required';
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."event_official"
            ALTER COLUMN "staff_payment_option" TYPE TEXT USING "staff_payment_option"::TEXT,
            ALTER COLUMN "payment_option" TYPE TEXT USING "payment_option"::TEXT;
        
        ALTER TABLE "public"."official_payout"
            ALTER COLUMN "payment_method" TYPE TEXT USING "payment_method"::TEXT;
        
        DROP TYPE IF EXISTS "public"."official_payment_option";
        
        CREATE TYPE "public"."official_payment_option" AS ENUM ('direct_deposit', 'on_site', 'mailed', 'arbiterpay');
        
        ALTER TABLE "public"."event_official"
            ALTER COLUMN "staff_payment_option" TYPE official_payment_option USING "staff_payment_option"::official_payment_option,
            ALTER COLUMN "payment_option" TYPE official_payment_option USING "payment_option"::official_payment_option;
        
        ALTER TABLE "public"."official_payout"
            ALTER COLUMN "payment_method" TYPE official_payment_option USING "payment_method"::official_payment_option;
    `);
};
