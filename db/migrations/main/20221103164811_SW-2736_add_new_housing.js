exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO public.housing_company(housing_company_id, name) VALUES (6, 'KC Sports Housing');
        UPDATE public.user SET housing_company_id = 6 WHERE email = '<EMAIL>';
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE public.user SET housing_company_id = NULL WHERE email = '<EMAIL>';
        DELETE FROM public.housing_company WHERE housing_company_id = 6;
    `)
};
