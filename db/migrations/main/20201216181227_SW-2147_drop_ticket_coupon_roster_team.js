
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Create ticket_coupon receivers ------------------------------------------------------------------------------
        INSERT INTO ticket_coupon_receiver (ticket_coupon_id, roster_team_id)
        SELECT ticket_coupon_id, roster_team_id
        FROM ticket_coupon;
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- Remove coupon - roster team connection ----------------------------------------------------------------------
        ALTER TABLE "public"."ticket_coupon"
            DROP COLUMN IF EXISTS "roster_team_id";
        -- -------------------------------------------------------------------------------------------------------------
    `)
}

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Add coupon - roster team connection -------------------------------------------------------------------------
        ALTER TABLE "public"."ticket_coupon" ADD COLUMN IF NOT EXISTS "roster_team_id" INT;
        -- -------------------------------------------------------------------------------------------------------------


        -- Update ticket_coupon.roster_team_id value -------------------------------------------------------------------
        UPDATE ticket_coupon AS tc
        SET roster_team_id = (SELECT tcr.roster_team_id
                              FROM ticket_coupon_receiver tcr
                              WHERE tcr.ticket_coupon_id = tc.ticket_coupon_id);
        -- -------------------------------------------------------------------------------------------------------------
    `)
};
