
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Create new field roster_club.is_virtual --------------------------------------------------------------------
        ALTER TABLE "public"."roster_club" ADD COLUMN "is_virtual" BOOLEAN DEFAULT FALSE NOT NULL;
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Set is_virtual = TRUE for all virtual clubs ----------------------------------------------------------------
        UPDATE roster_club rc
        SET is_virtual = TRUE
        WHERE rc.roster_club_id IN (
            SELECT club.roster_club_id
            FROM event e
                     JOIN roster_club club ON club.event_id = e.event_id
            WHERE (e.teams_settings ->> 'manual_teams_addition')::BOOLEAN IS TRUE
              AND club.roster_club_id IS NOT NULL
        );
        -- ------------------------------------------------------------------------------------------------------------
        
        -- Add new unique roster_club_event_id_is_virtual_is_true_unique ----------------------------------------------
        CREATE UNIQUE INDEX roster_club_event_id_is_virtual_is_true_unique
            ON "public"."roster_club" (event_id) WHERE is_virtual IS TRUE;
        -- ------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP INDEX roster_club_event_id_is_virtual_is_true_unique;
        ALTER TABLE "public"."roster_club" DROP COLUMN "is_virtual";
    `)
};
