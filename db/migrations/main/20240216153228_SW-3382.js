
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TYPE "ticket_receiver_type" --------------------------------------------------------------------------
        CREATE TYPE "public"."ticket_receiver_type" AS ENUM('exhibitors');
        ----------------------------------------------------------------------------------------------------------------
    
        -- CREATE FIELD "receiver_type" --------------------------------------------------------------------------------
        ALTER TABLE "public"."purchase" ADD COLUMN "receiver_type" ticket_receiver_type DEFAULT NULL;
        ----------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."purchase" DROP COLUMN IF EXISTS "receiver_type";
        DROP TYPE IF EXISTS "ticket_receiver_type";
    `);
};
