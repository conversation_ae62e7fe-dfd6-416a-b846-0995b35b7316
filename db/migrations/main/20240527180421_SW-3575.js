
exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO public.member_type_rules 
            (field_name, member_type, rule_data, is_active) 
        VALUES 
            ('membership_definition_id', 'athlete', '11ee20ef-8dff-1788-99e1-e656c8d6c998', true),
            ('membership_definition_id', 'athlete', '11ee20ef-18cc-6d76-abf3-8e22f6e13dea', true);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."member_type_rules" WHERE rule_data = '11ee20ef-8dff-1788-99e1-e656c8d6c998';
    `)
};
