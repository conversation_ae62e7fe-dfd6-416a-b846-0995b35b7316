
exports.up = function(knex) {
    return knex.schema.raw(`
        DROP MATERIALIZED VIEW ticket_purchase_number_in_season;
        
        CREATE MATERIALIZED VIEW ticket_purchase_number_in_season AS
        SELECT row_number() OVER (ORDER BY (COALESCE(ticket.created, payment.created)))::integer AS season_counter,
               COALESCE(ticket.purchase_id, payment.purchase_id)                                 AS ticket_id,
               payment.purchase_id                                                               AS payment_id,
               pt.quantity,
               payment.event_id
        FROM purchase payment
                 LEFT JOIN purchase ticket ON payment.purchase_id = ticket.linked_purchase_id AND ticket.is_ticket IS TRUE AND
                                              ticket.payment_for::text = 'tickets'::text AND ticket.is_payment IS FALSE
                 JOIN purchase_ticket pt ON COALESCE(ticket.purchase_id, payment.purchase_id) = pt.purchase_id
        WHERE payment.is_payment IS TRUE
          AND payment.type = 'card'::purchase_type
          AND payment.payment_for::text = 'tickets'::text
          AND payment.created >=
              ((date_part('year'::text, now() - '1 year'::interval) || '-09-01'::text)::timestamp without time zone)
          AND payment.created <= ((date_part('year'::text, now()) || '-08-31'::text)::timestamp without time zone)
        GROUP BY ticket.purchase_id, payment.purchase_id, pt.quantity
        UNION
        SELECT row_number() OVER (ORDER BY (COALESCE(ticket.created, payment.created)))::integer AS season_counter,
               COALESCE(ticket.purchase_id, payment.purchase_id)                                 AS ticket_id,
               payment.purchase_id                                                               AS payment_id,
               pt.quantity,
               payment.event_id
        FROM purchase payment
                 LEFT JOIN purchase ticket ON payment.purchase_id = ticket.linked_purchase_id AND ticket.is_ticket IS TRUE AND
                                              ticket.payment_for::text = 'tickets'::text AND ticket.is_payment IS FALSE
                 JOIN purchase_ticket pt ON COALESCE(ticket.purchase_id, payment.purchase_id) = pt.purchase_id
        WHERE payment.is_payment IS TRUE
          AND payment.type = 'card'::purchase_type
          AND payment.payment_for::text = 'tickets'::text
          AND payment.created >= ((date_part('year'::text, now()) || '-09-01'::text)::timestamp without time zone)
          AND payment.created <= ((date_part('year'::text, now() + '1 year'::interval) || '-08-31'::text)::timestamp without time zone)
        GROUP BY ticket.purchase_id, payment.purchase_id, pt.quantity;
        
        CREATE INDEX IF NOT EXISTS ticket_purchase_number_in_season_purchase_id
        ON ticket_purchase_number_in_season (payment_id);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP MATERIALIZED VIEW ticket_purchase_number_in_season;
        
        CREATE MATERIALIZED VIEW ticket_purchase_number_in_season AS
        SELECT row_number() OVER (ORDER BY (COALESCE(ticket.created, payment.created)))::integer AS season_counter,
               COALESCE(ticket.purchase_id, payment.purchase_id)                                 AS ticket_id,
               payment.purchase_id                                                               AS payment_id,
               pt.quantity,
               payment.event_id
        FROM purchase payment
                 LEFT JOIN purchase ticket ON payment.purchase_id = ticket.linked_purchase_id AND ticket.is_ticket IS TRUE AND
                                              ticket.payment_for::text = 'tickets'::text AND ticket.is_payment IS FALSE
                 JOIN purchase_ticket pt ON COALESCE(ticket.purchase_id, payment.purchase_id) = pt.purchase_id
        WHERE payment.is_payment IS TRUE
          AND payment.type = 'card'::purchase_type
          AND payment.payment_for::text = 'tickets'::text
          AND payment.created >=
              ((date_part('year'::text, now() - '1 year'::interval) || '-09-01'::text)::timestamp without time zone)
          AND payment.created <= ((date_part('year'::text, now()) || '-08-31'::text)::timestamp without time zone)
        GROUP BY ticket.purchase_id, payment.purchase_id, pt.quantity;
        
        CREATE INDEX IF NOT EXISTS ticket_purchase_number_in_season_purchase_id
        ON ticket_purchase_number_in_season (payment_id);
    `)
};
