
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS materialized_view_schedule
        (
            materialized_view_schedule_id INT GENERATED ALWAYS AS IDENTITY,
            created                       TIMESTAMPTZ DEFAULT NOW(),
            modified                      TIMESTAMPTZ,
            name                          TEXT    NOT NULL,
            schedule                      TEXT    NOT NULL,
            is_active                     BOOLEAN NOT NULL
        );
        
        COMMENT ON COLUMN materialized_view_schedule.schedule IS 'Schedule in cron format, example - */1 * * * *';
        COMMENT ON COLUMN materialized_view_schedule.name IS 'Materialized view name';
        COMMENT ON COLUMN materialized_view_schedule.is_active IS 'Defines if view requires scheduled refreshing';
        
        CREATE TRIGGER update_materialized_view_schedule_modified
            BEFORE UPDATE
            ON materialized_view_schedule
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
       
        INSERT INTO materialized_view_schedule (name, schedule, is_active)
        VALUES ('ticket_purchase_number_in_season', '* */10 * * *', TRUE);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS materialized_view_schedule;
    `)
};
