
exports.up = function(knex) {
    const email_text = `A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of previously uncollected {event_short_name} SW Ticket Fees. The balance due is most likely a result of cash sales for event tickets sold on site. Payment details:SW Ticket Fee Total: \${payment_net_amount}Merchant Processing Fee: \${payment_merchant_fee}Total Charge: \${payment_total_amount} Accounting details:SW Ticket Fee Charged: \${sw_fee}# of Uncollected Ticket Fees: {uncollected_tickets_count}                                                                  Copyright © SportWrench Inc. {current_year}. All rights reserved`;
    const email_html = `<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"> <html xmlns="http://www.w3.org/1999/xhtml"xmlns:v="urn:schemas-microsoft-com:vml"xmlns:o="urn:schemas-microsoft-com:office:office"> <head> <!--[if gte mso 9]>
        <xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
        <![endif]--> <meta http-equiv="Content-Type"content="text/html; charset=UTF-8"> <meta name="viewport"content="width=device-width,initial-scale=1"> <meta name="x-apple-disable-message-reformatting"> <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible"content="IE=edge"><!--<![endif]--> <title></title> <style type="text/css">@media only screen and (min-width:520px){.u-row{width:500px!important}.u-row .u-col{vertical-align:top}.u-row .u-col-100{width:500px!important}}@media (max-width:520px){.u-row-container{max-width:100%!important;padding-left:0!important;padding-right:0!important}.u-row .u-col{min-width:320px!important;max-width:100%!important;display:block!important}.u-row{width:100%!important}.u-col{width:100%!important}.u-col>div{margin:0 auto}}body{margin:0;padding:0}table,td,tr{vertical-align:top;border-collapse:collapse}p{margin:0}.ie-container table,.mso-container table{table-layout:fixed}*{line-height:inherit}a[x-apple-data-detectors=true]{color:inherit!important;text-decoration:none!important}table,td{color:#000}</style> </head> <body class="clean-body u_body"style="margin:0;padding:0;-webkit-text-size-adjust:100%;background-color:#f7f7f7;color:#000"> <!--[if IE]><div class="ie-container"><![endif]--> <!--[if mso]><div class="mso-container"><![endif]--> <table style="border-collapse:collapse;table-layout:fixed;border-spacing:0;mso-table-lspace:0;mso-table-rspace:0;vertical-align:top;min-width:320px;Margin:0 auto;background-color:#f7f7f7;width:100%"cellpadding="0"cellspacing="0"> <tbody> <tr style="vertical-align:top"> <td style="word-break:break-word;border-collapse:collapse!important;vertical-align:top"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #F7F7F7;"><![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0;font-family:arial,helvetica,sans-serif"align="left"> <div> <div style="text-align:left"> <p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of previously uncollected <b>{event_short_name}</b> SW Ticket Fees. The balance due is most likely a result of cash sales for event tickets sold on site.</p> <p><span style="margin-bottom:6px;display:block"><b>Payment details:</b></span><span style="margin-bottom:6px;display:block">SW Ticket Fee Total: \${payment_net_amount}</span><span style="margin-bottom:6px;display:block">Merchant Processing Fee: \${payment_merchant_fee}</span><span style="margin-bottom:6px;display:block">Total Charge: \${payment_total_amount}</span></p> <p><span style="margin-bottom:6px;display:block"><b>Accounting details:</b></span><span style="margin-bottom:6px;display:block">SW Ticket Fee Charged: \${sw_fee}</span><span style="margin-bottom:6px;display:block"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span></p> </div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:5px 5px 5px 5px;font-family:arial,helvetica,sans-serif"align="left"> <table height="0px"align="center"border="0"cellpadding="0"cellspacing="0"width="100%"style="border-collapse:collapse;table-layout:fixed;border-spacing:0;mso-table-lspace:0;mso-table-rspace:0;vertical-align:top;border-top:1px solid #bbb;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"> <tbody> <tr style="vertical-align:top"> <td style="word-break:break-word;border-collapse:collapse!important;vertical-align:top;font-size:0;line-height:0;mso-line-height-rule:exactly;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"> <span>&#160;</span> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0 10px 0 10px;font-family:arial,helvetica,sans-serif"align="left"> <div style="font-family:inherit;font-size:14px;color:#555;line-height:120%;text-align:left;word-wrap:break-word"> <div class="txtTinyMce-wrapper"style="font-size:12px;line-height:14px;font-family:inherit"data-mce-style="font-size: 12px; line-height: 14px; font-family: inherit;"><p style="font-size:14px;line-height:16px;text-align:center;word-break:break-word"data-mce-style="font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;"><span style="font-size:12px;line-height:14px"data-mce-style="font-size: 12px; line-height: 14px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div> </div> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if gte mso 9]>
              <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;min-width: 320px;max-width: 500px;">
                <tr>
                  <td background="none" valign="top" width="100%">
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width: 500px;">
                <v:fill type="frame" src="none" /><v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
              <![endif]--> <div class="u-row-container"style="padding:0;background-color:transparent"> <div class="u-row"style="margin:0 auto;min-width:320px;max-width:500px;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;background-color:#fff"> <div style="border-collapse:collapse;display:table;width:100%;height:100%;background-image:url(none);background-repeat:no-repeat;background-position:center top;background-color:transparent"> <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px;"><tr style="background-image: url(none);background-repeat: no-repeat;background-position: center top;background-color: #FFFFFF;"><![endif]--> <!--[if (mso)|(IE)]><td align="center" width="500" style="width: 500px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;" valign="top"><![endif]--> <div class="u-col u-col-100"style="max-width:320px;min-width:500px;display:table-cell;vertical-align:top"> <div style="height:100%;width:100%!important"> <!--[if (!mso)&(!IE)]><!--><div style="box-sizing:border-box;height:100%;padding:0;border-top:0 solid transparent;border-left:0 solid transparent;border-right:0 solid transparent;border-bottom:0 solid transparent"><!--<![endif]--> <table style="font-family:arial,helvetica,sans-serif"role="presentation"cellpadding="0"cellspacing="0"width="100%"border="0"> <tbody> <tr> <td style="overflow-wrap:break-word;word-break:break-word;padding:0;font-family:arial,helvetica,sans-serif"align="left"> <table width="100%"cellpadding="0"cellspacing="0"border="0"> <tr> <td style="padding-right:0;padding-left:0"align="center"> <img align="center"border="0"src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png"alt=""title=""style="outline:0;text-decoration:none;-ms-interpolation-mode:bicubic;clear:both;display:inline-block!important;border:none;height:auto;float:none;width:100%;max-width:72px"width="72"> </td> </tr> </table> </td> </tr> </tbody> </table> <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]--> </div> </div> <!--[if (mso)|(IE)]></td><![endif]--> <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]--> </div> </div> </div> <!--[if gte mso 9]>
              </v:textbox></v:rect>
            </td>
            </tr>
            </table>
            <![endif]--> <!--[if (mso)|(IE)]></td></tr></table><![endif]--> </td> </tr> </tbody> </table> <!--[if mso]></div><![endif]--> <!--[if IE]></div><![endif]--> </body> </html>`;
    const unlayer_json = JSON.stringify({"body": {"id": "4nxPBNE1pF", "rows": [{"id": "GtU13gqlUw", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_1", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "P3lskdoJIA", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "0ll3Vh3y-1", "type": "html", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "html": "<div style=\"text-align: left\">\n  <p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of previously uncollected <b>{event_short_name}</b> SW Ticket Fees. The balance due is most likely a result of cash sales for event tickets sold on site.</p>\n  <p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Total: ${payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Processing Fee: ${payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total Charge: ${payment_total_amount}</span></p>\n  <p><span style=\"margin-bottom: 6px; display: block\"><b>Accounting details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Charged: ${sw_fee}</span><span style=\"margin-bottom: 6px; display: block\"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span></p>\n</div>", "_meta": {"htmlID": "u_content_html_1", "htmlClassNames": "u_content_html"}, "anchor": "", "hideable": true, "deletable": true, "draggable": true, "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "0px 0px 0px 0px", "displayCondition": null}}]}]}, {"id": "geuS4ogZjg", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_2", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "sadkVwDCrb", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "OLz8eDa44w", "type": "divider", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "_meta": {"htmlID": "u_content_divider_1", "htmlClassNames": "u_content_divider"}, "width": "100%", "anchor": "", "border": {"borderTopColor": "#BBBBBB", "borderTopStyle": "solid", "borderTopWidth": "1px"}, "hideable": true, "deletable": true, "draggable": true, "textAlign": "center", "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "5px 5px 5px 5px", "displayCondition": null}}]}]}, {"id": "B6MpVw33m_", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_3", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "kdlMM0e6iK", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "SJbUKjAMRS", "type": "text", "values": {"src": {"width": "auto", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "size": {"autoWidth": true}, "text": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: inherit;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: inherit;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div>", "_meta": {"htmlID": "u_content_text_1", "htmlClassNames": "u_content_text"}, "color": "#555555", "anchor": "", "border": {}, "columns": false, "fontSize": "14px", "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"inherit": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textAlign": "left", "textColor": "#555555", "fontFamily": {"label": "Fonts", "value": "inherit"}, "lineHeight": "120%", "selectable": true, "headingType": "", "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "containerPadding": "0px 10px 0px 10px", "displayCondition": null}, "hasDeprecatedFontControls": true}]}]}, {"id": "LveuvBnaUA", "cells": [12], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_row_4", "htmlClassNames": "u_row"}, "anchor": "", "border": {}, "columns": false, "padding": "0px", "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"url": "none", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "columnsBackgroundColor": "#FFFFFF"}, "columns": [{"id": "xfxSSC__ln", "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_column_1", "htmlClassNames": "u_column"}, "border": {"borderTop": "0px solid transparent", "borderLeft": "0px solid transparent", "borderRight": "0px solid transparent", "borderBottom": "0px solid transparent"}, "columns": true, "hideable": false, "deletable": true, "draggable": true, "fontFamily": {}, "selectable": true, "headingType": "", "hideDesktop": false, "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "preheaderText": "", "backgroundColor": "", "backgroundImage": {"size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null}, "contents": [{"id": "bjcUg1Wnib", "type": "image", "values": {"src": {"url": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png", "width": "72px", "height": "auto"}, "href": {"name": "web", "values": {"target": "_blank"}}, "_meta": {"htmlID": "u_content_image_2", "htmlClassNames": "u_content_image"}, "action": {"name": "web", "values": {"href": "", "target": "_blank"}}, "anchor": "", "altText": "", "hideable": true, "deletable": true, "draggable": true, "textAlign": "center", "selectable": true, "hideDesktop": false, "buttonColors": {"hoverColor": "#FFFFFF", "backgroundColor": "#3AAEE0", "hoverBackgroundColor": "#3AAEE0"}, "duplicatable": true, "containerPadding": "0px 0px 0px 0px", "displayCondition": null}}]}]}], "values": {"size": {"autoWidth": true}, "text": "", "_meta": {"htmlID": "u_body", "htmlClassNames": "u_body"}, "border": {}, "columns": false, "hideable": false, "deletable": true, "draggable": true, "linkStyle": {"body": true, "linkColor": "#0000ee", "linkUnderline": true, "linkHoverColor": "#0000ee", "linkHoverUnderline": true}, "textColor": "#000000", "fontFamily": {"label": "Arial", "value": "arial,helvetica,sans-serif"}, "popupWidth": "600px", "selectable": true, "headingType": "", "hideDesktop": false, "popupHeight": "auto", "borderRadius": "10px", "contentAlign": "center", "contentWidth": "500px", "duplicatable": true, "popupPosition": "center", "preheaderText": "", "backgroundColor": "#F7F7F7", "backgroundImage": {"url": "", "size": "custom", "repeat": "no-repeat", "position": "top-center", "fullWidth": false, "customPosition": ["50%", "0%"]}, "calculatedWidth": null, "calculatedHeight": null, "displayCondition": null, "contentVerticalAlign": "center", "popupBackgroundColor": "#FFFFFF", "popupBackgroundImage": {"url": "", "size": "cover", "repeat": "no-repeat", "position": "center", "fullWidth": true}, "popupCloseButton_action": {"name": "close_popup"}, "popupCloseButton_margin": "0px", "popupCloseButton_position": "top-right", "popupCloseButton_iconColor": "#000000", "popupOverlay_backgroundColor": "rgba(0, 0, 0, 0.1)", "popupCloseButton_borderRadius": "0px", "popupCloseButton_backgroundColor": "#DDDDDD"}, "footers": [], "headers": []}, "counters": {"u_row": 4, "u_column": 4, "u_content_html": 1, "u_content_menu": 0, "u_content_text": 1, "u_content_image": 2, "u_content_button": 0, "u_content_divider": 1, "u_content_heading": 0}, "schemaVersion": 16});

    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text = '${email_text}',
            email_html = '${email_html}',
            unlayer_json = '${unlayer_json}'
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.success');
    `);
};

exports.down = function(knex) {
    const email_text =
        `A successful charge has been made to the {payment_method} ending in {card_last_4}
        for the payment of previously uncollected {event_short_name} SW Ticket Fees.
        The balance due is most likely a result of cash sales for event tickets sold on site.
        Payment details:
        SW Ticket Fee Total: \${payment_net_amount}
        Merchant Processing Fee: \${payment_merchant_fee}
        Total Charge: \${payment_total_amount}
        Accounting details:
        SW Ticket Fee Charged: \${sw_fee}
        # of Uncollected Ticket Fees: {uncollected_tickets_count}
        Copyright © SportWrench Inc. {current_year}. All rights reserved`;
    const email_html = `<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
        <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
        class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">    <div style="text-align: left">
        <p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of previously uncollected <b>{event_short_name}</b> SW Ticket Fees. The balance due is most likely a result of cash sales for event tickets sold on site.</p><p><span style="margin-bottom: 6px; display: block"><b>Payment details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Total: \${payment_net_amount}</span><span style="margin-bottom: 6px; display: block">Merchant Processing Fee: \${payment_merchant_fee}</span><span style="margin-bottom: 6px; display: block">Total Charge: \${payment_total_amount}</span></p><p>
        <span style="margin-bottom: 6px; display: block"><b>Accounting details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Charged: \${sw_fee}</span><span style="margin-bottom: 6px; display: block"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span>
        </p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
        <span style="font-size:12px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
        title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>`;

    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text = '${email_text}',
            email_html = '${email_html}',
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.success');
    `);
};
