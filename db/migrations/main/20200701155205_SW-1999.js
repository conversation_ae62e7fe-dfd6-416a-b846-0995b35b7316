
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."event_email" ADD COLUMN "ticket_coupons" JSONB DEFAULT '[]'::JSONB;
        COMMENT ON COLUMN "public"."event_email"."ticket_coupons" IS 'List of ticket_coupon_id''s sent in email';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."event_email" DROP COLUMN IF EXISTS "ticket_coupons";
    `)
};
