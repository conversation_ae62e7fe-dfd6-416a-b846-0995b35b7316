
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS ticket_buy_entry_code
        (
            ticket_buy_entry_code_id INT GENERATED ALWAYS AS IDENTITY,
            created                  TIMESTAMP DEFAULT NOW(),
            modified                 TIMESTAMP,
            code                     TEXT NOT NULL,
            event_id                 INT NOT NULL
        );
        
        CREATE UNIQUE INDEX "ticket_buy_entry_code_unique_event_id_code"
        ON ticket_buy_entry_code (LOWER("code"), "event_id");
        
        COMMENT ON COLUMN "ticket_buy_entry_code"."code" IS 'Custom ticket buy entry code.';
        COMMENT ON COLUMN "ticket_buy_entry_code"."event_id" IS 'Event ID related to code';
        
        CREATE TRIGGER update_ticket_buy_entry_code_modified
        BEFORE UPDATE ON "public"."ticket_buy_entry_code"
        FOR EACH ROW EXECUTE PROCEDURE update_modified_column();
        
        ALTER TABLE event_ticket_buy_entry_code_settings 
        ADD COLUMN IF NOT EXISTS custom_code_source_enabled BOOLEAN DEFAULT FALSE NOT NULL;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS ticket_buy_entry_code;
        
        ALTER TABLE event_ticket_buy_entry_code_settings DROP COLUMN IF EXISTS custom_code_source_enabled;
    `);
};
