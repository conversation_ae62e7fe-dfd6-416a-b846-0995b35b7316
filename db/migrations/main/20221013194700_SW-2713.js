exports.up = function (knex) {
    return knex.schema.raw(String.raw`
        -- Delete old default success payment template
        DELETE FROM email_template WHERE email_template_type = 'teams.any.paid' AND event_id IS NULL;
        DELETE FROM email_template_type WHERE type = 'teams.any.paid';
        DELETE FROM event_email_trigger WHERE email_template_type = 'teams.any.paid' and event_id = 0;

        -- Create tickets.payments template ------------------------------------------------------------
        WITH main_template AS (
            INSERT INTO public.email_template
            (email_html, email_subject, email_text,
            event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
            img_name, email_template_type, is_valid, email_template_group, published,
            deleted)
            VALUES
            ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--><link href="https://fonts.googleapis.com/css$2family=Lato" 
            rel="stylesheet" type="text/css"><!--<![endif]--><style>
            *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:640px){.row-content{width:100%!important}.mobile_hide{display:none}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
            </style></head><body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
            class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:20px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><img src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg" style="display:block;height:auto;border:0;width:116px;max-width:100%" width="116" alt="Image" title="Image"></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
            </table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:0;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:18px;">{event_name}</span></p><p 
            style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px"><span style="font-size:14px;">Dear, {cd_first} {cd_last}</span></p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:14px;text-align:left;mso-line-height-alt:16.8px">
            <span style="font-size:14px;"> Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}&nbsp; by SportWrench Inc.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><img src="https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/editor_images/8ba25a10-9dfd-4e77-8de6-148953d21d28.png" style="display:block;height:auto;border:0;width:100px;max-width:100%" width="100" alt="Image" title="Image"></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table 
            class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;border-radius:0;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:22px;color:#3ab46b;"><strong>SUCCESSFUL PAYMENT</strong></span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-6" align="center" width="100%" border="0" cellpadding="0" 
            cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="33.333333333333336%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Amount paid:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td 
            class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{payment_amount}</span></p></div></div></td></tr></table></td><td class="column column-2" 
            width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif">
            <div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;"><span style="font-size:16px;">Date</span> paid:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">{payment_date_paid}</span></p>
            </div></div></td></tr></table></td><td class="column column-3" width="33.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
            style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px"><span style="font-size:16px;">Payment method:</span></p></div></div></td></tr></table><table class="text_block block-3" width="100%" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
            <span style="font-size:16px;">&nbsp;{payment_type}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" 
            width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:10px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
            style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;mso-line-height-alt:18px;color:#000;line-height:1.5;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px;mso-line-height-alt:18px">&nbsp;</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" 
            role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;background-color:#555;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#fff;line-height:1.2"><p style="margin:0;font-size:12px;text-align:center;mso-line-height-alt:14.399999999999999px"><span style="font-size:16px;">PURCHASE SUMMARY</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
            width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">
            <strong><span style="font-size:14px;">Event</span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
            style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{event_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table 
            class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Team Name<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{team_names_flat} </span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
            width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><strong><span style="font-size:14px;">Purchase status<br></span></strong></p></div>
            </div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_status}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" 
            width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">
            <strong><span style="font-size:14px;">Total amount<br></span></strong></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td 
            class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{payment_amount}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table>
            <table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-14" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:10px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #222"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-15" align="center" width="100%" 
            border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;">If you have any questions, please contact event manager for details:</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-16" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" 
            role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
            class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p 
            style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Name</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px"><span style="font-size:14px;">{eo_first} {eo_last}</span></p></div>
            </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-17" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Email</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
            <table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p 
            style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_email}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-18" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" 
            style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px"><span style="font-size:14px;"><strong>Phone</strong></span></p></div></div></td></tr></table></td><td class="column column-2" width="75%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:5px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class 
            style="font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;mso-line-height-alt:16.8px">{eo_phone}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-19" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
            class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" 
            cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table>
            </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-20" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#333;width:620px" width="620"><tbody><tr><td class="column column-1" width="25%" 
            style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class 
            style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{facebook_icon}</p></div></div></td></tr></table></td><td class="column column-2" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
            class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p 
            style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{twitter_icon}</p></div></div></td></tr></table></td><td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{instagram_icon}</p></div></div></td></tr></table></td><td 
            class="column column-4" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-bottom:10px;padding-left:5px;padding-right:5px;padding-top:10px"><div 
            style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#000;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">{snapchat_icon}</p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-21" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
            class="divider_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" 
            style="font-size:1px;line-height:1px;border-top:1px dotted #ccc"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-22" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
            style="mso-table-lspace:0;mso-table-rspace:0;color:#000;width:620px" width="620"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:0;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
            <tr><td class="pad"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Lato,Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2">Copyright © 2022 SportWrench Inc., All rights reserved.<br>You subscribed to our newsletter via our website, <a style="text-decoration: underline; color: #71777D;" href="sportwrench.com" target="_blank" rel="noopener">sportwrench.com</a></div></div></td>
            </tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>', 
            'Purchase with successful payment {event_name}', 
            '{event_name} Dear, {cd_first} {cd_last}
            Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}  by SportWrench Inc.SUCCESSFUL PAYMENTAmount paid:{payment_amount}
            Date paid:{payment_date_paid}
            Payment method:
            {payment_type} PURCHASE SUMMARY
            Event{event_name}Team Name{team_names_flat} Purchase status
            {payment_status}
            Total amount{payment_amount}
            If you have any questions, please contact event manager for details:Name{eo_first} {eo_last}
            Email
            {eo_email}Phone{eo_phone}
            {facebook_icon}{twitter_icon}{instagram_icon}{snapchat_icon}
            Copyright © 2022 SportWrench Inc., All rights reserved.You subscribed to our newsletter via our website, sportwrench.com', null, null, null, 'Successful payment', null,
            '{
                "page": {
                "body": {
                    "type": "mailup-bee-page-properties",
                    "content": {
                    "style": {
                        "color": "#000000",
                        "font-family": "Lato, Tahoma, Verdana, Segoe, sans-serif"
                    },
                    "computedStyle": {
                        "linkColor": "#71777D",
                        "messageWidth": "620px",
                        "messageBackgroundColor": "transparent"
                    }
                    },
                    "webFonts": [
                    {
                        "url": "https://fonts.googleapis.com/css$2family=Lato",
                        "name": "Lato",
                        "fontFamily": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                    }
                    ],
                    "container": {
                    "style": {
                        "background-color": "#FFFFFF"
                    }
                    }
                },
                "rows": [
                    {
                    "type": "one-column-empty",
                    "uuid": "0e89c4b8-906b-4c22-811c-df1251c52939",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "925dc3ba-0d6a-4931-b1ed-42735888d874",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "20px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-image",
                            "uuid": "ea81271f-7c5c-4199-bb67-a5b7f0a9f621",
                            "locked": false,
                            "descriptor": {
                                "image": {
                                "alt": "Image",
                                "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/sw_logo.svg",
                                "href": "",
                                "width": "116px",
                                "height": "40px"
                                },
                                "style": {
                                "width": "100%",
                                "padding-top": "0px",
                                "padding-left": "0px",
                                "padding-right": "0px",
                                "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                "class": "center  autowidth ",
                                "width": "116px",
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "60428bcb-8ae5-4547-aac0-b15a58a88430",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "6fade5fa-4758-46a8-856f-8304e33c2b9c",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-divider",
                            "uuid": "1ed7ce7a-e6ac-43e0-bf37-bb48d3a18b4a",
                            "locked": false,
                            "descriptor": {
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "divider": {
                                "style": {
                                    "width": "100%",
                                    "height": "0px",
                                    "border-top": "1px solid #222222"
                                }
                                },
                                "computedStyle": {
                                "align": "center"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "85af7326-21b4-4faf-bd19-b5fdb9bfb608",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "131f9421-5b87-4f84-8d36-d7c601491464",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "4ea3ae11-2062-4f30-99c1-80cba5ae280f",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 18px; line-height: 21px;\" data-mce-style=\"font-size: 18px; line-height: 21px;\">{event_name}</span></p><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Dear, {cd_first} {cd_last}</span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: left;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"> Thank you for your purchase! This email is to confirm that you paid for teams registration on the {event_name}&nbsp; by SportWrench Inc.</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "343fbcfa-2f9b-43b1-84c5-c69eac801eb3",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "de4d12ed-5939-4fbd-bfe1-c88a82ed6653",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-image",
                            "uuid": "68be0c86-e761-4bda-8712-f396205b3e53",
                            "locked": false,
                            "descriptor": {
                                "image": {
                                "alt": "Image",
                                "src": "https://sw-email-media.s3.amazonaws.com/images/admin-yD7KXfNABi/editor_images/8ba25a10-9dfd-4e77-8de6-148953d21d28.png",
                                "href": "",
                                "width": "100px",
                                "height": "100px"
                                },
                                "style": {
                                "width": "100%",
                                "padding-top": "0px",
                                "padding-left": "0px",
                                "padding-right": "0px",
                                "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                "class": "center  autowidth ",
                                "width": "100px"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "57ac8774-5093-4d4f-b011-35458176b02f",
                    "columns": [
                        {
                        "uuid": "9422f281-9507-4434-ae34-7dd1f9483f79",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "e6e9749f-ddcc-4006-a0d8-3328114a1ea7",
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px;\"><span style=\"font-size: 22px; color: #3ab46b; line-height: 26px;\" data-mce-style=\"font-size: 22px; color: #3ab46b; line-height: 26px;\"><strong>SUCCESSFUL PAYMENT</strong></span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "mobileStyle": {},
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "500px",
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "border-right": "0px solid transparent",
                        "border-bottom": "0px solid transparent",
                        "border-radius": "0px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "verticalAlign": "top",
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false,
                        "rowReverseColStackOnMobile": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "three-columns-empty",
                    "uuid": "adccfd89-b115-44c5-a85a-b19a92153ba6",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "2a7a2896-6ead-4591-97ff-14188c00f297",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "aa8ab204-a297-407c-b0a3-be0b66c458b2",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Amount paid:</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            },
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "bf86f3db-ebae-4cbd-a412-b011e314e29c",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">{payment_amount}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 4
                        },
                        {
                        "uuid": "980e0c2b-9db0-4821-a45d-f0ee1081374c",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "e6dd8b7b-0280-447c-979d-584d2a1d01e5",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Date</span> paid:</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            },
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "5b70eb2c-9420-4e06-b98e-956bd912b0f4",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">{payment_date_paid}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 4
                        },
                        {
                        "uuid": "0e091979-e07d-4414-9293-200c298b296e",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "4086da00-a198-4849-a493-5b67913db7e1",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">Payment method:</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            },
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "3be3601b-c601-47ad-b20b-21922e8ad839",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"line-height: 19px; font-size: 16px;\" data-mce-style=\"line-height: 19px; font-size: 16px;\">&nbsp;{payment_type}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 4
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "4194e49f-97f7-4c6c-bd6a-a83670451f46",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "2aafd2d5-8497-4df2-ab4e-3b24a3758862",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "10px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "7ee54f3f-7d3a-4b2e-ba9d-8cf299138f19",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 18px;\" data-mce-style=\"font-size: 12px; line-height: 18px;\"><p style=\"font-size: 12px; line-height: 18px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 18px; word-break: break-word;\">&nbsp;</p></div>",
                                "style": {
                                    "color": "#000000",
                                    "font-family": "inherit",
                                    "line-height": "150%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "32c3bc61-1af2-496f-8b23-ee39cac2429a",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "207baf6c-6855-4302-841b-25248f4d1698",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "#555555"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "bd772cc2-b9b5-43f7-9e8a-57658f24fdec",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; text-align: center; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">PURCHASE SUMMARY</span></p></div>",
                                "style": {
                                    "color": "#FFFFFF",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "45868c87-e020-4a75-8df1-f7723152076e",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "47d7dcb9-b356-494c-aaeb-f3d2a18bd35c",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "6361aff3-0936-4448-803c-66ea2856982c",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Event</span></strong></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "bc602e5c-3a98-4bfa-b955-cbe47b23aa96",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "36bb4831-c3d7-49da-99c3-6a8d48bf26fd",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{event_name}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "4b518cf5-ebec-4325-b067-a4d897c868db",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "2a0680ef-525f-4da5-82b5-7afbfd126917",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "e078692e-0af2-4260-9ffa-1487fd3a93dd",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Team Name<br></span></strong></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "22d38f38-363f-446c-818b-1528b5121faa",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "c038f462-f613-41a0-b063-d7519574adc9",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{team_names_flat} </span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "436df4d5-519f-46f6-99bf-f542a6651b76",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "dbdb2fe6-8708-4f40-9272-478c8f96fe4f",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "c0598579-62b2-4cba-928e-2b3da62e0522",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Purchase status<br></span></strong></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "9e90c138-43d4-4972-91b7-1d0d20ffcb74",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "63675c56-b7dd-4692-8c98-b35535ec320f",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{payment_status}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "c027cde6-fb95-4943-9730-df05c4d35bf8",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "342f5ace-acfd-4932-af3a-f40e1d2109e4",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "7df5a87e-2659-49a2-a3d9-beed117bf4f1",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">Total amount<br></span></strong></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "4f42cef5-823c-4888-9c50-cb8f3777fec1",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "d17775aa-67cf-456a-8281-c714cffc5bb6",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{payment_amount}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "be233977-2639-405b-9397-8bb1d05ebc75",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "7dfe5f54-e911-44bd-b381-506ebfe28226",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-divider",
                            "uuid": "186d5ebd-b4d0-4606-9471-fbef5d1f631b",
                            "locked": false,
                            "descriptor": {
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "divider": {
                                "style": {
                                    "width": "100%",
                                    "border-top": "1px dotted #CCCCCC"
                                }
                                },
                                "computedStyle": {
                                "align": "center"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "506affb7-08f4-44cf-90ba-00fd5e5750c3",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "246a2062-960d-426e-b59d-ca5a37a5ecf3",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "10px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-divider",
                            "uuid": "a841869a-cc95-488e-a878-d7e585cb275c",
                            "locked": false,
                            "descriptor": {
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "divider": {
                                "style": {
                                    "width": "100%",
                                    "height": "0px",
                                    "border-top": "1px solid #222222"
                                }
                                },
                                "computedStyle": {
                                "align": "center"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "5780f28c-a4bd-415d-ab65-03d461b7d8c2",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "589a5673-166c-44cd-bd27-98156b8e9fbe",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "a5946608-c708-4c36-906d-c5f6413ab7ad",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"line-height: 16px; font-size: 14px;\" data-mce-style=\"line-height: 16px; font-size: 14px;\">If you have any questions, please contact event manager for details:</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "580881db-9333-4189-b4da-b17f57f7f98e",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "d92ee720-08fe-4a48-a6aa-d05a0cecbebc",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "47c45c49-c906-4535-846f-98b2c3eae423",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Name</strong></span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "144309b8-dbe4-425e-9e98-62707da32ab3",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "08b5b535-8a31-453c-bbb7-aa5e174ebe1e",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\">{eo_first} {eo_last}</span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "43027aa3-ddec-4531-a0cc-************",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "f360a096-5167-4527-aead-e9a3a611995d",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "68cdb1ac-3b8b-4d46-a1c0-eba660340798",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Email</strong></span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "9db903a7-5dab-4d41-8efc-7c918734cd71",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "2e3a4720-743a-4aca-9f4f-d797100f8588",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">{eo_email}</p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "row-2-columns-3-9",
                    "uuid": "5f50197d-98fb-4a3b-a702-ffb82ea158d2",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "9c1acd1c-087a-4807-84bd-772700d429c6",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "b650eba9-3abc-46d7-b11f-cff494cc48ed",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><strong>Phone</strong></span></p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "61a4b8c5-2365-4a69-8d25-fd7c7ee5a976",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "0d136d89-638c-4ef1-9031-b3861387c55f",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\" data-mce-style=\"font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">{eo_phone}</p></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 9
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "6697044f-8dc4-4cb9-aa6c-54e7325f1b7e",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "12691a3a-8f09-4075-82ef-ba2ea3f846b9",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-divider",
                            "uuid": "6cfd3035-b1d6-4190-843b-44ff7c89440a",
                            "locked": false,
                            "descriptor": {
                                "style": {
                                "padding-top": "20px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "20px"
                                },
                                "divider": {
                                "style": {
                                    "width": "100%",
                                    "border-top": "1px dotted #CCCCCC"
                                }
                                },
                                "computedStyle": {
                                "align": "center"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "four-columns-empty",
                    "uuid": "674eb499-8d05-4946-b271-5c07a32cf65a",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "243b5220-9c2d-401b-a8ea-720d82d54499",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "e397f2f2-fce5-424f-a67f-e34f9ffec126",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{facebook_icon}</p></div>",
                                "style": {
                                    "color": "#000000",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#000000"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "5px",
                                "padding-right": "5px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "8220a3d0-fd3b-42ea-acf9-fb10dda93969",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "25333cf4-b38f-47e3-aff6-52c3381b0f12",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{twitter_icon}</p></div>",
                                "style": {
                                    "color": "#000000",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#000000"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "5px",
                                "padding-right": "5px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "bde113d1-d3ae-4f55-a81d-6f46b41a23cf",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "c89788c2-67a9-49d7-8c37-358009f30df5",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{instagram_icon}</p></div>",
                                "style": {
                                    "color": "#000000",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#000000"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "5px",
                                "padding-right": "5px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        },
                        {
                        "uuid": "00841d46-4ca7-47fa-95b1-caab035c3c76",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "b52b2b64-8ba5-49cc-88d0-6d6406f10ddb",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\">{snapchat_icon}</p></div>",
                                "style": {
                                    "color": "#000000",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#000000"
                                }
                                },
                                "style": {
                                "padding-top": "5px",
                                "padding-left": "5px",
                                "padding-right": "5px",
                                "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 3
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#333",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "6abe7a75-e0fa-48c6-bfba-fd451d06e682",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "8e9e8f0b-e392-4eb3-b673-56d2f59748cf",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-divider",
                            "uuid": "b8587922-6dfa-483d-83ad-ca8088301a58",
                            "locked": false,
                            "descriptor": {
                                "style": {
                                "padding-top": "20px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "20px"
                                },
                                "divider": {
                                "style": {
                                    "width": "100%",
                                    "border-top": "1px dotted #CCCCCC"
                                }
                                },
                                "computedStyle": {
                                "align": "center"
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    },
                    {
                    "type": "one-column-empty",
                    "uuid": "01383499-845a-4073-bc3e-61fecc47feee",
                    "locked": false,
                    "columns": [
                        {
                        "uuid": "375e5671-2ca1-4010-b091-98fc2fbecff5",
                        "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                        },
                        "modules": [
                            {
                            "type": "mailup-bee-newsletter-modules-text",
                            "uuid": "ae38c7f4-b472-46ae-a1d2-5ab2965ebc16",
                            "locked": false,
                            "descriptor": {
                                "text": {
                                "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif;\">Copyright © 2022 SportWrench Inc., All rights reserved. <br>You subscribed to our newsletter via our website, <a style=\"text-decoration: underline;\" href=\"sportwrench.com\" target=\"_blank\" rel=\"noopener\">sportwrench.com</a></div>",
                                "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                },
                                "computedStyle": {
                                    "linkColor": "#71777D"
                                }
                                },
                                "style": {
                                "padding-top": "10px",
                                "padding-left": "10px",
                                "padding-right": "10px",
                                "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                "hideContentOnMobile": false
                                }
                            }
                            }
                        ],
                        "grid-columns": 12
                        }
                    ],
                    "content": {
                        "style": {
                        "color": "#000000",
                        "width": "620px",
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        },
                        "computedStyle": {
                        "hideContentOnMobile": false,
                        "rowColStackOnMobile": true,
                        "hideContentOnDesktop": false
                        }
                    },
                    "container": {
                        "style": {
                        "background-color": "transparent",
                        "background-image": "none",
                        "background-repeat": "no-repeat",
                        "background-position": "top left"
                        }
                    }
                    }
                ],
                "title": "",
                "template": {
                    "name": "template-base",
                    "type": "basic",
                    "version": "2.0.0"
                },
                "description": ""
                },
                "comments": {}
            }',null, 'teams.any.paid', true, 'teams.payments', true, null)
                RETURNING email_template_id
            ), insert_type AS (
            -- Create teams.any.paid template type ------------------------------------------------------------
            INSERT INTO public.email_template_type
            (type, email_template_group, title, description, long_title, is_trigger, default_email_template_id)
                VALUES ('teams.any.paid', 'teams.payments', 'Payment Successful',
                    '<em>Payment Successful</em>', 'Payment Successful', true,
                    (SELECT email_template_id
                    FROM main_template))
                    )
    
            -- Create event email trigger for teams.any.paid template type ------------------------------------------------------------
            INSERT
                INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
                VALUES ('teams.any.paid', 'teams.payments', (SELECT email_template_id FROM main_template), 0);
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        --- Delete default success payment template
        DELETE FROM email_template WHERE email_template_type = 'teams.any.paid' AND event_id IS NULL;
        DELETE FROM email_template_type WHERE type = 'teams.any.paid';
        DELETE FROM event_email_trigger WHERE email_template_type = 'teams.any.paid' and event_id = 0;

        -- Create tickets.payments template ------------------------------------------------------------
        WITH main_template AS (
            INSERT INTO public.email_template
            (email_html, email_subject, email_text,
            event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
            img_name, email_template_type, is_valid, email_template_group, published,
            deleted)
            VALUES
            ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
            
            <head>
                <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                <meta name="viewport" content="width=device-width">
                <!--[if !mso]><!-->
                <meta http-equiv="X-UA-Compatible" content="IE=edge">
                <!--<![endif]-->
                <title></title>
                <!--[if !mso]><!-->
                <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet" type="text/css">
                <link href="https://fonts.googleapis.com/css?family=Lato" rel="stylesheet" type="text/css">
                <!--<![endif]-->
                <style type="text/css">
                    body {
                        margin: 0;
                        padding: 0;
                    }
            
                    table,
                    td,
                    tr {
                        vertical-align: top;
                        border-collapse: collapse;
                    }
            
                    * {
                        line-height: inherit;
                    }
            
                    a[x-apple-data-detectors=true] {
                        color: inherit !important;
                        text-decoration: none !important;
                    }
                </style>
                <style type="text/css" id="media-query">
                    @media (max-width: 620px) {
            
                        .block-grid,
                        .col {
                            min-width: 320px !important;
                            max-width: 100% !important;
                            display: block !important;
                        }
            
                        .block-grid {
                            width: 100% !important;
                        }
            
                        .col {
                            width: 100% !important;
                        }
            
                        .col_cont {
                            margin: 0 auto;
                        }
            
                        img.fullwidth,
                        img.fullwidthOnMobile {
                            width: 100% !important;
                        }
            
                        .no-stack .col {
                            min-width: 0 !important;
                            display: table-cell !important;
                        }
            
                        .no-stack.two-up .col {
                            width: 50% !important;
                        }
            
                        .no-stack .col.num2 {
                            width: 16.6% !important;
                        }
            
                        .no-stack .col.num3 {
                            width: 25% !important;
                        }
            
                        .no-stack .col.num4 {
                            width: 33% !important;
                        }
            
                        .no-stack .col.num5 {
                            width: 41.6% !important;
                        }
            
                        .no-stack .col.num6 {
                            width: 50% !important;
                        }
            
                        .no-stack .col.num7 {
                            width: 58.3% !important;
                        }
            
                        .no-stack .col.num8 {
                            width: 66.6% !important;
                        }
            
                        .no-stack .col.num9 {
                            width: 75% !important;
                        }
            
                        .no-stack .col.num10 {
                            width: 83.3% !important;
                        }
            
                        .video-block {
                            max-width: none !important;
                        }
            
                        .mobile_hide {
                            min-height: 0px;
                            max-height: 0px;
                            max-width: 0px;
                            display: none;
                            overflow: hidden;
                            font-size: 0px;
                        }
            
                        .desktop_hide {
                            display: block !important;
                            max-height: none !important;
                        }
            
                        .img-container.big img {
                            width: auto !important;
                        }
                    }
                </style>
            </head>
            
            <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #FFFFFF;">
                <!--[if IE]><div class="ie-browser"><![endif]-->
                <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #FFFFFF; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#FFFFFF" valign="top">
                    <tbody>
                        <tr style="vertical-align: top;" valign="top">
                            <td style="word-break: break-word; vertical-align: top;" valign="top">
                                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#FFFFFF"><![endif]-->
                                <div style="background-color:transparent;">
                                    <div class="block-grid " style="min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: transparent;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                                            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                                            <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color:transparent;width:600px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                                            <div class="col num12" style="min-width: 320px; max-width: 600px; display: table-cell; vertical-align: top; width: 600px;">
                                                <div class="col_cont" style="width:100% !important;">
                                                    <!--[if (!mso)&(!IE)]><!-->
                                                    <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                                        <!--<![endif]-->
                                                        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
                                                        <div style="color:#555555;font-family:Lato, Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                                                            <div class="txtTinyMce-wrapper" style="font-size: 12px; line-height: 1.2; color: #555555; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 14px;">
                                                                <p style="margin: 0; font-size: 18px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 22px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 18px;">Payment Successful</span></p>
                                                            </div>
                                                        </div>
                                                        <!--[if mso]></td></tr></table><![endif]-->
                                                        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
                                                        <div style="color:#555555;font-family:Lato, Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                                                            <div class="txtTinyMce-wrapper" style="font-size: 12px; line-height: 1.2; color: #555555; font-family: Lato, Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 14px;">
                                                                <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;">Club - {club_name}&nbsp;</p>
                                                                <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;">Teams - {team_names}&nbsp;</p>
                                                                <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;">Amount - {payment_amount}&nbsp;</p>
                                                            </div>
                                                        </div>
                                                        <!--[if mso]></td></tr></table><![endif]-->
                                                        <!--[if (!mso)&(!IE)]><!-->
                                                    </div>
                                                    <!--<![endif]-->
                                                </div>
                                            </div>
                                            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (IE)]></div><![endif]-->
            </body>
            
            </html>', 
            'Payment Received for {event_name}', 
            'Payment Successful
            Club - {club_name}
            Teams - {team_names}
            Amount - {payment_amount}', null, null, null, 'Successful Card Payment', null,
            '{
                "page": {
                  "body": {
                    "type": "mailup-bee-page-properties",
                    "content": {
                      "style": {
                        "color": "#000000",
                        "font-family": "Lato, Tahoma, Verdana, Segoe, sans-serif"
                      },
                      "computedStyle": {
                        "linkColor": "#0068A5",
                        "messageWidth": "600px",
                        "messageBackgroundColor": "transparent"
                      }
                    },
                    "webFonts": [
                      {
                        "url": "https://fonts.googleapis.com/css?family=Montserrat",
                        "name": "Montserrat",
                        "fontFamily": "''Montserrat'', ''Trebuchet MS'', ''Lucida Grande'', ''Lucida Sans Unicode'', ''Lucida Sans'', Tahoma, sans-serif"
                      },
                      {
                        "url": "https://fonts.googleapis.com/css?family=Lato",
                        "name": "Lato",
                        "fontFamily": "''Lato'', Tahoma, Verdana, Segoe, sans-serif"
                      }
                    ],
                    "container": {
                      "style": {
                        "background-color": "#FFFFFF"
                      }
                    }
                  },
                  "rows": [
                    {
                      "type": "one-column-empty",
                      "uuid": "3ae0266e-72d8-4eb4-8543-47d6ae810d2d",
                      "columns": [
                        {
                          "uuid": "35b2a4e1-2ed8-4319-9ff0-6e3e9810c836",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "76f438a1-94ae-4650-b1b1-0bfae276e639",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\\"txtTinyMce-wrapper\\" style=\\"font-size: 12px; line-height: 14px;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\"><p style=\\"font-size: 12px; line-height: 14px; word-break: break-word;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\"><span style=\\"font-size: 18px; line-height: 21px;\\" data-mce-style=\\"font-size: 18px; line-height: 21px;\\">Payment Successful</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0068A5"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            },
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "5b72f5a4-54b9-4bcd-bc74-f447ee1d4c30",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\\"txtTinyMce-wrapper\\" style=\\"font-size: 12px; line-height: 14px;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\"><p style=\\"font-size: 12px; line-height: 14px; word-break: break-word;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\">Club - <code spellcheck=\\"false\\" data-bee-type=\\"mergetag\\" data-bee-code=\\"\\" data-bee-name=\\"Club Name\\">{club_name}</code>&nbsp;</p><p style=\\"font-size: 12px; line-height: 14px; word-break: break-word;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\">Teams - <code spellcheck=\\"false\\" data-bee-type=\\"mergetag\\" data-bee-code=\\"\\" data-bee-name=\\"Team Name(s)\\">{team_names}</code>&nbsp;</p><p style=\\"font-size: 12px; line-height: 14px; word-break: break-word;\\" data-mce-style=\\"font-size: 12px; line-height: 14px;\\">Amount - <code spellcheck=\\"false\\" data-bee-type=\\"mergetag\\" data-bee-code=\\"\\" data-bee-name=\\"Payment Amount\\">{payment_amount}</code>&nbsp;</p></div>",
                                  "style": {    
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0068A5"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    }
                  ],
                  "title": "",
                  "template": {
                    "name": "template-base",
                    "type": "basic",
                    "version": "2.0.0"
                  },
                  "description": ""
                },
                "comments": {}
              }',null, 'teams.any.paid', true, 'teams.payments', true, null)
                RETURNING email_template_id
            ), insert_type AS (
            -- Create teams.any.paid template type ------------------------------------------------------------
            INSERT INTO public.email_template_type
            (type, email_template_group, title, description, long_title, is_trigger,
            default_email_template_id)
                VALUES ('teams.any.paid', 'teams.payments', 'Payment Successful',
                    '<em>Payment Successful</em>', 'Payment Successful', true,
                    (SELECT email_template_id
                    FROM main_template))
                    )
    
            -- Create event email trigger for teams.any.paid template type ------------------------------------------------------------
            INSERT
                INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
                VALUES ('teams.any.paid', 'teams.payments', (SELECT email_template_id FROM main_template), 0);
      `);
};
