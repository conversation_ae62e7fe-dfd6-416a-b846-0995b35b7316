
exports.up = function(knex) {
    return knex.schema.raw(`
        create or replace view v_aem_template_usage(email_template_id, email_template_group, email_template_type, qty) as
        SELECT usage.email_template_id,
            usage.email_template_group,
            usage.email_template_type,
            CASE usage.default_qty
               WHEN 0 THEN usage.non_default_qty
               ELSE usage.non_default_qty + usage.events_qty
            END AS qty
        FROM (
            SELECT eet.email_template_id,
                eet.email_template_group,
                eet.email_template_type,
                count(eet.*) FILTER (WHERE eet.event_id > 0)               AS non_default_qty,
                count(eet.*) FILTER (WHERE eet.event_id = 0)               AS default_qty,
                (
                    SELECT count(tmpl_usage_by_event.*) AS count
                    FROM (
                        SELECT e.event_id
                        FROM event e
                        LEFT JOIN event_email_trigger e_trigger ON e_trigger.event_id = e.event_id AND
                            e_trigger.email_template_group = eet.email_template_group AND
                            e_trigger.email_template_type = eet.email_template_type
                        WHERE e.live_to_public IS TRUE
                            AND e.is_test IS NOT TRUE
                            AND CASE eet.email_template_group
                                WHEN 'clubs'::text THEN e.allow_teams_registration
                                WHEN 'tickets'::text THEN e.allow_ticket_sales
                                WHEN 'officials'::text THEN e.has_officials
                                WHEN 'head.referee'::text THEN e.has_officials
                                WHEN 'teams.refunds'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_check_payments)
                                WHEN 'tickets.refunds'::text THEN e.allow_ticket_sales
                                WHEN 'tickets.payments'::text THEN e.allow_ticket_sales
                                WHEN 'booths.payments'::text THEN e.has_exhibitors
                                WHEN 'exhibitors'::text THEN e.has_exhibitors
                                WHEN 'camps'::text THEN e.allow_ticket_sales AND e.ticket_camps_registration IS TRUE
                                WHEN 'staff'::text THEN e.has_staff
                                WHEN 'teams.payments'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_check_payments)
                                WHEN 'teams_uncollected_fee_payments'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_check_payments)
                                WHEN 'tickets_uncollected_fee_payments'::text THEN e.allow_ticket_sales AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_check_payments)
                                ELSE NULL::boolean
                            END
                            AND e.date_end::date > 'now'::text::date
                        GROUP BY e.event_id
                        HAVING count(e_trigger.*) = 0
                    ) tmpl_usage_by_event
                ) AS events_qty
            FROM event_email_trigger eet
            GROUP BY eet.email_template_id, eet.email_template_group, eet.email_template_type
        ) usage;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
                create or replace view v_aem_template_usage(email_template_id, email_template_group, email_template_type, qty) as
        SELECT usage.email_template_id,
            usage.email_template_group,
            usage.email_template_type,
            CASE usage.default_qty
               WHEN 0 THEN usage.non_default_qty
               ELSE usage.non_default_qty + usage.events_qty
            END AS qty
        FROM (
            SELECT eet.email_template_id,
                eet.email_template_group,
                eet.email_template_type,
                count(eet.*) FILTER (WHERE eet.event_id > 0)               AS non_default_qty,
                count(eet.*) FILTER (WHERE eet.event_id = 0)               AS default_qty,
                (
                    SELECT count(tmpl_usage_by_event.*) AS count
                    FROM (
                        SELECT e.event_id
                        FROM event e
                        LEFT JOIN event_email_trigger e_trigger ON e_trigger.event_id = e.event_id AND
                            e_trigger.email_template_group = eet.email_template_group AND
                            e_trigger.email_template_type = eet.email_template_type
                        WHERE e.live_to_public IS TRUE
                            AND e.is_test IS NOT TRUE
                            AND CASE eet.email_template_group
                                WHEN 'clubs'::text THEN e.allow_teams_registration
                                WHEN 'tickets'::text THEN e.allow_ticket_sales
                                WHEN 'officials'::text THEN e.has_officials
                                WHEN 'head.referee'::text THEN e.has_officials
                                WHEN 'teams.refunds'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_card_payments)
                                WHEN 'tickets.refunds'::text THEN e.allow_ticket_sales
                                WHEN 'tickets.payments'::text THEN e.allow_ticket_sales
                                WHEN 'booths.payments'::text THEN e.has_exhibitors
                                WHEN 'exhibitors'::text THEN e.has_exhibitors
                                WHEN 'camps'::text THEN e.allow_ticket_sales AND e.ticket_camps_registration IS TRUE
                                WHEN 'staff'::text THEN e.has_staff
                                WHEN 'teams.payments'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_card_payments)
                                WHEN 'teams_uncollected_fee_payments'::text THEN e.allow_teams_registration AND
                                    (e.allow_ach_payments OR e.allow_card_payments OR e.allow_card_payments)
                                ELSE NULL::boolean
                            END
                            AND e.date_end::date > 'now'::text::date
                        GROUP BY e.event_id
                        HAVING count(e_trigger.*) = 0
                    ) tmpl_usage_by_event
                ) AS events_qty
            FROM event_email_trigger eet
            GROUP BY eet.email_template_id, eet.email_template_group, eet.email_template_type
        ) usage;
    `)
};
