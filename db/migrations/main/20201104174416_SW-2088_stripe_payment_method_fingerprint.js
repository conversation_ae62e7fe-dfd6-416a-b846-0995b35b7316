
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."stripe_payment_method" ADD COLUMN "card_fingerprint" TEXT NOT NULL;
        
        CREATE UNIQUE INDEX "stripe_payment_method_card_fingerprint_stripe_customer_id" 
            ON "public"."stripe_payment_method" (card_fingerprint, stripe_customer_id);
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."stripe_payment_method" DROP COLUMN IF EXISTS "card_fingerprint";
        
        DROP INDEX IF EXISTS "public"."stripe_payment_method_card_fingerprint_stripe_customer_id";
    `);
};
