
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE OR REPLACE FUNCTION generate_organization_code(age INT, rank TEXT, gender team_gender, code TEXT, region TEXT)
            RETURNS TEXT AS
        $$
        DECLARE
            organization_code TEXT;
        BEGIN
            organization_code = UPPER(
                                    (CASE WHEN gender = 'male' then 'b' ELSE 'g' END) ||
                                    (CASE WHEN age < 10 then '0' || age::TEXT ELSE age::TEXT END) ||
                                    code ||
                                    rank ||
                                    region
                );
        
            RETURN organization_code;
        END;
        $$ LANGUAGE plpgsql IMMUTABLE;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`DROP FUNCTION IF EXISTS generate_organization_code;`);
};
