<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><%= event_name %> Online Check In</title>
    <link rel="stylesheet" href="<%= sails.config.urls.main_app.baseUrl %>/styles/main.css"/>
</head>
<body class="tickets-receipt">
    <div class="container">
        <div class="row row-space hidden-print hidden-xs">
            <div class="col-sm-12">
                <button class="btn btn-primary pull-right" onclick="window.print()">Print</button>
            </div>
        </div>
        <h4><%= event_name %> Online Teams Check In <br/><small class="text-grey"><%= staffer_name %> (<%= club_name %>)</small></h4>
        <p>You are currently assigned to pick up wristbands for these teams:</p>     
        <div class="row">
            <div class="col-md-4 col-md-push-8">
                <% if (!all_checked_in_teams) { %>
                    <img style="<%= barcode_border_color ? `border: 15px #a5a4a4 solid !important; outline: 10px solid ${barcode_border_color};` : '' %> " src="<%= imageLink %>" alt="QR Code">
                <% } %>
            </div>
            <div class="col-md-8 col-md-pull-4">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Team Name</th>
                      <th>USAV Code</th>
                      <th>Checked In</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(var i = 0, l = teams.length; i < l; ++i) { %>
                    <tr>
                      <td><%= teams[i].team_name %></td>
                      <td><%= teams[i].usav_code %></td>
                      <% if (teams[i].status_checkin === 'checkedin') { %>
                        <td><span class="gl-success fa fa-check"></span></td>
                      <% } else if (teams[i].status_checkin === null || teams[i].status_checkin === 'notcheckedin' ) {%>
                        <td><span class="gl-danger fa fa-times"></span></td>
                      <% } else if (teams[i].status_checkin === 'pending') { %>
                        <td><span class="gl-info fa fa-clock-o"></span></td>
                      <% } else if (teams[i].status_checkin === 'alert' ) {%>
                        <td><span class="gl-danger fa fa-exclamation-circle"></span></td>
                      <% } %>
                    </tr>
                    <% } %>
                  </tbody>
                </table>
            </div>            
        </div>
        <hr/>
        <div class="row row-space">
            <div class="col-sm-6">
                <span>&copy; SportWrench Inc. <%= new Date().getUTCFullYear() %></span>
            </div>
            <div class="col-sm-6">
                <img src="/images/types.png" class="pull-right" alt="card types">
            </div>
        </div>
    </div>
</body>
</html>
