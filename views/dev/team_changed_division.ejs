<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Report: Team's division changed</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/css/bootstrap.min.css" integrity="sha384-PsH8R72JQ3SOdhVi3uxftmaW6Vc51MKb0q5P2rRUpPvrszuE4W1povHYgTpBfshb" crossorigin="anonymous">
</head>
<body style="width: 640px;margin: 0;padding: 0;font-family: arial, sans-serif;font-size: 16px;">
    <div class="container">
        <h4>Division for the team <i>"<%= old.team_name %>"</i> has changed to a different registration fee.</h4>
        <div class="row">
            <div class="col-sm-12">
                It's important for EO to remember that S<PERSON> doesn't recalculate registration fee for the team if it was already paid before the division change.
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Event Name</lable>
            </div>
            <div class="col-sm-4">
                <label><%= old.event_long_name %></label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Event ID</lable>
            </div>
            <div class="col-sm-4">
                <label><%= old.event_id %></label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Roster Team ID</lable>
            </div>
            <div class="col-sm-4">
                <label><%= old.roster_team_id %></label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Master Team ID</lable>
            </div>
            <div class="col-sm-4">
                <label><%= old.master_team_id%></label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Club Owner ID</lable>
            </div>
            <div class="col-sm-4">
                <label>
                    <%= current.club_owner_id%>
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <lable>Payment Status</lable>
            </div>
            <div class="col-sm-4">
                <label>
                    <% if(old.status_paid === 24) { %>
                        <lable>Pending</lable>
                    <% } else if (old.status_paid === 22) { %>
                        <lable>Paid</lable>
                    <% } else {%>
                        <lable>Unknown: <%= old.status_paid %> </lable>
                    <% } %>
                </label>
            </div>
        </div>
        <table class="table table-bordered table-sm">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Old</th>
                    <th>Current</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Reg Fee</td>
                    <td>
                        <%= old.reg_fee %>
                    </td>
                    <td>
                        <%= current.reg_fee %>
                    </td>
                </tr>
                <tr>
                    <td>Division name</td>
                    <td>
                        <%= old.division_name %>
                    </td>
                    <td>
                        <%= current.division_name %>
                    </td>
                </tr>
                <tr>
                    <td>Division ID</td>
                    <td>
                        <%= old.division_id %>
                    </td>
                    <td>
                        <%= current.division_id %>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

</body>

