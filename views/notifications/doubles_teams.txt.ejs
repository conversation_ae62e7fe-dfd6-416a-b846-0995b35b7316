Thank you for your entry into "<%- event_name %>"
Team "<%- team_name %>"updates: 
<% if (action === 'team.entry.accepted') { %>
    The team is "Accepted"
<% } %>
<% if (action === 'team.entry.declined') { %>
    The team has been withdrawn from the event
<% } %>
<% if (action === 'team.deleted') { %>
    The team has been removed by the event owner
<% } %>
<% if (action === 'team.division.changed') { %>
    The team has been moved to division "<%- division_name %>"
<% } %>
<% if (action === 'team.entry.waiting') { %>
    The team has been marked as "Waiting"
    The status for teams listed as Waiting will be changed to Accepted when space is available for the division in which you have entered. Waiting teams will be accepted in order by the date they have fulfilled all entry criteria.
<% } %>
<% if (action === 'team.entry.pending') { %>
    The team has been marked as "Pending"
    The status for teams listed as Pending will be changed to Accepted when you have paid for the entry fee and satisfied all required entry criteria <% if(rules_website || event_website) { %>as defined on the <%- rules_website || event_website %> "<%- event_name %>" web site</a> <% } %>until this division has filled. Once the division you have entered is filled, teams with an entry status still Pending will be placed on a waiting list.  
<% } %>

SportWrench Inc. <%= new Date().getUTCFullYear() %>. All rights reserved
