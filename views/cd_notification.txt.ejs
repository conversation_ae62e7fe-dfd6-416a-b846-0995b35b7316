Thank you for your entry into "<%- club.event_name %>"<% var index = 0; %>
<% if (club.roster_teams.length > 0 && club.roster_teams[0].status_entry === 12) { %>You currently have a status of "Accepted" for these teams:<% for (; index < club.roster_teams.length; ++index) { if(club.roster_teams[index].status_entry !== 12) break; %>
 - <%- club.roster_teams[index].team_name %> (<%- club.roster_teams[index].division_name %>) <% } %><% } %>
<% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 13) { %>You currently have a status of "Pending" for these teams:<% for (; index < club.roster_teams.length; ++index) { if(club.roster_teams[index].status_entry !== 13) break; %>
 - <%- club.roster_teams[index].team_name %> (<%- club.roster_teams[index].division_name %>) <% } %>
The status for teams listed as Pending will be changed to Accepted when you have paid for the entry fee and satisfied all required entry criteria as defined on the "<%- club.event_name %>" web site <%- club.rules_website || club.website %> until this division has filled. Once the division you have entered is filled, teams with an entry status still Pending will be placed on a waiting list.<% } %>
<% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 14) { %>You currently have a status of "Wait List" for these teams:<% for (; index < club.roster_teams.length; ++index) { if(club.roster_teams[index].status_entry !== 14) break; %>
- <%- club.roster_teams[index].team_name %> (<%- club.roster_teams[index].division_name %>) <% } %>
The status for teams listed as Wait List will be changed to Accepted when space is available for the division in which you have entered.
Waiting teams will be accepted in order by the date they have fulfilled all entry criteria.<% } %>
<% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 11) { %>You currently have a status of "Not accepted" for these teams:<% for (; index < club.roster_teams.length; ++index) { if(club.roster_teams[index].status_entry !== 11) break; %>
 - <%- club.roster_teams[index].team_name %> (<%- club.roster_teams[index].division_name %>) <% } %><% } %>

<% if (club.event_id == 19036) { %>
REMINDER: We are a Stay & Play event. This means you must create a housing block with THS in order to be accepted into the event. Once you have paid, please click the link below to get started!

https://secure.thsweb.com/Event2.aspx?TournamentID=5599&Cid=16&RceId=227

(Are you a local team? Send an <NAME_EMAIL> to see if you fall under the local teams status)

If you have questions about booking, please contact THS at 888.536.8326
<% } else if (club.event_id == 19044 || club.event_id == 19045) { %>
REMINDER: We are a Stay & Play event. This means you must pay before you can have access to the loyalty application or any housing applications

(Are you a local team? Send an <NAME_EMAIL> to see if you fall under the local teams status)

If you have questions about booking, please contact THS at 888.536.8326
<% } else { %>
Make sure to satisfy all roster and results requirements by the published deadlines and familiarize yourself with team check in procedures on the event web site <%- club.rules_website || club.website %>.

We encourage you and all your coaches to sign up for an event newsletter if it is offered so you stay up to date with any
announcements regarding the upcoming event. Also look for us on Facebook, Twitter and Instagram.

Thank you for your patronage!
<% } %>

SportWrench Inc. <%= new Date().getUTCFullYear() %>. All rights reserved
