<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= event_name %></title>
</head>
<body style="width: 640px; font-family: helvetica, arial; font-size: 16px">
    <% if(sales_type !== 'camps') { %>
    <p style="font-weight: bold; font-size: 18px;"><b><%= event_name %></b></p> 
    <div>If you don't see a QR Image, click here:
        <a href="<%= receipt_url %>">
            Open <%= specificLabels.openLabel %>
        </a>
    </div>
    <table style="margin: 0; padding: 0" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td align="center">
                <h1><%= first %> <%= last %></h1>
                <img src="<%= qr_url %>" alt="QR Code">
                <br/>
                <a href="<%= apple_wallet_url %>"  target="_blank">
                    <img style="coursor: pointer; height: 34px; margin-top: 10px" src="<%= apple_wallet_icon %>" alt="Add to apple wallet">
                </a>
            </td>
        </tr>
    </table> 
    <% } %>
    <% if(payment_method === 'check') { %>
        <%- check_details %>
    <% } %> 
    <p style="margin-top: 6px; margin-bottom: 6px">
        <% if(sales_type === 'camps') { %>
            <ul>
            <% for(var i = 0; i < receipt.length; ++i) { %>
                <li><%= receipt[i].camp_dates %>: <%= receipt[i].camp_name %>: <%= receipt[i].label %> ($<%= receipt[i].price %>)</li>
            <% } %>
            </ul>
        <% } else { %>
            <% for(var i = 0; i < receipt.length; ++i) { %>
                <% if(i > 0) { %>&#44;<% } %>
                <%= receipt[i].quantity %> <%= receipt[i].label %>
            <% } %>
        <% } %>
    </p>
    <% if(sales_type !== 'camps') { %>
    <p style="margin-top: 6px; margin-bottom: 6px"><%= specificLabels.barcodeLabel %>%> barcode: <%= barcode %></p>
    <% } %>
    <p style="margin-top: 6px; margin-bottom: 6px">
        <%= specificLabels.purchaserLabel %>: <%= last %>&#44;&nbsp;<%= first %>
    </p>

    <% for(var i = 0, l = additional_fields.length; i < l; ++i) { %>
        <% if (!additional_fields[i].show_on || additional_fields[i].show_on.receipt) { %>
        <p style="margin-top: 6px; margin-bottom: 6px">
            <%= additional_fields[i].label %>:
            <% var val = additional[additional_fields[i].field] %>
            <% if (additional_fields[i].type === 'select'
                && additional_fields[i].options
                && additional_fields[i].options[val]) { %>
            <%=  additional_fields[i].options[val] %>
            <% } else { %>
            <%=   val || ' - ' %>
            <% } %>
        </p>
        <% } %>
    <% } %>

    <% if(sw_fee_payer === 'buyer') {%>
        <p style="margin-top: 6px; margin-bottom: 6px">Service Fee: $<%= service_fee %></p>
    <% } %>

    <% if(stripe_fee_payer === 'buyer' && payment_method !== 'check') {%>
        <p style="margin-top: 6px; margin-bottom: 6px">Credit Card Merchant Fee: $<%= credit_card_merchant_fee %></p>
    <% } %>
    
    <p style="margin-top: 6px; margin-bottom: 6px">Total Price: <b>$<%= total %></b></p>
    <% if(sales_type !== 'camps') { %>
    <p style="margin-top: 6px; margin-bottom: 6px">Thank you for your purchase! If you can see the above QR code on your mobile device, it can be scanned at the event without needing to print it.</p>
    <div>
        <a href="<%= receipt_url %>">Open <%= specificLabels.openLabel %></a>
    </div>
    <% } %>
    <% if(description) { %><p><%- description %></p><% } %>
    <% if(social_links.length) { %>
        <p><b>Follow us:</b> <%- include('../socials.ejs') %></p>
    <% } %>
    <p style="margin-top: 25px; text-align: center">&copy; SportWrench Inc. <%= new Date().getUTCFullYear() %></p>
</body>
</html>
