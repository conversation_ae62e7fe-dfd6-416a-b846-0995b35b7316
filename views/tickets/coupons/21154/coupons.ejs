<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><%= subject %></title>
</head>
<body>
  Club Directors,
  <p>Below you will find <b style="text-decoration:underline">IMPORTANT</b> information regarding spectator policies and admission <span style="text-decoration:underline">for the 12s MEPL, played at Munciana Volleyball Club <b style="color:red">ONLY.</b></span></p>

  <p>
    <span style="text-decoration:underline;background-color: yellow;font-size:20px;font-weight:bold">
        PLEASE READ ENTIRE EMAIL!
    </span>
  </p>
  <p>It is imperative that this information is distributed to all persons from your team planning to attend the 12’s division at Munciana Volleyball Club.</p>
  <b style="text-decoration:underline;font-size:16px;">Admissions</b>
  <ol style="margin-top:1px;line-height:20px">
    <li>All tickets must be purchased ONLINE</li>
    <li style="font-weight:bold">NO TICKETS WILL BE SOLD ONSITE</li>
    <li>Only 1 ticket can be purchased during a transaction.
      <dl style="margin-top:0px;margin-bottom:0px;">
        <dd style="margin-inline-start: 20px;">
          a. I.E. Mom can purchase a ticket and checkout, then use code again to log back in and purchase a second ticket.
        </dd>
      </dl>
    </li>
    <li>QR Codes must be shown at the door with Identification. Make sure the tickets are filled out with the name of the person using them.</li>
    <li>Tickets are capped at <b>25</b> per team link. No exceptions will be made. Once the team link has reached max allotment it will discontinue sales. </li>
    <li>Each team has their own link and is in this email below.</li>
    <li>Tickets must be purchased for anyone <b style="text-decoration:underline;">3 years of age or above</b>.</li>
    <li>No weekend passes may be purchased; just single day tickets.</li>
    <li><b>NO REFUNDS</b> for any purchased tickets. No exceptions!</li>
  </ol>
  <h3>Mask MUST be worn by all attendees</h3>
  <table border="1">
    <tr>
        <th>Team Name</th>
        <th>Code for buying ticket</th>
        <th>Avalible ticket date</th>
        <th>Ticket Max</th>
    </tr>
    <% for(const coupon of coupons) { %>
        <tr>
            <td><%= coupon.teamName %></td>
            <td><%= coupon.code %></td>
            <td><%= coupon.validDates.join(', ') %></td>
            <td><%= coupon.quantity %></td>
        </tr>
    <% } %>
  </table>
</body>
</html>
