<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>SW | Club updates</title>
</head>
<style>
   p {
    text-indent: 20px;
   }
</style>
<body style="width: 640px;margin: 0;padding: 0;font-family: arial, sans-serif;font-size: 16px;">
        <% var index = 0; %>
        <% if (club.roster_teams.length > 0 && club.roster_teams[0].status_entry === 12) { %>
            <p>You currently have a status of <span style="color: green;">"Accepted"</span> for these teams:</p>
            <ul style="color: green;">
            <% for (; index < club.roster_teams.length; ++index) { %>
                <% if(club.roster_teams[index].status_entry !== 12) break; %>
                <li>
                    <%= club.roster_teams[index].team_name %>
                    (<%= club.roster_teams[index].division_name %>)
                </li>
            <% } %>
            </ul>            
        <% } %>
        <% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 13) { %>
            <p>Thank you for your entry into <b>"<%= club.event_name %>"</b>!</p>
            <p>You currently have a status of <span style="color: blue;">"Pending"</span> for these teams:</p>
            <ul style="color: blue;">
            <% for (; index < club.roster_teams.length; ++index) { %>
                <% if(club.roster_teams[index].status_entry !== 13) break; %>
                <li>
                    <%= club.roster_teams[index].team_name %>
                    (<%= club.roster_teams[index].division_name %>)
                </li>
            <% } %>
            </ul>
            <% if (club.event_id == 19044 || club.event_id == 19045) { %>
                <p>Your status will be changed to Accepted when you have paid for your entry fee or your entry fee has been received AND housing requirements have been fulfilled. Teams who are not Accepted before this division fills will be placed on a waiting list.</p>
            <% } else { %>
                <p>The status for teams listed as Pending will be changed to Accepted when you have paid for the entry fee and satisfied all required entry criteria as defined on the <a href="<%= club.rules_website || club.website %>">"<%= club.event_name %>" web site</a> until this division has filled. Once the division you have entered is filled, teams with an entry status still Pending will be placed on a waiting list.</p>
            <% } %>
        <% } %>
        <% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 14) { %>
            <p>You currently have a status of <span style="color: orange;">"Wait List"</span> for these teams:</p>
            <ul style="color: orange;">
            <% for (; index < club.roster_teams.length; ++index) { %>
                <% if(club.roster_teams[index].status_entry !== 14) break; %>
                <li>
                    <%= club.roster_teams[index].team_name %>
                    (<%= club.roster_teams[index].division_name %>)
                </li>
            <% } %>
            </ul>
            <p>The status for teams listed as Wait List will be changed to Accepted when space is available for the division in which you have entered.
            Waiting teams will be accepted in order by the date they have fulfilled all entry criteria.</p>
        <% } %>
        <% if (club.roster_teams.length > index && club.roster_teams[index].status_entry === 11) { %>
            <p><span style="color: red;">The following teams have been withdrawn from the event:</span></p>
            <ul style="color: red;">
                <% for (; index < club.roster_teams.length; ++index) { %>
                    <% if(club.roster_teams[index].status_entry === 11) { %>
                        <li>
                            <%= club.roster_teams[index].team_name %>
                            (<%= club.roster_teams[index].division_name %>)
                        </li> 
                    <% } %> 
                <% } %>
            </ul>
        <% } %>

        <% if (club.event_id == 19036) { %>
            <p><span style="color: red;"><b>REMINDER:</b></span> We are a Stay & Play event. This means you must create a housing block with THS in order to be accepted into the event. Once you have paid, please click the link below to get started!</p>

            <p>
                <a href="https://secure.thsweb.com/Event2.aspx?TournamentID=5599&Cid=16&RceId=227">https://secure.thsweb.com/Event2.aspx?TournamentID=5599&Cid=16&RceId=227</a>
            </p>

            <p>(Are you a local team? Send an email to <a href="mailto:<EMAIL>"><EMAIL></a> to see if you fall under the local teams status)</p>

            <p>If you have questions about booking, please contact THS at <a href="tel:8885368326">************</a></p>
        <% } else if(club.event_id == 19044 || club.event_id == 19045) { %>
            <p><span style="color: red;"><b>REMINDER:</b></span> We are a Stay & Play event. This means you must <span style="text-decoration: underline; font-weight: bold;">pay</span> before you can have access to the loyalty application or any housing applications.</p>

            <p>(Are you a local team? Send an email to <a href="mailto:<EMAIL>"><EMAIL></a> to see if you fall under the local teams status)</p>

            <p>If you have questions about booking, please contact THS at <a href="tel:8885368326">************</a></p>
        <% } else { %>
            <p>Make sure to satisfy all roster and results requirements by the published deadlines and familiarize yourself with team check in procedures on the event <a href="<%= club.rules_website || club.website %>">web site</a>.</p>

            <p>We encourage you and all your coaches to sign up for an event newsletter if it is offered so you stay up to date with any announcements regarding the upcoming event.  Also look for us on Facebook, Twitter and Instagram.</p>

            <p>Thank you for your patronage!</p>
        <% } %>
<p style="font-size: 10px;padding: 10px;text-align: center;">&copy; SportWrench Inc. <%= new Date().getUTCFullYear() %>. All rights reserved</p>
</body>
</html>
