<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= event_name %></title>
</head>
<body style="width: 640px; font-family: helvetica, arial; font-size: 16px">
<p>
    <% if (hasLostDisputes && hasFailedACHPayments) { %>
        <b>The <%= event_short_name %> has LOST the previously filed dispute and Failed ACH payment penalty.</b>
    <% } else if (hasLostDisputes) { %>
        <b>The <%= event_short_name %> has LOST the previously filed dispute.</b>
    <% } else if (hasFailedACHPayments) { %>
        <b>The <%= event_short_name %> has Failed ACH payment penalty.</b>
    <% } %>
</p>
<p>
    A successful payment has been charged to the <%= payment_method_type %> ending in <%= card_last_4 %>
    for fees related to the
    <% if (hasLostDisputes && hasFailedACHPayments) { %>
        Failed ACH payment and lost dispute.
    <% } else if (hasLostDisputes) { %>
        lost dispute.
    <% } else if (hasFailedACHPayments) { %>
        Failed ACH payment.
    <% } %>
</p>
<p>
    <span style="margin-bottom: 6px; display: block"><b>Club Details:</b></span>
    <span style="margin-bottom: 6px; display: block">Club Director: <%= club_details.director_full_name %></span>
    <span style="margin-bottom: 6px; display: block">Club Name: <%= club_details.club_name %></span>
    <span style="margin-bottom: 6px; display: block">Email: <%= club_details.director_email %></span>
    <span style="margin-bottom: 6px; display: block">Phone: <%= club_details.director_phone %></span>
</p>
<%- include('./payment-details/payment-details.ejs') %>
<% if (hasLostDisputes) { %>
    <p>
        <span style="margin-bottom: 6px; display: block"><b>Accounting details:</b></span>
        <span style="margin-bottom: 6px; display: block">Total Amount refunded to customer from Stripe Acc: $<%= accounting_details.amount_refunded_from_connected_account %></span>
        <span style="margin-bottom: 6px; display: block">Team Count(s) Disputed: <%= accounting_details.counts_disputed %></span>
        <span style="margin-bottom: 6px; display: block">
            Team(s) Disputed:
            <% for (let i = 0; i < club_details.teams_disputed.length; i++) { %>
                <a href="<%= club_details.teams_disputed[i].link %>" target="_blank"><%= club_details.teams_disputed[i].team_name %></a>,&nbsp;
            <% } %>
        </span>
    </p>
<% } %>
<p style="margin:0;font-size:14px;text-align:center"><span style="font-size:12px;">© SportWrench Inc. <%= current_year %>. All rights reserved.</span></p>
</body>
</html>

