'use strict';

const db                = require('../../api/lib/db');
const DbUtilsService    = require('../../api/lib/db_utils');
const QueryRunner       = require('../../api/lib/db/QueryRunner');
const Tr                = require('../../api/lib/db/Tr');
const Listener          = require('../../api/lib/db/Listener');
const co                = require('co');

describe('db tests', () => {
    let dbPool                      = null;
    const MAX_CONNECTIONS_IN_POOL   = 5;

    function wait(seconds) {
        return new Promise((resolve, reject) => {
            setTimeout(() => resolve(), 1000 * seconds);
        })
    }

    const query04series = {
        text: 'SELECT * from generate_series($1::INTEGER, $2::INTEGER)',
        values: [0, 4],
    };
    const queryEmpty = 'SELECT NULL WHERE FALSE';

    const testQueryResult = async (db, q, bs, r) => {
        const queryResult = await (
            db.queryCursor(q, bs)
        );
        let id = 0;
        for(const expectedResultRows of r) {
            const value = await (queryResult.next().value);
            const expected = new Array(expectedResultRows).fill()
                .map(
                    () => ({generate_series: id++})
                );
            expect(value).to.eql(expected);
        }
        expect(queryResult.next().done).to.be.true;
    };

    beforeEach(() => {
        const DB_CONNECTION = sails.config.connections[sails.config.db.connection];

        // Idle client timeout (ms)
        DB_CONNECTION.idleTimeoutMillis = 2000;

        // Max clients number in the pool
        DB_CONNECTION.max = MAX_CONNECTIONS_IN_POOL;

        dbPool = new db(
            DB_CONNECTION,
            {},
            loggers.errors_log,
            DbUtilsService
        );
    });

    afterEach(() => {
        dbPool.end();
    });

    context('pool connections', () => {

        it('pool #query()', () => {
            let query = 'SELECT pg_sleep(3), 1 "one"';

            return co(function* () {
                // check init pool statistics
                let initPoolStats = dbPool.poolStats();

                // checking pool statistics while query is running
                let [queryResult, workPoolStats] = yield (Promise.all([
                    dbPool.query(query).then(result => result.rows[0] || null),
                    wait(1).then(() => dbPool.poolStats())
                ]));

                // checking pool statistics after query end immediately
                let afterQueryPoolStats = dbPool.poolStats();

                // checking pool statistics 3 seconds after query end
                let endPoolStats = yield (
                    wait(3).then(() => dbPool.poolStats())
                );

                // checking query result
                queryResult.one.should.be.equal(1);

                // checking pool statistics values before client connected
                expect(initPoolStats.total).to.be.equal(0);
                expect(initPoolStats.idle).to.be.equal(0);
                expect(initPoolStats.waiting).to.be.equal(0);
                expect(initPoolStats.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                // checking pool statistics values while query is running
                expect(workPoolStats.total).to.be.equal(1);
                expect(workPoolStats.idle).to.be.equal(0);
                expect(workPoolStats.waiting).to.be.equal(0);
                expect(workPoolStats.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                // checking pool statistics values after the query finished its execution
                expect(afterQueryPoolStats.total).to.be.equal(1);
                expect(afterQueryPoolStats.idle).to.be.equal(1);
                expect(afterQueryPoolStats.waiting).to.be.equal(0);
                expect(afterQueryPoolStats.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                // checking pool statistics values after idle connections timeout
                // exceeded and when no clients connected
                expect(endPoolStats.total).to.be.equal(0);
                expect(endPoolStats.idle).to.be.equal(0);
                expect(endPoolStats.waiting).to.be.equal(0);
                expect(endPoolStats.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
            });
        }).timeout(10000);

        it('pool #queryCursor', async () => {
            await testQueryResult(dbPool, query04series, 6, [5]);
            await testQueryResult(dbPool, query04series, 5, [5, 0]);
            await testQueryResult(dbPool, query04series, 4, [4, 1]);
            await testQueryResult(dbPool, query04series, 3, [3, 2]);
            await testQueryResult(dbPool, query04series, 2, [2, 2, 1]);
            await testQueryResult(dbPool, query04series, 1, [1, 1, 1, 1, 1, 0]);
            await testQueryResult(dbPool, queryEmpty, 2, []);

            await wait(3);

            const poolStatsAfterIdleClosing = dbPool.poolStats();

            // checking pool statistics values after idle connections timeout
            // exceeded and when no clients connected
            expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
        });

        it('pool #queryCursor error', async () => {
            const query = {
                text    : 'SELECT "',
                values  : [],
            };

            const queryResult = dbPool.queryCursor(query, 2);

            // checking query error reject
            await expect(queryResult).to.be.rejectedWith('unterminated quoted identifier at or near """');

            await wait(3);

            const poolStatsAfterIdleClosing = dbPool.poolStats();

            // checking pool statistics values after idle connections timeout
            // exceeded and when no clients connected
            expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
        });

        it('client #query()', () => {
            // checking pool statistics before client created
            let poolStatsBeforeConnection = dbPool.poolStats();

            return dbPool.client().then(client => {
                return co(function* () {

                    // checking client object parent class
                    expect(client).to.be.an.instanceof(QueryRunner);

                    // checking pool statistics after client created
                    let poolStatsAfterConnection = dbPool.poolStats();

                    let queryResult = yield (
                        client.query('SELECT 1 "one"').then(result => result.rows[0] || null)
                    );

                    // checking pool statistics before client released
                    let poolStatsBeforeRelease = dbPool.poolStats();

                    client.release();

                    // checking pool statistics after client released
                    let poolStatsAfterRelease = dbPool.poolStats();

                    yield wait(3);

                    // checking pool statistics 3 seconds after client release
                    let poolStatsAfterIdleClosing = dbPool.poolStats();

                    // checking pool statistics values before client created
                    expect(poolStatsBeforeConnection.total).to.be.equal(0);
                    expect(poolStatsBeforeConnection.idle).to.be.equal(0);
                    expect(poolStatsBeforeConnection.waiting).to.be.equal(0);
                    expect(poolStatsBeforeConnection.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                    // checking pool statistics values after client created
                    expect(poolStatsAfterConnection.total).to.be.equal(1);
                    expect(poolStatsAfterConnection.idle).to.be.equal(0);
                    expect(poolStatsAfterConnection.waiting).to.be.equal(0);
                    expect(poolStatsAfterConnection.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                    // checking pool statistics values before client released
                    expect(poolStatsBeforeRelease.total).to.be.equal(1);
                    expect(poolStatsBeforeRelease.idle).to.be.equal(0);
                    expect(poolStatsBeforeRelease.waiting).to.be.equal(0);
                    expect(poolStatsBeforeRelease.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                    // checking query result
                    expect(queryResult.one).to.be.equal(1);

                    // checking pool statistics values after client released
                    expect(poolStatsAfterRelease.total).to.be.equal(1);
                    expect(poolStatsAfterRelease.idle).to.be.equal(1);
                    expect(poolStatsAfterRelease.waiting).to.be.equal(0);
                    expect(poolStatsAfterRelease.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                    // checking pool statistics values after idle connections timeout
                    // exceeded and when no clients connected
                    expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
                    expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
                    expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
                    expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
                })
            })
        }).timeout(10000);
    });

    context('db transactions error skip', () => {
        let queryRunnerQuerySpy;

        beforeEach(function () {
            queryRunnerQuerySpy     = sinon.spy(QueryRunner.prototype, 'query');
        });

        afterEach(function () {
            QueryRunner.prototype.query.restore();
        });

        it('tr #query() without transaction error skip', () => {
            return co(function* () {
                // Skip "Skip Error" flag setting
                let tr = yield (dbPool.begin());

                // checking Tr super.begin() call
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('BEGIN');

                // Skip error variable should be false
                expect(tr.skipErrAboutCommittedTr).to.be.false;

                // Query with error
                let query = {
                    text    : `SELECT 1 FROM "unknown"`,
                };

                yield Promise.all([
                    tr.query(query.text),
                    tr.query(query.text)
                ]).should.be.rejectedWith(
                'current transaction is aborted, commands ignored until end of transaction block');

                expect(queryRunnerQuerySpy.secondCall.args[0]).to.be.equal(query.text);
                expect(queryRunnerQuerySpy.thirdCall.args[0]).to.be.equal(query.text);

                // checking Tr super.rollback() call
                expect(queryRunnerQuerySpy.lastCall.args[0]).to.be.equal('ROLLBACK');
            })
        });

        it('tr #query() with transaction error skip', () => {
            return co(function* () {
                // Set "Skip Error" flag to TRUE
                let tr = yield (dbPool.begin({skipErrAboutCommittedTr: true}));

                // checking Tr super.begin() call
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('BEGIN');

                // Skip error variable should be TRUE
                expect(tr.skipErrAboutCommittedTr).to.be.equal(true);

                // Query with error
                let query = {
                    text    : `SELECT 1 FROM "unknown"`,
                };

                yield Promise.all([
                    tr.query(query.text),
                    tr.query(query.text)
                ]).should.be.rejectedWith('relation "unknown" does not exist');

                expect(queryRunnerQuerySpy.secondCall.args[0]).to.be.equal(query.text);
                expect(queryRunnerQuerySpy.thirdCall.args[0]).to.be.equal(query.text);

                // checking Tr super.rollback() call
                expect(queryRunnerQuerySpy.lastCall.args[0]).to.be.equal('ROLLBACK');
            })
        });
    });

    context('db transactions', () => {
        let queryRunnerQuerySpy;
        let transactionBeginSpy;
        let transactionCommitSpy;
        let transactionRollbackSpy;

        beforeEach(function () {
            queryRunnerQuerySpy     = sinon.spy(QueryRunner.prototype, 'query');
            transactionBeginSpy     = sinon.spy(Tr.prototype, 'begin');
            transactionCommitSpy    = sinon.spy(Tr.prototype, 'commit');
            transactionRollbackSpy  = sinon.spy(Tr.prototype, 'rollback');
        });

        afterEach(function () {
            QueryRunner.prototype.query.restore();
            Tr.prototype.begin.restore();
            Tr.prototype.commit.restore();
            Tr.prototype.rollback.restore();
        });

        it('tr #begin()', () => {
            return co(function* () {
                let transaction = yield (dbPool.begin());

                // checking transaction object parent class
                expect(transaction).to.be.an.instanceof(QueryRunner);

                // checking transaction object class
                expect(transaction).to.be.an.instanceof(Tr);

                // checking transactionBegin calls number
                expect(transactionBeginSpy.calledOnce).to.be.true;

                // transaction should be started after #begin() and before #commit();
                expect(transaction.isStarted).to.be.true;

                // transaction should not be comitted after #begin() and before #commit();
                expect(transaction.isCommited).to.be.false;

                let poolStatsBeforeTransactionCommitted = dbPool.poolStats();

                // checking pool statistics values before transaction committed
                expect(poolStatsBeforeTransactionCommitted.total).to.be.equal(1);
                expect(poolStatsBeforeTransactionCommitted.idle).to.be.equal(0);
                expect(poolStatsBeforeTransactionCommitted.waiting).to.be.equal(0);
                expect(poolStatsBeforeTransactionCommitted.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                yield (transaction.commit());

                // checking transactionCommit calls number
                expect(transactionCommitSpy.calledOnce).to.be.true;

                // transaction should be started after #commit();
                expect(transaction.isStarted).to.be.true;

                // transaction should be comitted after #commit(;
                expect(transaction.isCommited).to.be.true;

                let poolStatsAfterTransactionCommitted = dbPool.poolStats();

                // checking pool statistics values after transaction committed
                expect(poolStatsAfterTransactionCommitted.total).to.be.equal(1);
                expect(poolStatsAfterTransactionCommitted.idle).to.be.equal(1);
                expect(poolStatsAfterTransactionCommitted.waiting).to.be.equal(0);
                expect(poolStatsAfterTransactionCommitted.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                yield wait(3);

                let poolStatsAfterIdleClosing = dbPool.poolStats();

                // checking pool statistics values after idle connections timeout
                // exceeded and when no clients connected
                expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                // checking Tr super.query() calls
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('BEGIN');
                expect(queryRunnerQuerySpy.secondCall.args[0]).to.be.equal('COMMIT');
            })
        });

        it('tr #query() success', () => {
            return co(function* () {
                let tr = yield (dbPool.begin());

                // checking Tr super.begin() call
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('BEGIN');

                let query = {
                    text    : 'SELECT 1 "one", $1::INTEGER "two"',
                    params  : [2]
                };

                let queryResult = yield (
                    tr.query(query.text, query.params).then(result => result.rows[0] || null)
                );

                // checking Tr super.query() calls
                expect(queryRunnerQuerySpy.secondCall.args[0]).to.be.equal(query.text);
                expect(queryRunnerQuerySpy.secondCall.args[1]).to.be.equal(query.params);

                // checking query results
                expect(queryResult.one).to.be.equal(1);
                expect(queryResult.two).to.be.equal(2);

                yield (tr.commit());

                yield wait(3);

                // checking Tr super.commit() call
                expect(queryRunnerQuerySpy.lastCall.args[0]).to.be.equal('COMMIT');

                let poolStatsAfterIdleClosing = dbPool.poolStats();

                // checking pool statistics values after idle connections timeout
                // exceeded and when no clients connected
                expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
            })
        });

        it('tr #query() error', () => {
            return co(function* () {
                let tr = yield (dbPool.begin());

                // checking Tr super.begin() call
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('BEGIN');

                let query = {
                    text    : 'SELECT 1 "one", $1::INTEGER "two"',
                    params  : []
                };

                // checking query error reject
                yield expect(tr.query(query.text, query.params))
                    .to.be.rejectedWith('there is no parameter $1');

                // transaction should be started and committed after #rollback();
                expect(tr.isStarted).to.be.true;
                expect(tr.isCommited).to.be.true;

                yield wait(3);

                let poolStatsAfterIdleClosing = dbPool.poolStats();

                // checking pool statistics values after idle connections timeout
                // exceeded and when no clients connected
                expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
                expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

                // checking Tr super.query() calls
                expect(queryRunnerQuerySpy.lastCall.args[0]).to.be.equal('ROLLBACK');

                // checking if "Tr.rollback" was called
                expect(transactionRollbackSpy.calledOnce).to.be.true;
            })
        })

        it('tr #queryCursor() success', async () => {
            let tr = await dbPool.begin();

            await testQueryResult(tr, query04series, 6, [5]);
            await testQueryResult(tr, query04series, 5, [5, 0]);
            await testQueryResult(tr, query04series, 4, [4, 1]);
            await testQueryResult(tr, query04series, 3, [3, 2]);
            await testQueryResult(tr, query04series, 2, [2, 2, 1]);
            await testQueryResult(tr, query04series, 1, [1, 1, 1, 1, 1, 0]);
            await testQueryResult(tr, queryEmpty, 2, []);

            await tr.commit();

            await wait(3);

            const poolStatsAfterIdleClosing = dbPool.poolStats();

            // checking pool statistics values after idle connections timeout
            // exceeded and when no clients connected
            expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
        });

        it('tr #queryCursor() error', async () => {
            let tr = await dbPool.begin();

            const query = {
                text    : 'SELECT "',
                values  : [],
            };

            const queryResult = tr.queryCursor(query, 2);

            // checking query error reject
            await expect(queryResult).to.be.rejectedWith('unterminated quoted identifier at or near """');

            // transaction should be started and committed after #rollback();
            expect(tr.isStarted).to.be.true;
            expect(tr.isCommited).to.be.true;

            await wait(3);

            const poolStatsAfterIdleClosing = dbPool.poolStats();

            // checking pool statistics values after idle connections timeout
            // exceeded and when no clients connected
            expect(poolStatsAfterIdleClosing.total).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.idle).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.waiting).to.be.equal(0);
            expect(poolStatsAfterIdleClosing.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);

            // checking if "Tr.rollback" was called
            expect(transactionRollbackSpy.calledOnce).to.be.true;
        });

        context('#inactivityTimeout() getter/setter', () => {

            let tr = null;
            let startRollbackTimeoutStub = null;
            let stopRollbackTimeoutStub = null;

            beforeEach(async () => {
                startRollbackTimeoutStub = 
                                        sinon.stub(Tr.prototype, '_startRollbackTimeout').callsFake(() => {});
                stopRollbackTimeoutStub = 
                                        sinon.stub(Tr.prototype, '_stopRollbackTimeout').callsFake(() => {});

                tr = await dbPool.begin();
            })

            afterEach(async () => {
                await tr.rollback();
                startRollbackTimeoutStub.restore();
                stopRollbackTimeoutStub.restore();
            })

            it('should return null if there\'s no value set', () => {
                const oldConfig = sails;
                sails = undefined; 
                try {
                    tr._inactivityTimeout = null;

                    let res = tr.inactivityTimeout;
                    expect(res).to.be.null;

                    tr._inactivityTimeout = undefined;

                    res = tr.inactivityTimeout;
                    expect(res).to.be.null;
                } finally {
                    sails = oldConfig;
                }
            })

            it('should return this._inactivityTimeout', () => {
                const TEST_VALUE = 1000;
                tr._inactivityTimeout = TEST_VALUE; 

                let res = tr.inactivityTimeout;
                expect(res).to.equal(TEST_VALUE);
            })

            it('should return sails.config.tr.inactivityTimeout', () => {
                const TEST_VALUE = 10000

                let res = tr.inactivityTimeout;
                expect(res).to.equal(sails.config.tr.inactivityTimeout).to.equal(TEST_VALUE);
            })

            it('should set inactivityTimeout', () => {
                const TEST_VALUE = 11111; 
                tr.inactivityTimeout = TEST_VALUE;

                let res = tr.inactivityTimeout;
                expect(res).to.equal(TEST_VALUE);
            })

            it('should set inactivityTimeout and restart the rollback timer', () => {
                const TEST_VALUE = 11111; 

                tr._rollbackTimeoutID = 'testID';

                tr.inactivityTimeout = TEST_VALUE;

                let res = tr.inactivityTimeout;
                expect(res).to.equal(TEST_VALUE);
                // Calls: #begin(), #inactivityTimeout setter
                expect(startRollbackTimeoutStub.callCount).to.equal(2);
            })
        })

        context('tr auto rollback', () => {
            const DEFAULT_TIMEOUT = 1000;

            let tr;
            let startRollbackTimeoutSpy = null;

            before(() => {
                startRollbackTimeoutSpy = sinon.spy(Tr.prototype, '_startRollbackTimeout');
            })

            beforeEach(async () => {
                tr = await dbPool.begin();
                tr.inactivityTimeout = DEFAULT_TIMEOUT;
            });

            afterEach(() => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }

                queryRunnerQuerySpy.resetHistory();
                transactionBeginSpy.resetHistory();
                transactionCommitSpy.resetHistory();
                transactionRollbackSpy.resetHistory();

                startRollbackTimeoutSpy.resetHistory()
            });

            const confirmNotCommited = (tr) => {
                const msg = `shouldn't be commited`;
                expect(tr.isStarted, msg).to.be.true;
                expect(tr.isCommited, msg).to.be.false;
            };

            const confirmCommited = (tr, rollback = true) => {
                const msg = `should be ${rollback?'rolled back':'commited'}`;
                if(rollback) {
                    // expect(queryRunnerQuerySpy.lastCall.args[0], msg).to.be.equal('ROLLBACK');
                    expect(transactionRollbackSpy.calledOnce, msg).to.be.true;
                }
                else {
                    // expect(queryRunnerQuerySpy.lastCall.args[0], msg).to.be.equal('COMMIT');
                    expect(transactionRollbackSpy.called, msg).to.be.false;
                    expect(transactionCommitSpy.calledOnce, msg).to.be.true;
                }
                expect(tr.isStarted, msg).to.be.true;
            };

            it('should be triggered if no queries on a transaction', async () => {
                confirmNotCommited(tr);

                await wait((tr.inactivityTimeout - 100) / 1000);

                confirmNotCommited(tr);

                await wait(0.2);

                confirmCommited(tr);
            });

            it('should log all the queries and trigger autorollback when inactive', async () => {
                let checkLogEntries = (logLength, queryParams, isFinished) => {
                    let index = logLength - 1;

                    expect(tr.log.length).to.equal(logLength);
                    expect(tr.log[index].timestamp).to.be.a.number;
                    expect(tr.log[index].finishedAt).to.be.a(isFinished ? 'number' : 'undefined');
                    expect(tr.log[index].info.query.params).to.deep.equal(queryParams);
                }

                expect(tr.log.length, 'The first entry for the BEGIN statement').to.equal(1);

                let q = tr.query(
                            squel.select().field(squel.str(`NULLIF(?::NUMERIC, 3.14159265)`, 42)));
                checkLogEntries(2, [42]);
                await q;
                checkLogEntries(2, [42], true);

                q = tr.query('SELECT $1::TEXT', ['test']);
                checkLogEntries(3, ['test']);
                await q;
                checkLogEntries(3, ['test'], true);

                await wait((tr.inactivityTimeout - 100) / 1000);

                confirmNotCommited(tr);

                await wait(0.2);

                confirmCommited(tr);
            });

            it('should NOT be triggered if disabled', async () => {
                const oldConfig = sails;
                sails = undefined;
                tr.inactivityTimeout = undefined;

                try {
                    await tr.query('SELECT 1');
                    confirmNotCommited(tr);
                    expect(tr._rollbackTimeoutID).to.be.undefined;

                    await wait(DEFAULT_TIMEOUT * 1.2 / 1000);

                    confirmNotCommited(tr);

                    await tr.rollback();
                    confirmCommited(tr);

                    expect(tr._rollbackTimeoutID).to.be.undefined;
                } finally {
                    sails = oldConfig;
                }
            });

            it('should NOT be triggered during a long running query progress', async () => {
                await tr.query('SELECT pg_sleep($1)', [tr.inactivityTimeout/1000 + 1]);

                confirmNotCommited(tr);

                await wait((tr.inactivityTimeout - 100) / 1000);

                confirmNotCommited(tr);

                await wait(0.2);

                confirmCommited(tr);
                expect(tr._rollbackTimeoutID).to.be.undefined;
            });

            it('should NOT be called on a commited transaction', async () => {
                await tr.commit();
                confirmCommited(tr, false);

                await wait((tr.inactivityTimeout + 100) / 1000);

                expect(transactionRollbackSpy.called).to.be.false;
                // calls: #begin(), #commit()
                expect(startRollbackTimeoutSpy.callCount).to.equal(2);
                expect(tr._rollbackTimeoutID).to.be.undefined;
            });
        });
    });


    context('db listener', () => {
        let queryRunnerQuerySpy;

        beforeEach(function () {
            queryRunnerQuerySpy = sinon.spy(QueryRunner.prototype, 'query');
        });

        afterEach(function () {
            QueryRunner.prototype.query.restore();
        });

        it('db.pool #getListener()', () => {
            return co(function* () {
                let listener = yield (dbPool.getListener());

                // checking transaction object parent class
                expect(listener).to.be.an.instanceof(QueryRunner);

                // checking transaction object class
                expect(listener).to.be.an.instanceof(Listener);

                // checking listeners count
                expect(listener.getListenersLength()).to.be.equal(0);
            })
        });

        it('should successful #add() listener', () => {
            return co(function* () {
                let listener = yield (dbPool.getListener());

                yield (listener.add('test', () => {}));

                // checking listeners count
                expect(listener.getListenersLength()).to.be.equal(1);

                // checking Listener super.query() call argument
                expect(queryRunnerQuerySpy.firstCall.args[0]).to.be.equal('LISTEN test;');

                yield (listener.add('test', () => {}));

                // checking if "QueryRunner.query" was called
                expect(queryRunnerQuerySpy.calledOnce).to.be.true;

                // checking listeners count
                expect(listener.getListenersLength()).to.be.equal(1);

                yield (listener.add('test1', () => {}));

                // checking if "QueryRunner.query" was called
                expect(queryRunnerQuerySpy.calledOnce).to.be.false;

                // checking listeners count
                expect(listener.getListenersLength()).to.be.equal(2);

                let poolStats = dbPool.poolStats();

                // checking pool connection after listener addition
                expect(poolStats.total).to.be.equal(1);
                expect(poolStats.idle).to.be.equal(0);
                expect(poolStats.waiting).to.be.equal(0);
                expect(poolStats.max).to.be.equal(MAX_CONNECTIONS_IN_POOL);
            })
        });

        it('should make successful watcher call', () => {
            let notifyQuery = `select pg_notify('test', '1')`;
            let watcher     = sinon.stub();

            return co(function* () {
                let listener = yield (dbPool.getListener());

                yield (listener.add('test', watcher));

                // checking listeners count
                expect(listener.getListenersLength()).to.be.equal(1);

                yield dbPool.query(notifyQuery);

                // need to wait 1 sec while watcher will triggered
                yield wait(1);

                // checking watcher result
                expect(watcher.calledOnce).to.be.true;
                expect(watcher.firstCall.args[0]).to.be.equal('1');
            })
        })
    })
});
