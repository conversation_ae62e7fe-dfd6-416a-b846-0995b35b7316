'use strict';

const responses = new Map();
const responsesPath = require("path").join(__dirname, '../../api/responses');

require("fs").readdirSync(responsesPath).forEach((file) => {
    responses.set(file.substring(0, file.length-3), require(`${responsesPath}/${file}`));
});
const customRespError = responses.get('customRespError');

function call(err, req, options) {
    return new Promise((resolve, reject) => {
        if(!_.isObject(req)) {
            req = {};
        }
        req._sails = sails;
        req.get = () => undefined;
        const res = {
            statusCode: undefined,
            err: undefined,
            status(status) {
                this.statusCode = status;
                return this;
            },
            sendStatus(status) {
                this.statusCode = status;
                resolve(this);
            },
        };
        const endFunction = function() {
            resolve(this);
        };
        for(const name of ['end', 'download', 'json', 'jsonp', 'redirect', 'render', 'send', 'sendFile', 'view']) {
            res[name] = endFunction;
        }
        for(const [name, response] of responses) {
            res[name] = response.bind({req, res});
        }
        customRespError.call({req, res}, err, options);
    });
}

describe('customRespError', function () {
    let errorLoggerSpy;
    before(function () {
        errorLoggerSpy = sinon.spy(loggers.errors_log, 'error');
    });

    it('should return 500 response when called without arguments', async function () {
        const res = await call();
        expect(res.statusCode).to.equal(500);
        expect(errorLoggerSpy.called).to.be.true;
    });

    it('should return 500 response when called with Error class instance', async function () {
        const res = await call(new Error());
        expect(res.statusCode).to.equal(500);
        expect(errorLoggerSpy.callCount).to.equal(1);
    });

    it('should return 500 response when called with Error subclass instance', async function () {
        const res = await call(new SubError());
        expect(res.statusCode).to.equal(500);
        expect(errorLoggerSpy.callCount).to.equal(1);
    });

    it('should return 400 response when called with validation error', async function () {
        const res = await call({validation: 'invalid input'});
        expect(res.statusCode).to.equal(400);
        expect(errorLoggerSpy.callCount).to.equal(0);
    });

    it('should return 400 response when called with validation errors', async function () {
        const res = await call({validationErrors: [{message: 'invalid input', path: ['field']}]});
        expect(res.statusCode).to.equal(400);
        expect(errorLoggerSpy.callCount).to.equal(0);
    });

    it('should return response with passed status code in 2-nd parameter', async function () {
        const res = await call(new Error(), {}, {status: 418});
        expect(res.statusCode).to.equal(418);
        expect(errorLoggerSpy.callCount).to.equal(1);
    });

    context('sensitive data', function() {
        it('should only change data in copy of request data', async function() {
            const req = {
                body: {
                    form: {
                        data: {
                            password: 'secret',
                            extra: [
                                {
                                    password2: 'secret2',
                                },
                            ],
                        },
                        meta: {
                            timestamp: new Date(),
                            test: 'value',
                        },
                    },
                },
            };
            const res = await call(new Error(), req);
            expect(req.body.form.data.password).to.equal('secret');
            expect(req.body.form.data.extra[0].password2).to.equal('secret2');
            expect(req.body.form.meta.test).to.equal('value');
            expect(errorLoggerSpy.lastCall.args[1].body.form.data.password).to.not.equal('secret');
            expect(errorLoggerSpy.lastCall.args[1].body.form.data.extra[0].password2).to.not.equal('secret2');
            expect(errorLoggerSpy.lastCall.args[1].body.form.meta.test).to.equal('value');
        })
    });

    after(function () {
        errorLoggerSpy.restore();
    });

    afterEach(function () {
        errorLoggerSpy.resetHistory();
    });
});

class SubError extends Error {

}
