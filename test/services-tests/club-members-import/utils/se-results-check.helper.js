const moment = require('moment');


class SeResultsCheckHelper {
    constructor(SEUtils) {
        this.SEUtils = SEUtils;
    }

    checkMembersCounts(membersFromImport, clubAthletes, clubStaffers) {
        const staffersInResponse = this.#extractStaffersFromImportData(membersFromImport);
        const athletesInResponse = this.#extractAthletesFromImportData(membersFromImport);

        const clubUSAVAthletes = this.#extractUSAVMembers(clubAthletes);
        const clubUSAVStaffers = this.#extractUSAVMembers(clubStaffers);

        this.#checkEquality(clubUSAVAthletes.length, athletesInResponse.length);
        this.#checkEquality(clubUSAVStaffers.length, staffersInResponse.length);
    }

    checkStaffers(membersFromAPI, membersFromDB) {
        this.#checkMembers(membersFromAPI, membersFromDB, this.SEUtils.MEMBER_TYPE.STAFF);

    }

    checkAthletes(membersFromAPI, membersFromDB) {
        this.#checkMembers(membersFromAPI, membersFromDB, this.SEUtils.MEMBER_TYPE.ATHLETE);
    }

    #checkMembers(membersFromAPI, membersFromDB, memberType) {
        for(const memberFromDB of membersFromDB) {
            const [memberFromAPI] = this.#findMemberFromAPI(membersFromAPI, memberFromDB);

            if(!_.isEmpty(memberFromAPI)) {
                this.#checkMemberData(memberType, memberFromAPI, memberFromDB);
            }
        }
    }

    #findMemberFromAPI(membersFromAPI, memberFromDB) {
        return membersFromAPI.filter(
            member => member[this.SEUtils.SE_FIELDS.USAV_CODE] === memberFromDB.usav_number
        );
    }

    #checkMemberData(memberType, memberFromAPI, memberFromDB) {
        const FIELD_NAMES = this.SEUtils.SE_FIELDS;

        const isEligible =
            member[this.SEUtils.SE_FIELDS.MEMBERSHIP_STATUS] === this.SEUtils.ELIGIBLE_MEMBER_STATUS;

        this.#checkEquality(memberFromDB.first.trim().toLowerCase(), memberFromAPI[FIELD_NAMES.FIRST].trim().toLowerCase());
        this.#checkEquality(memberFromDB.last.trim().toLowerCase(), memberFromAPI[FIELD_NAMES.LAST].trim().toLowerCase());

        this.#checkEquality(
            moment(memberFromDB.birthdate).format('YYYY-MM-DD'),
            moment(memberFromAPI[FIELD_NAMES.BIRTHDATE]).format('YYYY-MM-DD')
        )

        this.#checkEquality(memberFromDB.gender, this.SEUtils._prepareGenderValue[memberFromAPI[FIELD_NAMES.GENDER]]);
        this.#checkEquality(memberFromDB.season, sails.config.sw_season.current);
        this.#checkEquality(memberFromDB.organization_code, memberFromAPI[FIELD_NAMES.ORGANIZATION_CODE]);
        this.#checkEquality(memberFromDB.usav_number, memberFromAPI[FIELD_NAMES.USAV_CODE]);
        this.#checkEquality(memberFromDB.membership_status, memberFromAPI[FIELD_NAMES.MEMBERSHIP_STATUS]);
        this.#checkEquality(memberFromDB.safesport_statusid, isEligible
            ? this.SEUtils.VALID_NUMERIC_STATUS
            : this.SEUtils.INVALID_NUMERIC_STATUS);
        this.#checkEquality(memberFromDB.safesport_start_date, memberFromAPI[FIELD_NAMES.MEMBERSHIP_START_DATE]);
        this.#checkEquality(memberFromDB.safesport_end_date, memberFromAPI[FIELD_NAMES.MEMBERSHIP_END_DATE]);

        if(memberType === this.SEUtils.MEMBER_TYPE.STAFF) {
            this.#checkEquality(memberFromDB.seasonality, memberFromAPI[FIELD_NAMES.MEMBERSHIP_END_DATE]);
            this.#checkEquality(memberFromDB.age, this.SEUtils.getMinAge(memberFromAPI[FIELD_NAMES.BIRTHDATE]));
        }

        if(memberType === this.SEUtils.MEMBER_TYPE.ATHLETE) {
            this.#checkEquality(memberFromDB.is_impact, isEligible);
            this.#checkEquality(memberFromDB.bg_screening, isEligible
                ? this.SEUtils.VALID_NUMERIC_STATUS
                : this.SEUtils.INVALID_NUMERIC_STATUS
            );
            this.#checkEquality(memberFromDB.bg_expire_date, memberFromAPI[FIELD_NAMES.MEMBERSHIP_END_DATE]);
        }
    }

    #checkEquality(actualValue, expectedValue) {
        expect(actualValue).to.be.equal(expectedValue);
    }

    #extractStaffersFromImportData(membersFromImport) {
        return this.#extractMembersFromImportData(membersFromImport, this.SEUtils.MEMBER_TYPE.STAFF);
    }

    #extractAthletesFromImportData(membersFromImport) {
        return this.#extractMembersFromImportData(membersFromImport, this.SEUtils.MEMBER_TYPE.ATHLETE);
    }

    #extractMembersFromImportData(membersFromImport, memberType) {
        return Object.values(membersFromImport).filter(member => !_.isEmpty(member[memberType]));
    }

    #extractUSAVMembers(members) {
        return members.filter(member => !!member.usav_number);
    }
}

module.exports = SeResultsCheckHelper;
