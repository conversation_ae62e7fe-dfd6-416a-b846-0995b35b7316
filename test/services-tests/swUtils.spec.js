'use strict';

function generateCtrlCharsStr () {
	let characters = [];

	for (let i = 0; i <= 31; ++i) {
		characters.push(String.fromCharCode(i));
	}

	for (let i = 127; i <= 159; ++i) {
		characters.push(String.fromCharCode(i));
	}

	return characters.join('');
}

describe('swUtils', function () {

	let service;

	before(() => {
		service = require('../../api/lib/swUtils');
	});

	context('splitArray()', () => {

		let absSpy, ceilSpy, sliceSpy;

		beforeEach(function () {
			absSpy 		= sinon.spy(Math, 'abs');
			ceilSpy 	= sinon.spy(Math, 'ceil');
			sliceSpy 	= sinon.spy(Array.prototype, 'slice');
		})

		afterEach(function () {
			Math.abs.restore();
			Math.ceil.restore();
			Array.prototype.slice.restore();
		})

		it('should throw error on non-array argument', () => {
			let fn = service.splitArray.bind(service, 0);

			expect(fn).to.throw(Error, new RegExp('Expecting first argument to be an array'));

			expect(absSpy.callCount).to.be.equal(0);
			expect(ceilSpy.callCount).to.be.equal(0);
			expect(sliceSpy.callCount).to.be.equal(0);
		})

		it('should return empty array if empty array passed', () => {
			let result = service.splitArray([]);

			expect(result).to.be.an.instanceof(Array);
			expect(result.length).to.be.equal(0);

			expect(absSpy.callCount).to.be.equal(0);
			expect(ceilSpy.callCount).to.be.equal(0);
			expect(sliceSpy.callCount).to.be.equal(0);
		})

		it('should split array into sub-arrays', () => {
			let inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];

			let result = service.splitArray(inputArray);

			expect(result).to.be.an.instanceof(Array);
			expect(result.length).to.be.equal(3);

			// NOTE: 5 is the default chunk size
			expect(result[0].length).to.be.equal(5);
			expect(result[1].length).to.be.equal(5);
			expect(result[2].length).to.be.equal(1);

			expect(absSpy.calledOnce).to.be.true;
			expect(ceilSpy.calledOnce).to.be.true;
			expect(sliceSpy.callCount).to.be.equal(3);
		})


		it('should use custom chunk size', () => {
			let inputArray 	= [1, 2, 3, 4];
			let chunkSize 	= 3;

			let result = service.splitArray(inputArray, chunkSize);

			expect(result).to.be.an.instanceof(Array);
			expect(result.length).to.be.equal(2);

			expect(result[0].length).to.be.equal(3);
			expect(result[1].length).to.be.equal(1);

			expect(absSpy.calledOnce).to.be.true;
			expect(ceilSpy.calledOnce).to.be.true;
			expect(sliceSpy.callCount).to.be.equal(2);
		})
	})

	context('maskString()', () => {

		let substringSpy, joinSpy

		beforeEach(function () {
			substringSpy 	= sinon.spy(String.prototype, 'substring')
			joinSpy 		= sinon.spy(Array.prototype, 'join')
		})

		afterEach(function () {
			substringSpy.restore();
			joinSpy.restore();
		})

		it('should throw exception on non-string argument', () => {
			let fn = service.maskString.bind(service);

			expect(fn).to.throw(Error, new RegExp('Not a string passed'));

			expect(substringSpy.callCount).to.be.equal(0);
			expect(joinSpy.callCount).to.be.equal(0);
		})

		it('should throw exception on too short string passed', () => {
			let fn = service.maskString.bind(service, '1');

			expect(fn).to.throw(Error, new RegExp('Minimum string legnth is 14'));

			expect(substringSpy.callCount).to.be.equal(0);
			expect(joinSpy.callCount).to.be.equal(0);
		})

		it('should mask string', () => {
			let result = service.maskString('abcdefghijklmn');

			expect(result).to.be.equal('abcdefghij*lmn');

			expect(substringSpy.callCount).to.be.equal(2);
			expect(joinSpy.callCount).to.be.equal(1);
		})

	})

	context('numArrayToString()', () => {

		it('should return empty string for empty array', () => {
			let res = service.numArrayToString([]);

			expect(res).to.equal('');
		})

		it('should return string if "property" is not specified', () => {
			let res = service.numArrayToString([1, 2, 3, 4]);

			expect(res).to.equal('1, 2, 3, 4');
		})

		it('should return string if "property" is specified', () => {
			let res = service.numArrayToString([{ test: 1 }, { test: 2 }, { test: 3 }], 'test');

			expect(res).to.equal('1, 2, 3');
		})

		it('should try to parse integers and skip invalid', () => {
			let res = service.numArrayToString(['1', '2', '3', 'test', '4']);

			expect(res).to.equal('1, 2, 3, 4');

		})

		it('should try to parse in and raise exception if raising is allowed', () => {
			let fn = service.numArrayToString.bind(service, ['1', '2', '3', 'test', '4'], null, true);

			expect(fn).to.throw(Error, /All Items should be positive integers/);
		})

	})

	context('escapeStr()', () => {

		it('should throw error if passed value is not type of "string"', () => {
			let fn = service.escapeStr.bind(service, 123);

			expect(fn).to.throw({ message: 'Expecting first argument to be a string' });
		})

		it('should return zero-length string if zero-length string passed', () => {
			let result = service.escapeStr('');

			expect(result).to.be.a('string');
			expect(result.length).to.equal(0);
		})

		it('should remove control symbols from a string', () => {
			let input = `${generateCtrlCharsStr()}\u001C test!`;

			let result = service.escapeStr(input);

			expect(result).to.be.a('string');
			expect(result.length).to.equal(5);
			expect(result).to.equal('test!');
		})

		it('should leave control sumbols', () => {
			let input = generateCtrlCharsStr();

			let result = service.escapeStr(input, {
				ctrl 	: false,
				spaces 	: false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(input.length);
			expect(result).to.equal(input);
		})

		it('should remove HTML symbol codes from a string', () => {
			let input = '&#0;test&#000000;;;123';

			let result = service.escapeStr(input);

			expect(result).to.be.a('string');
			expect(result.length).to.equal(11);
			expect(result).to.equal('test0;;;123');
		})

		it('should leave HTML symbol codes', () => {
			let input = '&#0&#000000;';

			let result = service.escapeStr(input, {
				html: false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(input.length);
			expect(result).to.equal(input);
		})

		it('should remove spaces from the strart and the end of a string', () => {
			let input = '   test!   ';

			let result = service.escapeStr(input);

			expect(result).to.be.a('string');
			expect(result.length).to.equal(5);
			expect(result).to.equal('test!');
		})

		it('should leave spaces at start and end of a string', () => {
			let input = ' test! ';

			let result = service.escapeStr(input, {
				trim: false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(input.length);
			expect(result).to.equal(input);
		})

		it('should collapse multiple multiple whitespaces and tabs to one', () => {
			let input = 'test   test \t  test\t\t\ttest';

			let result = service.escapeStr(input, {
				/* by default, this removes all tabs, so we turn this off */
				ctrl: false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(19);
			expect(result).to.equal('test test test test');
		})

		it('should leave multiple whitespaces and tabs', () => {
			let input = 'test   test\t\t test';

			let result = service.escapeStr(input, {
				ctrl 	: false,
				spaces  : false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(input.length);
			expect(result).to.equal(input);
		})

		it('should remove escaped hex symbols', () => {
			let input = '\\u00a0\\u00a0\\u00a0\\u0078\\u00EF';

			let result = service.escapeStr(input);

			expect(result).to.be.a('string');
			expect(result.length).to.equal(0);
			expect(result).to.equal('');
		})

		it('should leave escaped hex symbols', () => {
			let input = '\\u00a0\\u00a0\\u00a0\\u0078\\u00EF';

			let result = service.escapeStr(input, {
				escHex: false
			});

			expect(result).to.be.a('string');
			expect(result.length).to.equal(input.length);
			expect(result).to.equal(input);
		})
	})

    context('getMinCurrentSeasonEventID()', () => {

        it('should return minimum ID of events in the current season', () => {
            let year = service.getMinCurrentSeasonEventID();

            expect(year).to.equal(25000);
        })
    })

    context('isSessionDisabled()', () => {
        const routesDisabled = [
            '/api/swb/*',
            'GET /api/tickets/events',
            'GET /api/tickets/events/:event',
            'GET /api/ticket-entry-code/event/:event/code/:code/validation',
        ];
        const routesParseData = [
            { regExp: /\*/g, patternData: '.*' }, //for *
            { regExp: /:(.*?)($|\/)/g, patternData: '(.*?)($|\/)' }, //for :variable
        ];
        const reqArr = [
            { path: '/api/swb/create', method: 'POST' },
            { path: '/api/tickets/events', method: 'GET' },
            { path: '/api/tickets/events/22041', method: 'GET' },
            { path: '/api/ticket-entry-code/event/22041/code/c49146e5bbdd4ddbb4/validation', method: 'GET' }
        ];

        it('should return "true" session disabled statuses for all resquests', () => {
            const routesDisabledParsedArr = service.parseRoutesArr(routesDisabled, routesParseData);

            for (let i = 0; i < reqArr.length; i++) {
                expect(service.isSessionDisabled(reqArr[i], routesDisabledParsedArr)).to.equal(true);
            }
        })
    })
})
