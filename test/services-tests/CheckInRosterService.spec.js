'use strict';

describe('CheckInRosterService', function () {
	let service;

	before(() => {
		service = sails.services.checkinrosterservice;
	})
	
	context('validateAthletesJerseys()', function () {

		let countDups = teams => {
			return teams.reduce((sum, team) => {
				return sum + team.members.filter(member => member.jersey_duplicate).length
			}, 0)
		}

		it('should find jersey duplicates among athletes', () => {
			let teams = [
				{ 
					members: [
						{ role: 'athlete' 	, jersey: 'Test' },
						{ role: 'athlete' 	, jersey: 'Test' },
						{ role: 'staff' 	, jersey: null }
					]
				}
			];


			let result = service.validateAthletesJerseys(teams);

			expect(result).to.eql(teams);

			let dupJerseyQty = countDups(result);

			expect(dupJerseyQty).to.equal(2);
		})

		it('should ignore invalid jerseys for athletes "as staff"', () => {
			let teams = [
				{ 
					members: [
						{ role: 'athlete' 	, jersey: 'Test' },
						{ role: 'staff' 	, jersey: 'Test' },
						{ role: 'staff' 	, jersey: null }
					]
				}
			];

			let result = service.validateAthletesJerseys(teams);

			expect(result).to.eql(teams);

			let dupJerseyQty = countDups(result);

			expect(dupJerseyQty).to.equal(0);
		})

	})

})