{"stripe_charge_id": "ch_test", "amount": 780, "fee": 20, "balance_transaction": {"id": "txn_19Tvfd2Yt0RbUG0qxVTXVkS1", "fee": 1209, "net": 56291, "type": "charge", "amount": 57500, "object": "balance_transaction", "source": "ch_test", "status": "pending", "created": **********, "currency": "usd", "description": "Test", "fee_details": [{"type": "stripe_fee", "amount": 1209, "currency": "usd", "application": null, "description": "Stripe processing fees"}], "available_on": **********, "sourced_transfers": {"url": "/v1/transfers?source_transaction=ch_test", "data": [{"id": "tr_19Tvfc2Yt0RbUG0q6oJ7NUao", "date": **********, "type": "stripe_account", "amount": 57500, "method": "standard", "object": "transfer", "status": "paid", "created": **********, "currency": "usd", "livemode": true, "metadata": {}, "reversed": false, "recipient": null, "reversals": {"url": "/v1/transfers/tr_19Tvfc2Yt0RbUG0q6oJ7NUao/reversals", "data": [], "object": "list", "has_more": false, "total_count": 0}, "description": null, "destination": "acct_14YhOtIE7LDjm4AE", "source_type": "card", "failure_code": null, "amount_reversed": 0, "application_fee": null, "failure_message": null, "source_transaction": "ch_test", "balance_transaction": "txn_19Tvfc2Yt0RbUG0qjJIt5yRT", "destination_payment": "py_19TvfcIE7LDjm4AEFR6LTSoa", "statement_descriptor": null}], "object": "list", "has_more": false, "total_count": 1}}, "collected_fee": 25, "stripe_payment_id": "py_test", "stripe_account_id": "act_test", "type": "connect"}