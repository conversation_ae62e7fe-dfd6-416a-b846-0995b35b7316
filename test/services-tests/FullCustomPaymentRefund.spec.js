'use strict';

const customPaymentRows = require('./fixture/EventCustomPaymentRefundService/custom-payment.rows.json');
const mockPayment = require('./fixture/EventCustomPaymentRefundService/payment.mock.json');

const EventPaymentMethodService = require('../../api/services/EventPaymentMethodService');
const FullCustomPaymentRefund = require('../../api/services/custom-payment/refund/_FullCustomPaymentRefund');

const { CUSTOM_PAYMENT: { PAYMENT_STATUS } } = require('../../api/constants/payments');

describe('FullCustomPaymentRefund', function () {
    let service;

    before(() => {
        service = new FullCustomPaymentRefund(mockPayment, 'dashboard');
        return insertWithIdentity('custom_payment', customPaymentRows);
    });

    after(() => {
        return Db.query(
            `DELETE FROM "custom_payment" WHERE "custom_payment_id" IN (${customPaymentRows
                .map((row) => row.custom_payment_id)
                .join(', ')});`
        );
    });

    describe('#constructor', () => {
        it('should throw error', () => {
            expect(() => new FullCustomPaymentRefund()).to.throw(
                Error,
                'Payment is empty'
            );
            expect(() => new FullCustomPaymentRefund({})).to.throw(
                Error,
                'Payment is empty'
            );
            expect(() => new FullCustomPaymentRefund(mockPayment)).to.throw(
                Error,
                'Refund source is empty'
            );
        });
        it('should init service', () => {
            const source = 'dashboard'
            const fullCustomPaymentRefund = new FullCustomPaymentRefund(
                mockPayment,
                source
            );

            expect(
                fullCustomPaymentRefund
            ).to.be.an.instanceof(FullCustomPaymentRefund);

            expect(fullCustomPaymentRefund.payment).to.be.eql(mockPayment);
            expect(fullCustomPaymentRefund.source).to.be.eql(source);
        });
    });

    describe('__prepareCustomPaymentData', () => {
        it('should return custom payment', () => {
            const customPayment = service.__prepareCustomPaymentData();
            expect(customPayment.amount).to.be.a('number').eql(0);
            expect(customPayment.merchantFee).to.be.a('number').eql(0);
            expect(customPayment.netProfit).to.be.a('number').eql(0);
            expect(customPayment.status).to.be.a('string').eql(PAYMENT_STATUS.CANCELED);
        });
    });

    describe('__updateCustomPaymentRow', () => {
        let paymentPropertyStub;

        const updatedCustomPayment = {
            amount: 100,
            merchantFee: 100,
            netProfit: 100,
            status: PAYMENT_STATUS.PAID,
        };

        afterEach(() => {
            paymentPropertyStub.restore();
        });

        it('should update custom payment', async () => {
            paymentPropertyStub = sinon
                .stub(service, 'payment')
                .value(mockPayment);

            await service.__updateCustomPaymentRow(updatedCustomPayment);

            const customPayment = await getCustomPayment(
                mockPayment.purchase_id
            );

            expect(customPayment).not.to.be.a('null');

            expect(Number(customPayment.amount)).to.be.equal(
                updatedCustomPayment.amount
            );
            expect(Number(customPayment.merchant_fee)).to.be.equal(
                updatedCustomPayment.merchantFee
            );
            expect(Number(customPayment.net_profit)).to.be.equal(
                updatedCustomPayment.netProfit
            );
            expect(customPayment.status).eql(updatedCustomPayment.status);
        });

        it('should not update custom payment', async () => {
            // stub with id that does not exist
            paymentPropertyStub = sinon
                .stub(service, 'payment')
                .value({ ...mockPayment, purchase_id: 0 });

            await expect(
                service.__updateCustomPaymentRow(updatedCustomPayment)
            ).to.be.rejectedWith('Custom payment row not updated');
        });
    });

    describe('__proceedStripeDashboardRefund', () => {
        let __prepareCustomPaymentDataSpy;
        let paymentPropertyStub;
        let __updateCustomPaymentRowSpy;

        before(() => {
            paymentPropertyStub = sinon
                .stub(service, 'payment')
                .value(mockPayment);
            __prepareCustomPaymentDataSpy = sinon.spy(
                service,
                '__prepareCustomPaymentData'
            );
            __updateCustomPaymentRowSpy = sinon.spy(
                service,
                '__updateCustomPaymentRow'
            );
        });

        after(() => {
            paymentPropertyStub.restore();
            __prepareCustomPaymentDataSpy.restore();
            __updateCustomPaymentRowSpy.restore();
        });

        it('should update custom payment', async () => {
            await service.__proceedStripeDashboardRefund();

            const updatedCustomPayment =
                __prepareCustomPaymentDataSpy.returnValues[0];

            expect(__prepareCustomPaymentDataSpy.calledOnce).to.be.true;

            expect(__updateCustomPaymentRowSpy.calledOnce).to.be.true;
            expect(__updateCustomPaymentRowSpy.calledWith(updatedCustomPayment))
                .to.be.true;

            const customPayment = await getCustomPayment(
                mockPayment.purchase_id
            );

            expect(customPayment).not.to.be.a('null');

            expect(Number(customPayment.amount)).to.be.equal(
                updatedCustomPayment.amount
            );
            expect(Number(customPayment.merchant_fee)).to.be.equal(
                updatedCustomPayment.merchantFee
            );
            expect(Number(customPayment.net_profit)).to.be.equal(
                updatedCustomPayment.netProfit
            );
            expect(customPayment.status).eql(updatedCustomPayment.status);
        });
    });

    describe('proceed', () => {
        let sourcePropertyStub;
        let __proceedStripeDashboardRefundSpy;

        before(() => {
            __proceedStripeDashboardRefundSpy = sinon.spy(
                service,
                '__proceedStripeDashboardRefund'
            );
        });

        afterEach(() => {
            sourcePropertyStub.restore();
            __proceedStripeDashboardRefundSpy.restore();
        });

        it('should proceed the refund', async () => {
            sourcePropertyStub = sinon
                .stub(service, 'source')
                .value('dashboard');

            await service.proceed();

            expect(__proceedStripeDashboardRefundSpy.calledOnce).to.be.true;
        });

        it('should throw error for wrong source', async () => {
            sourcePropertyStub = sinon.stub(service, 'source').value('');

            await expect(service.proceed()).to.be.rejectedWith(
                'Refund source not supported'
            );
        });
    });
});

function insertWithIdentity(tableName, data) {
    const columns = Object.keys(data[0]).join(', ');
    const values = data.map((row) => {
        const sqlValues = Object.values(row)
            .map((value) =>
                value === null
                    ? 'null'
                    : typeof value === 'string'
                    ? `'${value}'`
                    : value
            )
            .join(', ');
        return '( ' + sqlValues + ' )';
    });
    return Db.query(`
        INSERT INTO ${tableName} ( ${columns} )
        OVERRIDING SYSTEM VALUE 
            VALUES ${values.join(', ')};
    `);
}

function getCustomPayment(custom_payment_id) {
    return Db.query(
        knex('custom_payment as cp')
            .select(['amount', 'merchant_fee', 'net_profit', 'status'])
            .where('cp.custom_payment_id', custom_payment_id)
    ).then(({ rows }) => rows[0] || null);
}
