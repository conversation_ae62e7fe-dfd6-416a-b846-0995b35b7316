{"created": 1326853478, "livemode": false, "id": "evt_00000000000000", "type": "charge.dispute.closed", "object": "event", "request": null, "pending_webhooks": 1, "api_version": "2016-02-03", "data": {"object": {"id": "dp_00000000000000", "object": "dispute", "amount": 8000, "balance_transactions": [{"id": "txn_17kvhAEtHbmUxNyGx2vIeqrJ", "object": "balance_transaction", "amount": -8000, "available_on": 1457568000, "created": 1457008604, "currency": "usd", "description": "Chargeback withdrawal for ch_17kvh9EtHbmUxNyG9k5b3ePP", "fee": 1500, "fee_details": [{"amount": 1500, "application": null, "currency": "usd", "description": "Dispute fee", "type": "stripe_fee"}], "net": -9500, "source": "dp_17kvh9EtHbmUxNyGZMzwu9Zp", "sourced_transfers": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/transfers?source_transaction=ad_17kvhAEtHbmUxNyGQCHgxhVl"}, "status": "available", "type": "adjustment"}], "charge": "ch_00000000000000", "created": 1457008603, "currency": "usd", "evidence": {"access_activity_log": null, "billing_address": "0000", "cancellation_policy": null, "cancellation_policy_disclosure": null, "cancellation_rebuttal": null, "customer_communication": null, "customer_email_address": null, "customer_name": "Test User", "customer_purchase_ip": "**************", "customer_signature": null, "duplicate_charge_documentation": null, "duplicate_charge_explanation": null, "duplicate_charge_id": null, "product_description": null, "receipt": null, "refund_policy": null, "refund_policy_disclosure": null, "refund_refusal_explanation": null, "service_date": null, "service_documentation": null, "shipping_address": null, "shipping_carrier": null, "shipping_date": null, "shipping_documentation": null, "shipping_tracking_number": null, "uncategorized_file": null, "uncategorized_text": "Here is some evidence"}, "evidence_details": {"due_by": 1458431999, "has_evidence": false, "past_due": false, "submission_count": 0}, "is_charge_refundable": false, "livemode": false, "metadata": {}, "reason": "fraudulent", "status": "lost"}}}