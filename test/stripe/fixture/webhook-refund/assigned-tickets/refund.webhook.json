{"id": "evt_1EwqZSJyPh92VEwc92uCt2FL", "object": "event", "api_version": "2017-08-15", "created": **********, "data": {"object": {"id": "ch_1EwpDM2Yt0RbUG0qWWWc4p3b", "object": "charge", "amount": 200, "amount_refunded": 200, "application": "ca_6BluQhjxGw39pnci70csHwmx7Jp52tf2", "application_fee": null, "application_fee_amount": 455, "balance_transaction": "txn_1EwpDMJyPh92VEwca46UD1CK", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": null, "name": null, "phone": null}, "captured": true, "created": 1563277980, "currency": "usd", "customer": null, "description": "Test assigned tickets event, <NAME_EMAIL> 11231231231  Total: $2", "destination": 1, "dispute": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"purchase_id": "115621", "barcode": "*********, *********", "email": "<EMAIL>", "phone": "11231231231", "event_name": "Test assigned tickets event", "tickets": "Daily Passes * 1 ($1); Daily Passes * 1 ($1); ", "cardholder": "wdwd dwwd", "total": "$-0.55", "stripe_fee": "$0.35", "additional_fee": "$2.00", "sw_fee": "$2.20", "ticket_holders": [{"barcode": *********, "price": 1, "quantity": 1, "event_ticket_id": 208}, {"barcode": *********, "price": 1, "quantity": 1, "event_ticket_id": 208}]}, "on_behalf_of": null, "order": null, "outcome": null, "paid": true, "payment_intent": null, "payment_method": null, "payment_method_details": {"stripe_account": {}, "type": "stripe_account"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_1BTtqQJyPh92VEwc/py_1EwpDMJyPh92VEwckXYPze1U/rcpt_FRieIjjmBrVEBKnKzsvmYQ93p8P8z5O", "refunded": true, "refunds": {"object": "list", "data": [{"id": "pyr_1EwqZRJyPh92VEwcVasYJF80", "object": "refund", "amount": 200, "balance_transaction": "txn_1EwqZRJyPh92VEwc8OanLMzb", "charge": "py_1EwpDMJyPh92VEwckXYPze1U", "created": **********, "currency": "usd", "metadata": {}, "reason": "requested_by_customer", "receipt_number": null, "source_transfer_reversal": null, "status": "succeeded", "transfer_reversal": null}], "has_more": false, "total_count": 1, "url": "/v1/charges/py_1EwpDMJyPh92VEwckXYPze1U/refunds"}, "review": null, "shipping": null, "source": {"id": "acct_102orH2Yt0RbUG0q", "object": "account", "application_icon": "https://s3.amazonaws.com/stripe-uploads/acct_102orH2Yt0RbUG0qapplication-icon-sw-icon-notext.png", "application_logo": "https://s3.amazonaws.com/stripe-uploads/acct_102orH2Yt0RbUG0qapplication-logo-sw-icon-textright.png", "application_name": "SportWrench.com", "application_url": "https://sportwrench.com/"}, "source_transfer": null, "statement_descriptor": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}, "previous_attributes": {"amount_refunded": 0, "refunded": false, "refunds": {"data": [], "total_count": 0}}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_caKT4wMYEQdo9E", "idempotency_key": null}, "type": "charge.refunded"}