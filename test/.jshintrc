{
  // Details: https://github.com/victorporof/Sublime-JSHint#using-your-own-jshintrc-options
  // Example: https://github.com/jshint/jshint/blob/master/examples/.jshintrc
  // Documentation: http://www.jshint.com/docs/options/
  "browser": true,
  "globals": {
    "db": true,
    "Db": true,
    "expect": true,
    "HOST": true,
    "squel": true,
    "sails": true,
    "sinon": true,
    "_": true,
    "toString": true
  },
  "globalstrict": false,
  "quotmark": false,
  "undef": true,
  "unused": true,
  "asi": true,
  "sub": true,
  "multistr": true,
  "eqeqeq": true,
  "freeze": true,
  "maxlen": 120,
  "eqnull": true,
  "funcscope": true,
  "laxbreak": true,
  "laxcomma": true,
  "-W086" : true,
  "newcap": false,
  "node": true,
  "mocha": true,
  "expr": true,
  "varstmt": true,
  "esversion": 9
}
