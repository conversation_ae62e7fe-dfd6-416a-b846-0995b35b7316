const request = require('request-promise');
const EventData = require('../services/EventData');
const UserSignin = require('../ticket-refunds/user-signin');

const users = require('../ticket-refunds/fixture/user');

describe('POST /api/event/:event/ticket/:code/payment/void', function () {
    /**
     * @type {EventData}
     */
    let eventData;
    let event_id;
    /**
     * @type {UserSignin}
     */
    let user;
    let cookies;

    before(async function () {
        user = await UserSignin.create(users[0]);
    });

    after(async function() {
        if(user) {
            await user.del();
            user = null;
        }
    });

    async function init({ event, data }) {
        eventData = await EventData.create({ event, data });
        event_id = eventData.event.event_id;
        cookies = await user.signIn();
        await user.assignToEvent(event_id);
    }

    afterEach(async function () {
        if(eventData) {
            await eventData.cleanup();
            eventData = null;
        }
    });

    it('cash ticket', async function () {
        await init(require('./fixture/ticket-void/cash-ticket'));
        const [ticket1, ticket2, purchase] = eventData.getTableRows('purchase');
        await sendRequest(ticket1.ticket_barcode);
        await checkPurchase(purchase.purchase_id, false);
        await checkPurchase(ticket1.purchase_id);
        await checkPurchase(ticket2.purchase_id, false);
        await checkPurchaseTicket(ticket1.purchase_id);
        let purchaseHistory = await findPurchaseHistoryRecords(purchase, ticket1, ticket2);
        await checkCancellationHistory(purchaseHistory[0], null);
        await checkCancellationHistory(purchaseHistory[1], ticket1.amount);
        await checkCancellationHistory(purchaseHistory[2], null);

        await sendRequest(ticket2.ticket_barcode);
        await checkPurchase(purchase.purchase_id);
        await checkPurchase(ticket1.purchase_id);
        await checkPurchase(ticket2.purchase_id);
        purchaseHistory = await findPurchaseHistoryRecords(purchase, ticket1, ticket2);
        await checkCancellationHistory(purchaseHistory[0], ticket2.amount);
        await checkCancellationHistory(purchaseHistory[1], ticket1.amount);
        await checkCancellationHistory(purchaseHistory[2], ticket2.amount);
    });

    it('check ticket', async function () {
        await init(require('./fixture/ticket-void/check-ticket'));
        const [purchase] = eventData.getTableRows('purchase');
        await sendRequest(purchase.ticket_barcode);
        await checkPurchase(purchase.purchase_id);
        await checkPurchaseTicket(purchase.purchase_id);
        const [purchaseHistory] = await findPurchaseHistoryRecords(purchase);
        await checkCancellationHistory(purchaseHistory, purchase.amount);
    });

    it('free ticket', async function () {
        await init(require('./fixture/ticket-void/free-ticket'));
        const [purchase] = eventData.getTableRows('purchase');
        const [ticketDiscount] = eventData.getTableRows('ticket_discount');
        await sendRequest(purchase.ticket_barcode);
        await checkPurchase(purchase.purchase_id);
        await checkPurchaseTicket(purchase.purchase_id);
        await checkTicketDiscount(ticketDiscount.ticket_discount_id, ticketDiscount.used_count);
        const [purchaseHistory] = await findPurchaseHistoryRecords(purchase);
        await checkCancellationHistory(purchaseHistory, purchase.amount);
    });

    it('free ticket with restore', async function () {
        await init(require('./fixture/ticket-void/free-ticket'));
        const [purchase] = eventData.getTableRows('purchase');
        const [ticketDiscount] = eventData.getTableRows('ticket_discount');
        await sendRequest(purchase.ticket_barcode, {restore: true});
        await checkPurchase(purchase.purchase_id);
        await checkPurchaseTicket(purchase.purchase_id);
        await checkTicketDiscount(ticketDiscount.ticket_discount_id, ticketDiscount.used_count - 1);
        const [purchaseHistory] = await findPurchaseHistoryRecords(purchase);
        await checkCancellationHistory(purchaseHistory, purchase.amount);
        await checkRestoreHistory(purchaseHistory, ticketDiscount.used_count, ticketDiscount.code);
    });

    async function sendRequest(barcode, body={}) {
        return request({
            method: 'POST',
            uri: getUrl(barcode),
            body: {
                restore: false,
                ...body,
            },
            json: true,
            resolveWithFullResponse: true,
            headers: {
                'cookie': cookies,
                'content-type': 'application/json',
            }
        });
    }

    function getUrl(barcode) {
        return `http://${HOST}/api/event/${eventData.eventId}/ticket/${barcode}/payment/void`;
    }

    async function checkPurchase(purchase_id, canceled = true) {
        const purchase = await findPurchase(purchase_id);
        if(canceled) {
            expect(purchase.canceled_date).to.not.equal(null);
            expect(purchase.status).to.equal('canceled');
        }
        else {
            expect(purchase.canceled_date).to.equal(null);
            expect(purchase.status).to.not.equal('canceled');
        }
    }

    async function checkPurchaseTicket(purchase_id) {
        const purchase_ticket = await findPurchaseTicket(purchase_id);
        expect(purchase_ticket.canceled).to.not.equal(null);
    }

    async function checkCancellationHistory(purchaseHistory, amount) {
        let cancellationHistory = purchaseHistory.filter(h => h.action === 'purchase.canceled');
        if(amount === null) {
            expect(cancellationHistory.length).to.equal(0);
        }
        else {
            expect(cancellationHistory.length).to.equal(1);
            expect(cancellationHistory[0]).to.deep.include({
                description: "Purchase canceled",
                user_id: user.userId,
            });
            expect(Number(cancellationHistory[0].amount)).to.equal(Number(amount));
        }
    }

    async function checkRestoreHistory(purchaseHistory, quantity, code) {
        let restoreHistory = purchaseHistory.filter(h => h.action === 'discount.restored');
        expect(restoreHistory.length).to.equal(1);
        expect(restoreHistory[0].description).to.equal(`Restored ${quantity} Item(s) for Discount ${code}`);
    }

    async function checkTicketDiscount(ticket_discount_id, usedCount) {
        const ticketDiscount = await findTicketDiscount(ticket_discount_id);
        expect(ticketDiscount).to.deep.include({
            used_count: usedCount,
        })
    }

    async function findPurchase(purchase_id) {
        return Db.query(
            knex('purchase').select('*').where('purchase_id', purchase_id)
        ).then(r => r.rows[0]);
    }

    async function findPurchaseTicket(purchase_id) {
        return Db.query(
            knex('purchase_ticket').select('*').where('purchase_id', purchase_id)
        ).then(r => r.rows[0]);
    }

    async function findPurchaseHistoryRecords(...purchases) {
        const history = await Db.query(
            knex('purchase_history').select('*').whereIn('purchase_id', purchases.map(p => p.purchase_id))
        ).then(r => r.rows);
        return purchases.map(p => history.filter(h => h.purchase_id === p.purchase_id))
    }

    async function findTicketDiscount(ticket_discount_id) {
        return await Db.query(
            knex('ticket_discount').select('*').where('ticket_discount_id', ticket_discount_id)
        ).then(r => r.rows[0]);
    }
});
