const argv                    = require('optimist').argv,
      path                    = require('path'),
      fs                      = require('fs'),
      sails                   = require('sails'),
      os                      = require('os'),

      LOGS_DIR                = path.resolve(__dirname, '..', 'logs'),
      UPLOADS_DIR             = path.resolve(__dirname, '..', 'uploads'),
      UPLOADS_ROSTER_IMPORT   = path.resolve(UPLOADS_DIR, 'rosterImport'),
      DISCOUNTS_EXPORT_DIR    = path.resolve(UPLOADS_DIR, 'discounts'),
      STRIPE_UPLOADS          = path.resolve(UPLOADS_DIR, 'stripe'),
      EXPORT_DIR              = path.resolve(__dirname, '..', 'export'),
      WEBPOINT_DIR            = path.resolve(__dirname, '..', 'webpoint'),
      SWAGGER_DIR             = path.resolve(__dirname, '..', 'swagger'),
      DATA_FOLDERS_STRUCTURE  = [
          path.resolve(__dirname, '..', 'data'),
          path.resolve(__dirname, '..', 'data', 'tickets'),
          path.resolve(__dirname, '..', 'data', 'tickets', 'qrcodes'),
          path.resolve(__dirname, '..', 'data', 'officials'),
          path.resolve(__dirname, '..', 'data', 'officials', 'qrcodes'),
      ];

global._ = require('lodash');
global.knex = require('knex')({ client: 'pg', useNullAsDefault: true });

// SW-2319 hotfix
os.tmpDir = os.tmpdir;

// Output argv parameters
console.log('argv parameters: ', argv);

// Output environment variables
console.log(JSON.stringify(process.env, null, ' '));


if (argv.prod || argv.dev || argv.esw || argv.admin) {
    argv.hooks = {
        grunt       : false,
        orm         : false,
        pubsub      : false,
        sockets     : false,
        i18n        : false,
        async       : false,
        blueprints  : false
    };
}

if(!argv.hooks) {
  argv.hooks = {};
}

argv.hooks.swagger = !argv.prod;

createConfigFolders();

sails.lift(argv);

function createDirectiesStructure(foldersList) {
    for (let folder of foldersList) {
        if (!fs.existsSync(folder)) {
            fs.mkdirSync(folder);
        }
    }
}


function createConfigFolders () {
    if (!fs.existsSync(LOGS_DIR)){
        fs.mkdirSync(LOGS_DIR);
    }      
    if (!fs.existsSync(UPLOADS_DIR)){
        fs.mkdirSync(UPLOADS_DIR);
    }
    if (!fs.existsSync(UPLOADS_ROSTER_IMPORT)){
        fs.mkdirSync(UPLOADS_ROSTER_IMPORT);
    }
    if(!fs.existsSync(DISCOUNTS_EXPORT_DIR)) {
        fs.mkdirSync(DISCOUNTS_EXPORT_DIR);
    }
    if(!fs.existsSync(STRIPE_UPLOADS)) {
        fs.mkdirSync(STRIPE_UPLOADS);
    }
    if (!fs.existsSync(EXPORT_DIR)){
        fs.mkdirSync(EXPORT_DIR);
    }
    if(!fs.existsSync(WEBPOINT_DIR)) {
        fs.mkdirSync(WEBPOINT_DIR);
    }
    if(!fs.existsSync(SWAGGER_DIR)) {
      fs.mkdirSync(SWAGGER_DIR);
    }

    createDirectiesStructure(DATA_FOLDERS_STRUCTURE);
}

