module.exports.beforeShutdown = async function beforeShutdown (done) {
    try {
        await Promise.all([
            Db.end(),
            EmailService.endQueueClient(),
            Cache.close(),
            RedisService.close(),
            BulkLogger.close(),
            SalesHubService.webhook.queue.close(),
            StripeService.webhook.queue.close(),
            StripeService.payouts.closeQueue(),
            EventService.duplication.close(),
        ]);
        console.log('All Redis connections closed successfully');
    } catch (error) {
        console.error('Error closing Redis connections:', error);
    } finally {
        done();
    }
};
