angular.module('SportWrench').service('UtilsService', ['$filter', '$location', 'SWT_URL', 'SALES_HUB_URL', UtilsService]);

function UtilsService ($filter, $location, SWT_URL, SALES_HUB_URL) {
    this._filter   = $filter;
    this._location = $location;
    this.SWT_URL = SWT_URL;
    this.SALES_HUB_URL = SALES_HUB_URL;
}

// TODO: write a filter
UtilsService.prototype.approxNumber = function (n) {
	return (Math.round(parseFloat(n) * 100) / 100);
};

UtilsService.prototype.showYear = function (currentEvent, index, eventsList, useSeason = false) {
    const field = useSeason ? 'season' : 'year';

	if (index === 0) {
        return true;
    }

    var prevEvent = eventsList[index - 1];

    return prevEvent && (prevEvent[field] !== currentEvent[field]);
};

UtilsService.prototype.findUniqueChars = function (str) {
	var uniques = [];

    for (var i = 0, len = str.length, _char; i < len; ++i) {
        _char = str[i];

        if (uniques.indexOf(_char) === -1) {
        	uniques.push(_char);
        }
    }

    return uniques;
};

UtilsService.prototype.prepareMergeTags = function (variables) {
    return variables.map(function (tmplVar) {
        return {
            name    : tmplVar.title,
            value   : tmplVar.pattern
        }
    })
}

UtilsService.prototype.isLiveIconClass = function (isLive) {
    return {
        'fa fa-circle xs-text'  : true,
        'is-live'               : isLive,
        'not-live'              : !isLive
    };
}

UtilsService.prototype.isEmpty = function (obj) {
    if (obj === null) {
        return true;
    } 

    var keys = Object.keys(obj).filter(function (key) {
        return key !== '$$hashKey'
    })

    return (keys.length === 0);
}

UtilsService.prototype.capitalizeFirstLetter = function (word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
}

UtilsService.prototype.getAmount = function (currency, val) {
    var curFilter = this._filter('currency');

    return currency + ' ' + curFilter(val || 0, '');
}

UtilsService.prototype.sumByField = function(collection, field) {
    return collection.reduce((acc, item) => {
        return acc + item[field];
    }, 0);
}

UtilsService.prototype.getTicketsDirectLink = function (ticketsCode, isAssignedTickets, useMerchandiseSales) {
    return isAssignedTickets
        ? `${this.SWT_URL}/buy?event=${ticketsCode}`
        : !useMerchandiseSales
            ? `${this.SWT_URL}/#/events/${ticketsCode}`
            : `${this.SWT_URL}/#/pay/merchandise/${ticketsCode}`;
}

UtilsService.prototype.getTicketsSalesHubLink = function ({ salesHubPosID }) {
    return `${this.SALES_HUB_URL}/point-of-sales/${salesHubPosID}/purchase-tickets`
}

// https://en.wikipedia.org/wiki/Unit_prefix#Binary_prefixes
UtilsService.prototype.getNextBinaryPrefixValue = function (value) {
    return Math.round(value/1024);
}

UtilsService.prototype.filterHiddenAccounts = function (accounts, currentAccountId) {
    if(!Array.isArray(accounts) || !accounts.length) {
        return accounts;
    }

    return accounts.filter(acc => !acc.hidden || (currentAccountId && acc.account_id === currentAccountId));
}
