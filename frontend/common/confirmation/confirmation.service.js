angular.module('SportWrench').service('ConfirmationService', ConfirmationService);

function ConfirmationService ($uibModal, ConfirmationPartialDataService) {
	this.$modal  			    = $uibModal;
	this._partialDataService    = ConfirmationPartialDataService;

	Object.defineProperties(this, {
        'DEFAULT_MSG': {
            value           : 'Confirm the Action',
            writable        : false,
            configurable    : false,
        },
        'NO_RESP': {
            value 			: 'no',
            writable 		: false,
            configurable 	: false
        },
        'YES_RESP': {
            value 			: 'yes',
            writable 		: false,
            configurable 	: false
        },
        'CANCEL_RESP': {
            value 			: 'cancel',
            writable 		: false,
            configurable 	: false
        },
        'DEFAULT_TITLE': {
            value 			: 'Confirm',
            writable 		: false,
            configurable 	: false
        },
        'PARTIAL_DATA': {
            get: () => this._partialDataService.data,
        }
    })
}


ConfirmationService.prototype.ask = function (message, params, templatePartial = '') {
    var self  			 = this,
        modalParams  	 = params || {},
        disableNoBtn  	 = modalParams.disableNoBtn     || false,
        disableCancelBtn = modalParams.disableCancelBtn || false,
        backdrop         = modalParams.backdrop         || true,
        disableYesBtn     = modalParams.disableYesBtn   || false;

    return this.$modal.open({
		template    : `<confirm-modal>${templatePartial}</confirm-modal>`,
		size 		: 'sm',
		windowClass : 'sw-confirm',
        backdrop    : backdrop,
		controller  : ['$scope', function ($scope) {
			$scope.msg  		    = message || self.DEFAULT_MSG;
			$scope.title 		    = modalParams.title || self.DEFAULT_TITLE;
			$scope.yesResp  	    = self.YES_RESP;
			$scope.noResp  	        = self.NO_RESP;
			$scope.cancelResp       = self.CANCEL_RESP;
			$scope.disableNoBtn     = disableNoBtn;
            $scope.disableCancelBtn = disableCancelBtn;
            $scope.disableYesBtn    = disableYesBtn;
			$scope.description      = modalParams.description || false;
		}]
	}).result.then(function (resp) {
		return (resp === self.YES_RESP)?self.YES_RESP:self.CANCEL_RESP;
	}, function (resp) {
		return (resp === self.NO_RESP)?self.NO_RESP:self.CANCEL_RESP;
	});
};
