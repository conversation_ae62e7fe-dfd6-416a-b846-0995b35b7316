<div ng-if="$ctrl.isMemberChecked">
    <div ng-class="$ctrl.controlClass('{{$ctrl.member}}_payment_option', true)">
        <label class="col-sm-4 control-label">{{$ctrl.getMemberTitle()}} Payment Option</label>
        <div class="col-sm-8">
            <select
                class="form-control"
                name="{{$ctrl.member}}_payment_option"
                ng-options="opt.id as opt.label for opt in ::$ctrl.paymentOptions"
                ng-model="$ctrl.paymentOption"
                required>
                <option value="">Select Payment Option</option>
            </select>
        </div>
    </div>
    
    <div ng-if="!$ctrl.hideRequiredFields">
        <div ng-if="$ctrl.showBankAccountFields()">
            <div ng-class="$ctrl.controlClass('acc_num', true)">
                <label class="col-sm-4 control-label">Bank Account Number</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="acc_num" ng-model="$ctrl.memberData.bank_account_number" required>
                </div>
            </div>
            <div ng-class="$ctrl.controlClass('acc_routing', true)">
                <label class="col-sm-4 control-label">Bank Account Routing #</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="acc_routing" ng-model="$ctrl.memberData.bank_account_routing" required>
                </div>
            </div>
        </div>
        <div ng-if="$ctrl.showRQPayFields()">
            <div ng-class="$ctrl.controlClass('rq_pay_username', false)">
                <label class="col-sm-4 control-label">RQ Pay Username</label>
                <div class="col-sm-8">
                    <input type="email" email-validator class="form-control" name="rq_pay_username" ng-model="$ctrl.memberData.rq_pay_username">
                </div>
            </div>
        </div>
        <div ng-if="$ctrl.showArbiterpayFields()">
            <div ng-class="$ctrl.controlClass('arbiterpay_username', true)">
                <label class="col-sm-4 control-label">ArbiterPay Username</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="arbiterpay_username" ng-model="$ctrl.memberData.arbiterpay_username" required>
                </div>
            </div>
            <div ng-class="$ctrl.controlClass('arbiterpay_account_number', true)">
                <label class="col-sm-4 control-label">ArbiterPay Account Number</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="arbiterpay_account_number" ng-model="$ctrl.memberData.arbiterpay_account_number" required>
                </div>
            </div>
        </div>
    </div>
</div>
