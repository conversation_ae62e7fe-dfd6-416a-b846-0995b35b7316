class Component {
    constructor(PAYMENT_OPTIONS, STAFF_MEMBER_TYPE, OFFICIAL_MEMBER_TYPE) {
        this.PAYMENT_OPTIONS = PAYMENT_OPTIONS;
        this.STAFF_MEMBER_TYPE = STAFF_MEMBER_TYPE;
        this.OFFICIAL_MEMBER_TYPE = OFFICIAL_MEMBER_TYPE;
    }

    getMemberTitle() {
        const titles = {
            [this.STAFF_MEMBER_TYPE]: 'Staff',
            [this.OFFICIAL_MEMBER_TYPE]: 'Official',
        };

        return titles[this.member];
    }

    controlClass(fieldName, isFieldRequired) {
        return this.cClass({ fieldName, isFieldRequired })
    }

    showArbiterpayFields() {
        return this.paymentOption === this.PAYMENT_OPTIONS.arbiterPay;
    }

    showRQPayFields() {
        return this.paymentOption === this.PAYMENT_OPTIONS.rqPay;
    }

    showBankAccountFields() {
        return this.paymentOption === this.PAYMENT_OPTIONS.directDeposit;
    }
}

angular.module('SportWrench').component('paymentOptions', {
    templateUrl: 'official/events/checkin-form/payment-options/payment-options.html',
    bindings: {
        cClass: '&',
        member: '<',
        memberData: '=',
        isMemberChecked: '<',
        paymentOptions: '<',
        paymentOption: '=',
        hideRequiredFields: '<',
    },
    controller: [
        'PAYMENT_OPTIONS', 
        'STAFF_MEMBER_TYPE',
        'OFFICIAL_MEMBER_TYPE',
        Component
    ]
})
