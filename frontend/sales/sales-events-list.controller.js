angular.module('SportWrench')
.controller('SalesEventsListController', SalesEventsListController);

function SalesEventsListController (
    $scope, $state, SalesService, UtilsService, $filter, APP_ROUTES
) {
    const UTCDateFilter = $filter('UTCdate');
    $scope.utils = {
        loading: true,
    };
    $scope.states = {
        event_exhibitors: APP_ROUTES.SM.EVENT_EXHIBITORS,
    };
    SalesService.getEvents()
        .then((events) => {
            $scope.events = events.map((event) => {
                event.date_start_form = UTCDateFilter(event.date_start, 'MMM DD, YYYY');
                event.year = +UTCDateFilter(event.date_start, 'YYYY');
                return event;
            });
        })
        .finally(() => {
            $scope.utils.loading = false;
        });

    $scope.showYear = function (event, index) {
        return UtilsService.showYear(event, index, this.events, true);
    };

    $scope.goToInvoices = function (event) {
        $state.go(APP_ROUTES.SM.INVOICES, {
            filters: {
                eventName: event.name,
                eventYear: event.season,
            }
        })
    };
}
