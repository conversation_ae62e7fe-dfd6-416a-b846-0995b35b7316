angular.module('SportWrench').controller('DetailedReportController', DetailedReportController);
function DetailedReportController ($scope, $stateParams, $http, $uibModalInstance) {
    var event_id = $stateParams.event;
    $scope.exhibitors = [];
    $scope.event = {};
    $scope.data_loaded = false;
    $http.get('/api/sales/event/' + event_id + '/report')
    .success(function (data) {
        $scope.exhibitors = data.exhibitors;
        $scope.event = data.event_data;
        $scope.data_loaded = true;
    })

    $scope.get_sponsor_info = function (sponsor) {
        if(!sponsor || !sponsor.sponsor_id) return;    
        $http.get('/api/sales/event/' + event_id + '/sponsor/' + sponsor.sponsor_id + '/info')
        .success(function(data) {
            sponsor.info = data.sponsor_data;
        })
    }

    $scope.export_to_excel = function () {
        window.location = '/api/sales/event/' + event_id + '/sponsor/export';
    }

    $scope.close = function () {
        $uibModalInstance.close();
    }
}
