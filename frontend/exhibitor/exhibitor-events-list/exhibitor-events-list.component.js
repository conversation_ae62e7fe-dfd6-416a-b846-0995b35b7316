class Component {
    constructor(
        ExhibitorService,
        INTERNAL_ERROR_MSG,
        UtilsService,
        $filter,
        $state,
        APP_ROUTES,
        EXHIBITOR_EVENT_TABS,
        EXHIBITOR_EVENT_STATUS,
    ) {
        this.service = ExhibitorService;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.utilsService = UtilsService;
        this.$filer = $filter;
        this.$state = $state;
        this.APP_ROUTES = APP_ROUTES;
        this.tabs = EXHIBITOR_EVENT_TABS;
        this.status = EXHIBITOR_EVENT_STATUS;

        this.events = [];

        this.loading = {
            inProcess: true,
            error: '',
        }

    }

    $onInit() {
        this.onInit();
    }

    onInit() {
        this.service.getEvents()
            .then(this.onGetEventsSuccess.bind(this))
            .catch(this.onGetEventsFail.bind(this))
            .finally(this.onGetEventsFinally.bind(this));
    }

    onGetEventsSuccess(events) {
        this.events = events;

        this.generateYearForEachEvent();
    }

    onGetEventsFail(error) {
        this.loading.error = error && error.validation
            ? error.validation
            : this.INTERNAL_ERROR_MSG
    }

    onGetEventsFinally() {
        this.loading.inProcess = false;
    }

    showYear(event, index) {
        return this.utilsService.showYear(event, index, this.events, true);
    }

    generateYearForEachEvent() {
        this.events.forEach(event => {
            event.year = this.$filer('UTCdate')(event.date_start, 'YYYY');
        })
    }

    showInvoicesBtn({ has_invoice }) {
        return has_invoice;
    }

    isApplyMode(event) {
        const { exhibitors_registration_is_opened, status, event_is_over, has_invoice } = event;

        const rules = [
            exhibitors_registration_is_opened,
            !status,
            !has_invoice,
            !event_is_over
        ];

        return rules.every(rule => rule);
    }

    isEditMode(event) {
        const { status, has_invoice, event_is_over } = event;

        const rules = [
            status === this.status.PENDING,
            !has_invoice,
            !event_is_over,
        ];

        return rules.every(rule => rule);
    }

    isViewMode(event) {
        const { status, has_invoice, event_is_over } = event;

        const rules = [
            status === this.status.APPROVED,
            status === this.status.DECLINED,
            has_invoice,
            event_is_over
        ];

        return rules.some(rule => rule);
    }

    goToInvoices({ name, season }) {
        this.$state.go(this.APP_ROUTES.EX.RECEIPTS, {
            filters: {
                eventName: name,
                eventYear: season,
            }
        })
    }

    getModes(event) {
        return {
            isView: this.isViewMode(event),
            isEdit: this.isEditMode(event),
            isApply: this.isApplyMode(event),
        }
    }

    openModal(event, tab) {
        this.service.openExhibitorEventRegistrationModal({
            eventID: event.event_id,
            eventName: event.name,
            tab,
            modes: this.getModes(event),
            status: event.status,
            exhibitorsRegistrationIsOpened: event.exhibitors_registration_is_opened,
        }).then(reloadList => {
            if(reloadList) {
                this.onInit();
            }
        });
    }
}

angular.module('SportWrench').component('exhibitorEventsList', {
    templateUrl: 'exhibitor/exhibitor-events-list/exhibitor-events-list.html',
    controller: [
        'ExhibitorService',
        'INTERNAL_ERROR_MSG',
        'UtilsService',
        '$filter',
        '$state',
        'APP_ROUTES',
        'EXHIBITOR_EVENT_TABS',
        'EXHIBITOR_EVENT_STATUS',
        Component
    ]
});
