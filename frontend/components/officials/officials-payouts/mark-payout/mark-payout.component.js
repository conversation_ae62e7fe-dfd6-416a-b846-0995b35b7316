class Component {
    constructor($stateParams, OfficialsPayoutsService) {
        this.$stateParams = $stateParams;
        this.service = OfficialsPayoutsService;
        this.eventID = this.$stateParams.event;
        this._info = {};
        this._history = [];
    };

    $onChanges(changes) {
        if (changes.info) {
            if (_.isEmpty(changes.info.previousValue) || _.isEqual(changes.info.currentValue, changes.info.previousValue)) {
                return;
            }

            this.divideInfo(changes.info.currentValue);
        }
    }

    $onInit() {
        this.divideInfo();
    }

    divideInfo(info = this.info) {
        this._info = _.omit(info, 'history');
        this._history = info.history;
    }

    formatAmount(amount) {
        if (!amount) {
            return '';
        } else if (amount < 0) {
            return `-$${Math.abs(amount)}`
        } else {
            return `$${amount}`;
        }
    }

    showHistory() {
        return this._history.length;
    }
}

angular.module('SportWrench').component('markPayout', {
    templateUrl: 'components/officials/officials-payouts/mark-payout/mark-payout.html',
    bindings: {
        onClose: '&',
        info: '<',
        onMarkPayout: '&',
    },
    controller: ['$stateParams', 'OfficialsPayoutsService', Component]
})