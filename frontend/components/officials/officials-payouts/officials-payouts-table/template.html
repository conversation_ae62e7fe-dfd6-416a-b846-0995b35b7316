<div class="row officials-payouts-table_row">
  <div class="col-sm-12">
    <table class="officials-payouts-table">
      <thead>
        <tr>
            <th colspan="2">Name</th> <!--Name-->
            <th ng-if="$ctrl.isOfficialsType()" class="border-bottom-transparent">Rank</th> <!--Rank-->
            <th ng-if="$ctrl.isOfficialsType()" colspan="{{2 * $ctrl.matchTypes.length + 2}}">Matches</th> <!--Matches-->
            <th colspan="{{$ctrl.additionalCategories.length + ($ctrl.isOfficialsType() ? 1 : 0)}}">Other</th> <!--Other-->
            <th class="border-bottom-transparent" colspan="2">TOTAL</th> <!--TOTAL FEES DUE-->
            <th class="border-bottom-transparent" colspan="3">PAYMENT DETAILS</th> <!--PAYMENT DETAILS-->
            <th class="border-bottom-transparent">BALANCE</th> <!--BALANCE-->
        </tr>
        <tr>
          <th class="border-bottom-transparent" colspan="2"></th> <!--Name-->
          <th ng-if="$ctrl.isOfficialsType()" class="officials-payouts-table-header_to-bottom" rowspan="3"></th> <!--Rank-->
          <th ng-if="$ctrl.isOfficialsType()" ng-repeat="type in $ctrl.matchTypes" colspan="2">{{ type }}</th> <!--Matches-->
          <th ng-if="$ctrl.isOfficialsType()" colspan="2">Totals</th> <!--Matches-->
          <th class="officials-payouts-table-header_to-bottom" rowspan="2" ng-repeat="category in $ctrl.additionalCategories">{{$ctrl.formatCategoryTitle(category)}}</th> <!--Other-->
          <th ng-if="$ctrl.isOfficialsType()" class="officials-payouts-table-header_to-bottom" rowspan="2">Total</th> <!--Other-->
          <th class="border-bottom-transparent" colspan="2">FEES DUE</th> <!--TOTAL FEES DUE-->
          <th class="border-bottom-transparent" colspan="3"></th> <!--PAYMENT DETAILS-->
          <th class="border-bottom-transparent"></th> <!--BALANCE-->
        </tr>
        <tr>
          <th colspan="2">
            <div class="officials-payouts-table-header_name">
              <div>Last</div>
              <div>First</div>
            </div>
          </th>
          <th ng-if="$ctrl.isOfficialsType()" ng-repeat-start="count in $ctrl.matchTypes track by $index">Qty</th>
          <th ng-if="$ctrl.isOfficialsType()" ng-repeat-end>Rate</th>
          <th ng-if="$ctrl.isOfficialsType()">Qty</th>
          <th ng-if="$ctrl.isOfficialsType()">Fees</th>
          <th class="border-top-transparent" rowspan="2" colspan="2"></th>
          <th class="border-top-transparent" rowspan="2" colspan="3"></th>
          <th class="border-top-transparent" rowspan="2"></th>
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="payout in $ctrl.payouts track by $index">
          <td colspan="2">
            <div class="officials-payouts-table-body_name">
              <div>{{payout.last}}</div>
              <div>{{payout.first}}</div>
            </div>
          </td>
          <td ng-if="$ctrl.isOfficialsType()">{{payout.rank}}</td>
          <td ng-if="$ctrl.isOfficialsType()" ng-repeat-start="match in payout.official_matches track by $index">{{match.qty || ''}}</td>
          <td ng-if="$ctrl.isOfficialsType()" ng-repeat-end>${{match.rate}}</td>
          <td ng-if="$ctrl.isOfficialsType()" >{{payout.totalMatches}}</td>
          <td ng-if="$ctrl.isOfficialsType()" class="text-bold">${{payout.totalRate}}</td>
          <td ng-repeat="p in payout.additional_payments track by $index">
              <input 
                ng-disabled="!$ctrl.canUpdatePayment" 
                class="edit-field_amount" 
                ng-model="p.amount" 
                editable-currency="$" 
                ng-blur="$ctrl.updateAdditionalPayment(p, payout.event_official_id)" 
                ng-focus="$ctrl.onFocusAdditionalPaymentCell($event, p, payout.event_official_id)"
                ng-style="!$ctrl.canUpdatePayment && {'cursor': 'progress'}"
                float-number="allow-negative"
              >
          </td>
          <td ng-if="$ctrl.isOfficialsType()" class="text-bold">{{$ctrl.formatAdditionalTotalAmount(payout)}}</td>
          <td class="text-bold" colspan="2">{{$ctrl.formatAmount(payout.totalPay)}}</td>
          <td ng-click="$ctrl.openMarkPayoutModal(payout)" class="balance-col balance-border-right">
            <p class="balance-col_item" ng-repeat="p in payout.payouts track by $index">{{$ctrl.formatAmount(p.amount)}}</p>
          </td>
          <td ng-click="$ctrl.openMarkPayoutModal(payout)" class="balance-col balance-border-right">
            <p class="balance-col_item" ng-repeat="p in payout.payouts track by $index">
              <span>{{$ctrl.formatPaymentMethod(p)}}</span> <span ng-if="p.check_number">Check</span>
            </p>
          </td>
          <td ng-click="$ctrl.openMarkPayoutModal(payout)" class="balance-col">
            <p class="balance-col_item" ng-repeat="p in payout.payouts track by $index">{{p.date_paid | date: 'MM/dd/yyyy'}}</p>
          </td>
          <td ng-class="$ctrl.getNegativeNumberClass(payout.balance)" class="text-bold">{{$ctrl.formatAmount(payout.balance, true)}}</td>
        </tr>
        <tr>
          <!--Total row-->
          <tr class="total-row">
            <td colspan="2">Total</td>
            <td ng-if="$ctrl.isOfficialsType()"></td>
            <td ng-if="$ctrl.isOfficialsType()" ng-repeat-start="qty in $ctrl.total.matchesTypeTotal track by $index">{{qty || ''}}</td>
            <td ng-if="$ctrl.isOfficialsType()" ng-repeat-end></td>
            <td ng-if="$ctrl.isOfficialsType()">{{$ctrl.total.matches}}</td>
            <td ng-if="$ctrl.isOfficialsType()">${{$ctrl.total.rate}}</td>
            <td ng-repeat="amount in $ctrl.total.additionalPaymentsTotal track by $index">{{$ctrl.formatAmount(amount)}}</td>
            <td ng-if="$ctrl.isOfficialsType()">{{$ctrl.formatAmount($ctrl.total.additional)}}</td>
            <td colspan="2">${{$ctrl.total.pay}}</td>
            <td colspan="3">{{$ctrl.formatAmount($ctrl.total.paid)}}</td>
            <td ng-class="$ctrl.getNegativeNumberClass($ctrl.total.balance)">{{$ctrl.formatAmount($ctrl.total.balance, true)}}</td>
          </tr>
        </tr>
      </tbody>
    </table> 
  </div>
</div>
