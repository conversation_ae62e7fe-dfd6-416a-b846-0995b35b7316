angular.module('SportWrench').component('manualPayout', {
    templateUrl     : 'components/officials/payouts/manual-payout.html',
    bindings        : {
        getPayoutsHistory 	: '&',
        loading             : '<',
        official            : '<'
    },
    controller      : ['$stateParams', 'StripeService', 'toastr', '$rootScope', manualPayoutContoller]
});

function manualPayoutContoller ($stateParams, StripeService, toastr, $rootScope) {

    this.payout_amount = 0;

    this.utils = {
        payoutInProcess: false
    };

    this.makePayout = function () {
        const eventId = $stateParams.event;
        const officialId = $stateParams.official;
        const amount = +this.payout_amount || 0;

        if (amount <= 0) {
            this.payoutForm.$setValidity('payoutAmountValid', false);
            return;
        }

        this.utils.payoutInProcess = true;
        StripeService.makePayout(eventId, officialId, amount)
            .then(() => {
                this.utils.payoutInProcess = false;
                this.payout_amount = 0;
                this.loading.error = false;
                toastr.success(`Payout of $ ${amount} to ${this.official.first} ${this.official.last} successfully made.`);
                $rootScope.$emit('new-payout-made');
            })
            .catch((err) => {
                this.utils.payoutInProcess = false;
                this.loading.error = true;
                this.loading.errMsg = (err.data && err.data.validation) ?
                    err.data.validation :
                    'Internal Server Error.'
            });

    };
}