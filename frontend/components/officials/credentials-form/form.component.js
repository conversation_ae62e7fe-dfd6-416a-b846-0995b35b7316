angular.module('SportWrench').component('officialRegInfoCredentials', {
	templateUrl: 'components/officials/credentials-form/form.html',
	bindings: {
		official                            : '<',
		onSubmit                            : '&',
        onChanged                           : '&',
        eventOfficialAdditionalRoleEnabled  : '=',
        officialAdditionalRoles             : '<'
	},
	controller: ['OFFICIALS_RATINGS', '_', '$rootScope', '$stateParams', 'ConfirmationService', Ctrl]
});

function Ctrl (OFFICIALS_RATINGS, _, $rootScope, $stateParams, ConfirmationService) {
    var self = this;

    var _wsField = 'work_status', _rankField = 'rank', _schNameField = 'schedule_name', _roleField = 'official_additional_role_id';

	this.officialCache = {};
	this._modified = [];
	this.data = null;
    this.ratings       = OFFICIALS_RATINGS;
    this.wsLabel 		= {
    	pending 		: 'Pending',
    	approved 		: 'Approved',
    	declined 		: 'Declined',
    	waitlisted 		: 'Wait Listed'
    };



    this.onWSChange = function () {
        self.eventOfficialForm.work_status.$modified = (self.official.work_status !== self.officialCache.work_status);
        
        setFieldModified(self.eventOfficialForm.work_status.$modified, _wsField);
    };

    this.onRankChange = function () {
        self.eventOfficialForm.rank.$modified = (self.official.rank !== self.officialCache.rank);

        setFieldModified(self.eventOfficialForm.rank.$modified, _rankField);
    };
    
    this.onRoleChange = function () {
        self.eventOfficialForm.official_additional_role_id.$modified = (self.official.official_additional_role_id !== self.officialCache.official_additional_role_id);
        setFieldModified(self.eventOfficialForm.official_additional_role_id.$modified, _roleField);
    };

    this.onSchNameChange = function () {
        self.eventOfficialForm.schedule_name.$modified 
        										= (self.official.schedule_name !== self.officialCache.schedule_name);

        setFieldModified(self.eventOfficialForm.schedule_name.$modified, _schNameField);
    };



    this.restoreDefaultWS = function () {
        self.official.work_status = self.officialCache.work_status;
        self.onWSChange();
    };

    this.restoreDefaultRank = function () {
        self.official.rank = self.officialCache.rank;
        self.onRankChange();
    };

    this.restoreDefaultSchName = function () {
        self.official.schedule_name = self.officialCache.schedule_name;
        self.onSchNameChange();
    };


    this.submit = function () {

    	if (!self.eventOfficialForm.$modified.length) {
    		return;
    	}

    	self.data = {
    		notes: self.changesNotes
    	};
        
        self._modified = _.clone(self.eventOfficialForm.$modified);
        
        self._modified.forEach(function (field) {
            self.data[field] = self.official[field];
    	});
        
        if(self._modified.includes(_wsField) && self.official.is_assign_match) {
            const askMessage    = 'This Official is assigned to the match. When you will change Work Status, Official automatically will remove from the match schedule. Do you really want to change the Work Status?';
            const title         = 'Confirm';
            
            ConfirmationService.ask(askMessage, {
                title: title,
            }).then(self.onConfirmationSubmit)
                .finally(() => {
                    self.eventOfficialForm.$setPristine();
                });
        } else {
            self.$submit(self.data);
        }
    };

    this.onConfirmationSubmit = (answer) => {
        if (answer === ConfirmationService.YES_RESP) {
            self.$submit(self.data);
        }
        
        if (answer === ConfirmationService.NO_RESP) {
            self.restoreDefaultWS();
        }
    };
    
    this.$submit = function (data) {
        self.onSubmit({ data: data })
            .then(function () {
                self.changesNotes = '';
                
                self._modified.forEach(function (field) {
                    self.officialCache[field] = data[field];
                    self.eventOfficialForm[field].$modified = false;
                    setFieldModified(false, field);
                });
            }).catch(err => {})
            .finally(() => {
                self.eventOfficialForm.$setPristine();
            });
    };



    this.$onChanges = function (changes) {
        if (changes.official && !_.isEmpty(changes.official.currentValue)) {
            console.log('$onChanges triggered for official');
            self.officialCache = _.pick(changes.official.currentValue, 'work_status', 'rank', 'schedule_name', 'official_additional_role_id');
        }
    };

    this.$postLink = function () {
        self.eventOfficialForm.$modified = [];
    };

    function setFieldModified (isModified, fieldName) {
        var _index = self.eventOfficialForm.$modified.indexOf(fieldName);

        if (isModified) {
            if (_index === -1) {
                self.eventOfficialForm.$modified.push(fieldName);
            }
        } else {
            self.eventOfficialForm.$modified.splice(_index, 1); 
        }

        self.onChanged({ isChanged: (self.eventOfficialForm.$modified.length > 0) });

        if(self.eventOfficialForm.$modified.length > 0) {
            $rootScope.$emit('official-data-not-saved');
        } else {
            $rootScope.$emit('official-data-saved');
        }
    }

    this.changesListener = function(isChanged) {
        self.onChanged({ isChanged })
    };
}
