<spinner active="$ctrl.initLoad"></spinner>
<div class="row" ng-if="!$ctrl.initLoad">
    <div class="col-sm-6">
        <div>
            <accounting-merchant-stats
                title="Exhibitors"
                payment-for="booths"
                stats="$ctrl.stats.merchant"
                balance="$ctrl.usdAvailable"
                account="$ctrl.accountID"
            ></accounting-merchant-stats>
        </div>
        <div ng-if="$ctrl.transfers.length">
            <p class="lead">Sent Booths Payouts:</p>
            <event-transfers-list list="$ctrl.transfers"></event-transfers-list>
        </div>
    </div>
    <div class="col-sm-6">
        <accounting-general-stats
            title="Booths"
            payment-for="booths"
            stats="$ctrl.stats.general"
            sw-item-fee="$ctrl.stats.event_fees.exhibitors_sw_fee"
        ></accounting-general-stats>
        <uncollected-fee-payments-list ng-if="$ctrl.enabledAssignedPayments" payment-for-type="booths"></uncollected-fee-payments-list>
    </div>
</div>
