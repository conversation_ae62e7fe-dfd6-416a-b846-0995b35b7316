angular.module('SportWrench').directive('swConfirm', function (ConfirmationService, _) {
	return {
		restrict 	: 'A',
		scope 	 	: {
			swConfirmDo 	: '&',
			swConfirmCancel : '&?',
			swConfirmParams : '=?',
			swConfirmArgs 	: '&?'
		},
		link: function (scope, elem, attrs) {
			var callAction = function (confirmResult) {
				var args = scope.swConfirmArgs && scope.swConfirmArgs();
				if(_.isArray(args)) {
					args.unshift(confirmResult);
					scope.swConfirmDo().apply(null, args);
				} else if (angular.isDefined(args)) {
					scope.swConfirmDo().apply(null, [confirmResult, args]);
				} else {
					scope.swConfirmDo()(confirmResult);
				}
			};
			elem.bind('click', function () {
				var params = { disableNoBtn: attrs.swConfirmHideNo === '' };
				ConfirmationService.ask(attrs.swConfirm, _.defaults(params, scope.swConfirmParams), attrs.swPartialTemplate)
                .then(function (result) {
                    if(ConfirmationService.YES_RESP === result) {
                    	callAction(true);
                    } else if (ConfirmationService.NO_RESP === result) {
                    	callAction(false);
                    } else if (scope.swConfirmCancel && scope.swConfirmCancel()) {
                    	scope.swConfirmCancel()();
                    }
                });
			});
		}
	};
});
