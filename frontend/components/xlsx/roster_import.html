<div class="modal-header">
    <h4 class="text-center">Import</h4>
</div>
<div class="modal-body">
    <form class="form-horizontal" role="form" name="importForm">
        <fieldset ng-disabled="submitted">
            <div class="well">
                <div class="form-group pointer">               
                    <div class="col-sm-offset-3 col-sm-9">
                        <p><i class="fa fa-info-circle"></i> Only .xlsx files are allowed</p>
                        <p>Click <a href="data/import/SportWrench Foreign Team Roster Import.xlsx">here</a> to download a sample file</p>
                        <input 
                            type="file" 
                            name="roster-list" 
                            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            required>
                    </div>
                </div>   
            </div>   
        </fieldset>      
    </form>
    <div class="text-center" ng-if="submitted">
        <strong>Import is in progress. Please wait....</strong><br/>
        <small>(Usually it takes about 2-3 minutes to import the data.)</small>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="$dismiss()">Close</button>
    <button class="btn btn-primary" ng-click="submit()" ng-disabled="submitted">Import</button>
</div>