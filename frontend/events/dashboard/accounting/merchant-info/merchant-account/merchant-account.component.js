angular.module('SportWrench').component('merchantAccount', {
    templateUrl 	: 'events/dashboard/accounting/merchant-info/merchant-account/merchant-account.html',
    controller 		: MerchantAccountController,
    bindings: {
        accountData: '<'
    }
});

MerchantAccountController.$inject = ['UtilsService'];

function MerchantAccountController(UtilsService) {
    this.usdAvailable = 0;

    let accData = this.accountData;

    this.account 		= accData.account   || {};
    this.balance 		= accData.balance   || {};
    this.current 		= accData.currentEventBalance   || {};
    this.autoTransfers 	= accData.transfers || [];
    this.events         = accData.events;

    this.accountForText = getAccountForText(this.account.usage);

    if (this.balance && this.balance.available) {
        this.balance.totalAvailable = this.balance.available.reduce(
            (sum, val) => UtilsService.approxNumber(sum + val), 0
        );
    }

    this.showAutoTransfers = function () {
        return (+this.account.last_transfer_days_ago >= 10);
    };

    function getAccountForText(accountUsage) {

        if(!_.isArray(accountUsage) || _.isEmpty(accountUsage)) {
            return '';
        }

        let capitalize = UtilsService.capitalizeFirstLetter.bind(UtilsService);

        if(accountUsage.length === 1) {
            return `${capitalize(accountUsage[0])}`;
        }

        if(accountUsage.length === 2) {
            return `${capitalize(accountUsage[0])} and ${capitalize(accountUsage[1])}`;
        }

        if(accountUsage.length === 3) {
            return `${capitalize(accountUsage[0])}, ${capitalize(accountUsage[1])} and ${capitalize(accountUsage[2])}`;
        }
    }
}
