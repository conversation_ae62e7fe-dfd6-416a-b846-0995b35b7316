

class Controller {
    constructor (
        ngTableParams, customFormBuilderService, $stateParams, CustomFormsDynamicColsFactory, CUSTOM_FORM_TYPE,
        CustomFormService
    ) {
        this.ngTableParams = ngTableParams;
        this.CustomFormBuilderService = customFormBuilderService;
        this.$stateParams = $stateParams;
        this.CustomFormsDynamicColsFactory = CustomFormsDynamicColsFactory;
        this.CUSTOM_FORM_TYPE = CUSTOM_FORM_TYPE;
        this.CustomFormService = CustomFormService;
    }

    $onInit () {
        this.filters = {
            limit: 100,
            page : 1
        };
        this.search = '';
        this.forms = [];
        this.defaultSort = false;

        this.tableParams = new this.ngTableParams({
            page: 1,
            count: 100,
            sorting: {},
            filter: this.filters,
        }, {
            total: 0,
            counts: [],
            filterDelay: 0,
            getData: this.getData.bind(this),
        });

        this.dynamicCols = this.CustomFormsDynamicColsFactory.getColumns();

        this.FORM_PLACE_LABEL = {
            [this.CUSTOM_FORM_TYPE.CAMPS_PURCHASE_PAGE]: 'Camps purchase page',
            [this.CUSTOM_FORM_TYPE.TEAM_ASSIGN_FOR_EVENT]: 'Team assign for event',
        }

        let self = this;
        this.delete = function (actionConfirmed, formID) {
            if(!actionConfirmed) {
                return;
            }

            return self.CustomFormBuilderService.deleteForm(
                self.$stateParams.event,
                formID
            ).then(() => {
                self.tableParams.reload();
            })
        }
    }

    sort (column) {
        if(!column.sortable) {
            return;
        }

        if (column) {
            this.filters.page = 1;

            let direction =  this.tableParams.isSortBy(column.name, 'asc')
                ? 'desc'
                : this.tableParams.isSortBy(column.name, 'desc') ? this.setSortDefault() : 'asc';

            this.tableParams.sorting(column.name, direction);
        }
    };

    columnClass(c) {
        let stylesClasses = {
            'text-center sortable': c.sortable,
            'sort-asc': this.tableParams.isSortBy(c.name, 'asc'),
            'sort-desc': this.tableParams.isSortBy(c.name, 'desc')
        };

        if (c.selectors) {
            stylesClasses[c.selectors] = true;
        }
        return stylesClasses;
    }

    filterSearch() {
        if(this.tableParams.settings().$loading) return;

        this.filters.search = this.search;
        this.filters.page = 1;
    };

    setSortDefault () {
        this.defaultSort = true;
        return 'text-center sortable';
    }

    getData ($q, params) {
        let _params = {};
        let orderBy = params.orderBy();
        let filter  = params.filter();

        if (filter) {
            _params = _.clone(filter);
        }

        if(orderBy && orderBy.length && !this.defaultSort) {
            _params.order  = orderBy[0].substr(1);
            _params.revert = orderBy[0].charAt(0) === '-';
        }

        return this.CustomFormBuilderService.getList(this.$stateParams.event, _params)
            .then(responseData => {
                if(!_.isEmpty(responseData)) {
                    this.forms = responseData.forms || [];
                    this.defaultSort = false;
                    this.totalRows = this.forms[0] && this.forms[0].total_rows || 0;
                }

                this.defaultSort = false;

                return responseData.forms;
            })
            .catch(err => {
                this.defaultSort = false;
                this.forms = [];

                return [];
            });
    }

    getPlaceLabel (type) {
        const label = this.FORM_PLACE_LABEL[type];

        if(!label) {
            return type;
        }

        return label;
    }

    exportResults (formID) {
        this.CustomFormService.exportFormResults(this.$stateParams.event, formID);
    }

    disableExportButton (form) {
        return form.editable;
    }

    edit (formID) {
        return this.CustomFormBuilderService.openFormEditingModal(
            this.$stateParams.event,
            formID
        ).then(() => {
            this.tableParams.reload();
        })
    }

    openCreationModal () {
        return this.CustomFormBuilderService.openFormCreationModal(this.$stateParams.event)
            .then(() => {
                this.tableParams.reload();
            })
    }
}

Controller.$inject = [
    'ngTableParams', 'customFormBuilderService', '$stateParams', 'CustomFormsDynamicColsFactory', 'CUSTOM_FORM_TYPE',
    'CustomFormService'
];


angular.module('SportWrench').component('customFormsList', {
    templateUrl: 'events/dashboard/custom-forms-builder/custom-forms-list/custom-forms-list.template.html',
    bindings: {},
    controller: Controller
});
