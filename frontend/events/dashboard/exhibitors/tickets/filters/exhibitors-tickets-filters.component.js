angular.module('SportWrench').component('exhibitorsTicketsFilters', {
    templateUrl 	: 'events/dashboard/exhibitors/tickets/filters/exhibitors-tickets-filters.html',
    bindings 		: {
        filtersChanged: '&',
    },
    controller 		: ExhibitorsTicketsFiltersController
});

function ExhibitorsTicketsFiltersController() {
    const self = this;

    this.filters = {};
    this.utils = {};

    this.search_keypress = function () {
        this.filtersChanged({ filters: this.filters, search: this.utils.search });
    };

    this.isFiltersApplied = function () {
        return this.utils.search;
    };

    //need to use "self" because clearFilters used in sw-confirm
    this.clearFilters = function () {
        self.utils.search = null;

        self.changed();
    };

    this.changed = function (withoutUpdate) {
        this.filtersChanged({ filters: this.filters, search: this.utils.search, updateWithoutReload: withoutUpdate });
    };

    this.changed(true);
}
