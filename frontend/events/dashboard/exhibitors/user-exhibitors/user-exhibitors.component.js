class UserExhibitorsComponent {
    constructor($stateParams, ExhibitorsService, INTERNAL_ERROR_MSG) {
        this.$stateParams = $stateParams;
        this.eventID = $stateParams.event;
        this.service = ExhibitorsService;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;

        this.exhibitors = [];
        this.filteredExhibitors = [];

        this.loading = {
            inProcess: true,
            error: '',
        }

        this.search = '';
    }

    $onInit() {
        this.loadExhibitors();
    }

    loadExhibitors() {
        this.service.getUserExhibitors(this.eventID)
            .then(this.onGetUserExhibitorsSucceeded.bind(this))
            .catch(this.onGetUserExhibitorsFailed.bind(this))
            .finally(this.onGetUserExhibitorsFinally.bind(this))
    }

    onGetUserExhibitorsSucceeded(response) {
        this.exhibitors = response;

        this.filteredExhibitors = this.exhibitors;
    }

    onGetUserExhibitorsFailed(error) {
        this.loading.error = error && error.validation
            ? error.validation
            : this.INTERNAL_ERROR_MSG;
    }

    onGetUserExhibitorsFinally() {
        this.loading.inProcess = false;
    }

    onSearch(search = this.search) {
        if (!this.search) {
            this.search = search;
        }

        search = search.toLowerCase();

        if (!search) {
            this.filteredExhibitors = this.exhibitors;
        } else {
            this.filteredExhibitors = this.exhibitors.filter(exhibitor => {
                return exhibitor.first.toLowerCase().indexOf(search) !== -1
                    || exhibitor.last.toLowerCase().indexOf(search) !== -1
                    || exhibitor.company_name.toLowerCase().indexOf(search) !== -1
                    || exhibitor.email.toLowerCase().indexOf(search) !== -1
            });
        }
    }

    onCreate() {
        this.service.openCreateExhibitorModal(this.onSearch.bind(this))
            .then(withReload => {
                if (withReload) {
                    this.loadExhibitors();
                }
            })
    }
}

angular.module('SportWrench').component('userExhibitors', {
    templateUrl: 'events/dashboard/exhibitors/user-exhibitors/user-exhibitors.html',
    controller: [
        '$stateParams',
        'ExhibitorsService',
        'INTERNAL_ERROR_MSG',
        UserExhibitorsComponent
    ]
});