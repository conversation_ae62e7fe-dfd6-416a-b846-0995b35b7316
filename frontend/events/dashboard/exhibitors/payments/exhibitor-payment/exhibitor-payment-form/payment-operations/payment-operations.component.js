class PaymentOperationsComponent {
    constructor(PAYMENT_TYPE, PAYMENT_STATUS, ExhibitorsService, $filter) {
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.PAYMENT_STATUS = PAYMENT_STATUS;
        this.service = ExhibitorsService;
        this.$filter = $filter;

        this.enterCheck = false;
        this.operationInProcess = false;
    }

    $onInit() {
        this.enterCheck = Boolean(this.checkNumber && this.datePaid);
    }

    get isCheck() {
        return this.type === this.PAYMENT_TYPE.CHECK;
    }

    get isCard() {
        return this.type === this.PAYMENT_TYPE.CARD;
    }

    get isPaid() {
        return this.status === this.PAYMENT_STATUS.PAID;
    }

    get isCanceled() {
        return this.status === this.PAYMENT_STATUS.CANCELED;
    }

    get isPending() {
        return this.status === this.PAYMENT_STATUS.PENDING;
    }

    disableReceivedCheckbox() {
        return this.isCanceled;
    }

    disableReceivedFields() {
        return !this.enterCheck || this.isCanceled;
    }

    setReceivedDate() {
        if (this.datePaid) {
            return;
        }

        this.datePaid = new Date();
    }

    showSaveButton() {
        return this.isPending;
    }

    showVoidButton() {
        return this.isPaid && this.isCheck;
    }

    showRefundButton() {
        return this.isPaid && this.isCard;
    }

    showInvoiceLink() {
        return this.purchaseId;
    }

    makeRefund() {
        if (this.operationInProcess) {
            return;
        }

        this.operationInProcess = true;

        this.service.makeRefund(this.purchaseId, {
            date_refunded: new Date(),
        }, this.eventId)
            .then(this.onOperationSucceeded.bind(this))
            .finally(this.onOperationFinally.bind(this));
    }

    makeVoid() {
        if (this.operationInProcess) {
            return;
        }

        this.operationInProcess = true;

        this.service.makeVoid(this.purchaseId, {
            date_canceled: this.datePaid,
        }, this.eventId)
            .then(this.onOperationSucceeded.bind(this))
            .finally(this.onOperationFinally.bind(this));
    }

    makeReceive() {
        if (this.operationInProcess) {
            return;
        }

        this.operationInProcess = true;

        this.service.makeReceive(this.purchaseId, {
            date_paid: this.datePaid,
            check_num: this.checkNumber,
        }, this.eventId)
            .then(this.onOperationSucceeded.bind(this))
            .finally(this.onOperationFinally.bind(this));
    }

    onOperationSucceeded() {
        this.onModalClose();
    }

    onOperationFinally() {
        this.operationInProcess = false;
    }
}

angular.module('SportWrench').component('paymentOperations', {
    templateUrl: 'events/dashboard/exhibitors/payments/exhibitor-payment/exhibitor-payment-form/payment-operations/payment-operations.html',
    bindings: {
        type: '<',
        status: '<',
        checkNumber: '<',
        purchaseId: '<',
        datePaid: '<',
        onModalClose: '&',
        eventId: '<',
    },
    controller: [
        'PAYMENT_TYPE',
        'PAYMENT_STATUS',
        'ExhibitorsService',
        '$filter',
        PaymentOperationsComponent
    ],
})
