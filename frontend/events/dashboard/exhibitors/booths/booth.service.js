angular.module('SportWrench').service('BoothService', BoothService);

function BoothService ($http) {
	this.$http 		= $http;
	this.prefix 	= '/api/event/';
	this.resource 	= '/booth/';
}

BoothService.prototype.getAll = function (eventId) {
	return this.$http.get(this.prefix + eventId + this.resource);
};

BoothService.prototype.remove = function (eventId, boothId) {
	return this.$http.delete(this.prefix + eventId + this.resource + boothId);
};

BoothService.prototype.getBooth = function (eventId, boothId) {
	return this.$http.get(this.prefix + eventId + this.resource + boothId);
};

BoothService.prototype.update = function (eventId, boothId, booth) {
	return this.$http.put(this.prefix + eventId + this.resource + boothId, booth);
};

BoothService.prototype.create = function (eventId, booth) {
	return this.$http.post(this.prefix + eventId + this.resource, booth);
};
