angular.module('SportWrench').component('eventTransferForm', {
	templateUrl : 'events/dashboard/transfers/transfer-form/form.html',
	bindings 	: {
		fundsList 	: '<',
		onSubmit  	: '&'
	},
	controller: ['toastr', EventTransferFormController]
})

function EventTransferFormController (toastr) {

	this.data = {};
    this.payment_errors = [];

	this.pending = false;

	if (this.fundsList.length === 1) {
		this.data.fund = this.fundsList[0];
	}

	this.ctrlClass = function (name) {
		return {
			'form-group' 	: true,
			'has-error'  	: this.trForm.$submitted && this.trForm[name].$invalid
		}
	}

	this.submit = function () {
		if (this.trForm.$invalid) {
			toastr.warning('Invalid Form Data');
			return;
		}

		if (this.pending) {
			return;
		}

		this.pending = true;

		this.onSubmit({
			data: {
				currency 	: this.data.fund.currency,
				amount 	 	: this.data.amount,
				notes 		: this.data.notes
			}
		}).catch((error) => {
            const errorMsg =
                error && error.validation
                    ? error.validation
                    : error && error.data && error.data.validation
                        ? error.data.validation
                        : 'Internal Server Error. Please, try again later.';
            
            this.payment_errors.push(errorMsg);
        }).finally(function () {
			this.trForm.$setPristine();
			this.pending = false;
		}.bind(this));
	}

	this.getMaxAmount = function () {

	}
    
    this.closeAlert = function (i) {
        this.payment_errors.splice(i, 1)
    }
}
