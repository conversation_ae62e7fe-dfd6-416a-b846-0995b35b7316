<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">{{::$ctrl.headerTitle}}</h3>
    </div>
    <div class="panel-body">
        <div class="row" ng-if="$ctrl.showCreditNetProfit()">
            <div class="col-xs-7 font-bold">Credit Net Profit <small>(Total - Merchant Fees - SW{{($ctrl.isTeamsType() ? ' teams' : '')}} fees)</small></div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.credit_net_profit)"></div>
        </div>
        <div class="row" ng-if="$ctrl.showEscrow()">
            <div class="col-xs-7 font-bold">Escrow</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.escrow)"></div>
        </div>
<!--         <div class="row">
            <div class="col-xs-7 font-bold">Lost Disputes Penalty <span ng-if="$ctrl.hasLostDisputes()">({{$ctrl.stats.lost_disputes.qty}} * {{$ctrl.trCtrl.getAmount('$', $ctrl.stats.lost_disputes.fee)}})</span></div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.trCtrl.getAmount('', $ctrl.stats.lost_disputes.penalty)"></div>
        </div> -->
        <div class="row" ng-if="$ctrl.showEscrowToReturn()">
            <div class="col-xs-7 font-bold">
                Escrow to be credited to EO in 4 months 
                <i class="fa fa-info-circle text-info pointer" 
                    uib-tooltip="Escrow - Lost Disputes Penalty, Failed ACH Payments Penalty"
                    tooltip-trigger="click" 
                    tooltip-append-to-body="true"
                ></i>
            </div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.escrow_to_return)"></div>
        </div>
        <div class="row" ng-if="$ctrl.showCreditTemporaryProfit()">
            <div class="col-xs-7 font-bold">Credit Temporary Profit (less escrow)</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.net_profit)"></div>
        </div>
        <div class="row" ng-if="$ctrl.showStripeToBankPayouts()">
            <div class="col-xs-7 font-bold">Stripe to Bank Payouts</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.bank_transfered)"></div>
        </div>
        <div class="row" ng-if="$ctrl.showStripeAutoPayouts()">
            <div class="col-xs-7 font-bold">Stripe Auto Payouts</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.auto_transfered)"></div>
        </div>
        <div class="row" ng-if="!$ctrl.isTilledPaymentProvider">
            <div class="col-xs-7 font-bold">Pending Payout Availability</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.not_available_net_profit)"></div>
        </div>
        <div class="row" ng-if="!$ctrl.isTilledPaymentProvider">
            <div class="col-xs-7 font-bold">Available to Payout</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.available_to_transfer)"></div>
        </div>

        <button class="btn btn-primary" ng-click="$ctrl.openModal()" ng-if="$ctrl.showMakeTrBtn()">Make Payout</button>

        <p class="text-danger" ng-if="!$ctrl.showMakeTrBtn()">{{$ctrl.getPayoutDescription()}}</p>
    </div>
</div>
