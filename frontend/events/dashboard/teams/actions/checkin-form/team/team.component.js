angular.module('SportWrench').component('checkinTeam', {
	templateUrl: 'events/dashboard/teams/actions/checkin-form/team/team.html',
	bindings: {
		team: '<'
	},
	controller: function () {
		var self = this;

		this.teamClass = function () {
			return {
				'gl-success fa fa-check text-success' 	          : (self.team.status_checkin === 'checkedin'),
				'gl-danger fa fa-times text-danger' 	          : (self.team.status_checkin === 'notcheckedin'),
				'gl-info fa fa-clock-o text-primary' 	          : (self.team.status_checkin === 'pending'),
                'gl-danger fa fa-exclamation-circle text-primary' : (self.team.status_checkin === 'alert')
			};
		};

	}
});
