<div class="row earned-bids-form" ng-if="$ctrl.show">
    <div class="col-xs-3">
        <div class="checkbox">
            <label>
                <input ng-model="$ctrl.team.show_accepted_bid" type="checkbox"> Bid Accepted
            </label>
        </div>
    </div>
    <div class="col-xs-3 col-xs-offset-1" align="right">
        <div class="checkbox">
            <label>
                <input ng-model="$ctrl.team.prev_qual" type="checkbox"> Prev Qualified
            </label>
        </div>
    </div>
    <div ng-class="{ 'col-xs-2': true, 'has-error': !$ctrl.team.prev_qual_age && $ctrl.team.prev_qual }" align="right">
        <select class="form-control"
                ng-model="$ctrl.team.prev_qual_age"
                ng-disabled="!$ctrl.team.prev_qual"
        >
            <option value="" selected>Age...</option>
            <option value="10">10</option>
            <option value="11">11</option>
            <option value="12">12</option>
            <option value="13">13</option>
            <option value="14">14</option>
            <option value="15">15</option>
            <option value="16">16</option>
            <option value="17">17</option>
            <option value="18">18</option>
        </select>
    </div>
    <div ng-class="{ 'col-xs-3': true, 'has-error': !$ctrl.team.prev_qual_division && $ctrl.team.prev_qual }">
        <select class="form-control"
                ng-model="$ctrl.team.prev_qual_division"
                ng-disabled="!$ctrl.team.prev_qual"
                ng-options="d.id as d.title for d in $ctrl.divisions"
        >
            <option value="" selected>Division...</option>
        </select>
    </div>
</div>
<div class="row">
    <div class="col-sm-3 control-label pull-right">
        <a target="_blank" href="{{$ctrl.getStandingPageLink()}}">Standings Page</a>
    </div>
    <div ng-if="$ctrl.show" ng-class="{ 'col-sm-4 pull-right mt-5': true, 'has-error': !$ctrl.team.earned_at && $ctrl.team.prev_qual }">
        <input
            class="form-control"
            type="text"
            name="earned_at"
            placeholder="Earned At"
            ng-model="$ctrl.team.earned_at"
            ng-disabled="!$ctrl.team.prev_qual"
        >
    </div>
</div>
