angular.module('SportWrench').controller('Events.TeamInfo.TabRosterController', TabRosterController);

function TabRosterController (
    $scope, $window, $http, toastr, RosterTeamMembersService, $q, ConfirmationService, ClubCheckinService, userService, CHECKIN_MODES,
    TEAM_ENTRY_STATUS, SANCTIONING_BODY
) {
    $scope.athletes             = [];
    $scope.staff                = [];
    $scope.loading              = { data_loaded: false, error: false, errMsg: '' };


    $scope.wristbands           = {};
    $scope.utils                = {
        isAdditionMenuOpen  : false,
        isDoublesEvent      : false
    }
    $scope.rosterValidation     = {};
    $scope.team                 = {};
    $scope.staffRolesAllowed    = $scope.$parent.event.staff_roles_allowed || {};

    $scope.hasGodRole = function () {
        return userService.hasGodRole();
    }

    $scope.isWithManualTeams = $scope.$parent.event.is_with_manual_teams_addition;
    $scope.eventHas9ManSanctioning = $scope.$parent.event.sport_sanctioning_id === SANCTIONING_BODY.NINE_MAN;
    $scope.eventHasAAUSanctioning = $scope.$parent.event.sport_sanctioning_id === SANCTIONING_BODY.AAU;

    $scope.$parent.$watch('tabs.team', function (value) {
        if(_.isEmpty(value)) return; 
        $scope.team = value;
        _getRosters(value);
    });

    $scope.notEmpty = function (obj) {
        return !_.isEmpty(obj)
    }

    $scope.printRoster = function () {
        RosterTeamMembersService.printRoster($scope.$parent.modalParams.event_id, $scope.$parent.modalParams.team_id)
    }

    $scope.toggleAdditionMenu = function () {
        this.utils.isAdditionMenuOpen = !this.utils.isAdditionMenuOpen
    }

    $scope.rosterChanged = function () {
        if($scope.isWithManualTeams) {
            $scope.toggleAdditionMenu();
        }

        _getRosters(this.team);
    }

    var __onMemerRemoved = function () {
        toastr.success('Successfully removed!')
        _getRosters($scope.team)
    }

    var __removeMember = function (id, type, additionalAskText) {
        ConfirmationService.ask(
            `${additionalAskText ? additionalAskText : ''} 
            Do you really want to remove this Member from "${$scope.team.team_name}"?`
        ).then(function (resp) {
            return (resp === ConfirmationService.YES_RESP)
                ?(RosterTeamMembersService.removeMember(
                    $scope.$parent.modalParams.event_id, 
                    $scope.team.roster_team_id, id, type
                 ).then(__onMemerRemoved)
                ):false;
        });
    };

    $scope.removeAthlete = function (id) {
        __removeMember(id, 'athlete')
    }

    $scope.updateAthlete = function (athlete) {
       return __updateAthlete(athlete);
    }

    $scope.removeStaffer = function (id, additionalAskText) {
        __removeMember(id, 'staff', additionalAskText)
    }

    $scope.wristBandsDefined = function () {
        return !_.isEmpty($scope.wristbands)
    }

    $scope.updateStaffer = function (stafferId, staffer) {
        var eventId = $scope.$parent.modalParams.event_id,
            teamId  = $scope.team.roster_team_id;
            
        return RosterTeamMembersService.updateStaffer(
            eventId, teamId, stafferId, staffer
        ).then(function (result) {
            const resultData = result && result.data;

            if(resultData.checked_in_online) {
                toastr.success('Staffer checked in online with current team');
            }

            if(resultData.checkin_email_notification_sent) {
                toastr.success('Online Checkin barcode sent to staffer');
            }

            $scope.wristbands.staff = resultData.wristbands || 0;
            __validateRoster();
        })
    }

    $scope.sendBarcode = function (staffID, data) {
        let teamID  = $scope.team.roster_team_id;

        return ClubCheckinService.resendPrimaryStaffEmail(teamID, staffID, data);
    }

    $scope.unlock = function () {
        var eventId = $scope.$parent.modalParams.event_id,
            teamId  = $scope.team.roster_team_id;

        return RosterTeamMembersService.unlockTeamRoster(eventId, teamId).then(function () {
            toastr.success('Unlocked');
            $scope.team.locked = false;
            $scope.team.online_checkin_date = null;
            $scope.$emit('team.info.data-changed');
        });
    }

    $scope.lock = function () {
        var eventId = $scope.$parent.modalParams.event_id,
            teamId  = $scope.team.roster_team_id;

        return RosterTeamMembersService.lockTeamRoster(eventId, teamId).then(function () {
            toastr.success('Locked');
            $scope.team.locked = true;
            $scope.$emit('team.info.data-changed');
        }, function (err) {
            console.log(err);
        });
    }

    $scope.lockMessage = function () {
        var message = 'Do you really want to lock roster for selected team?';
        return _.isEmpty($scope.rosterValidation)
            ? message
            : 'Team has roster validation errors. ' + message;
    }
    
    $scope.changeRosterValidation = function () {
        let eventID = $scope.$parent.modalParams.event_id;
        let teamID  = $scope.team.roster_team_id;

        $scope.disableValidationChange = true;

        if($scope.team.validated_by_eo) {
            return RosterTeamMembersService.markRosterValid(eventID, teamID)
                .then(() => onRosterValidationChange());
        } else {
            return RosterTeamMembersService.removeRosterValidMark(eventID, teamID)
                .then(() => onRosterValidationChange());
        }

        function onRosterValidationChange() {
            $scope.$emit('team.info.data-changed');
            $scope.disableValidationChange = false;
            toastr.success('Success!');
            __validateRoster();
        }
    };

    $scope.onlineCheckin = function() {
        let eventID = $scope.$parent.modalParams.event_id;
        let teamID  = $scope.team.roster_team_id;

        if ($scope.team.online_team_checkin_mode === CHECKIN_MODES.DEFAULT) {
            return RosterTeamMembersService.openStaffersListModal(eventID, teamID)
                .then(onCheckinSuccess)
        }

        return RosterTeamMembersService.checkInTeam(eventID, teamID)
            .then(onCheckinSuccess)
    };

    $scope.isAllowOnlineCheckin = function() {
        const rules = [
            $scope.team.online_team_checkin_available,
            !$scope.team.event_is_ended,
            $scope.team.status_entry === TEAM_ENTRY_STATUS.ACCEPTED,
        ];

        return rules.every(rule => rule);
    }

    function onCheckinSuccess() {
        toastr.success('Team successfully check-in!');
        $scope.team.locked = true;
        $scope.$emit('team.info.data-changed');
    }

    function _getRosters (team) {
        RosterTeamMembersService.getMembers($scope.$parent.modalParams.event_id, team.roster_team_id)
        .then(function (resp) {
            var data = resp && resp.data;
            if(data) {
                $scope.reg_method           = $scope.$parent.event && $scope.$parent.event.registration_method;  
                $scope.utils.isDoublesEvent = ($scope.reg_method === 'doubles');
                $scope.athletes             = data.athletes || [];            
                $scope.staff                = data.staff    || [];
                $scope.wristbands           = data.wristbands;
                $scope.rosterValidation     = data.rosterValidation;
            }
            $scope.loading.data_loaded = true;
        })
        .catch(function (err) {
            $scope.loading.data_loaded = true;
            $scope.loading.error = true;
            err.data && err.data.validation
                ? $scope.loading.errMsg = err.data.validation
                : $scope.loading.errMsg = 'Internal Server Error.'
        });
    }

    function __validateRoster () {
        return RosterTeamMembersService.validateRoster(
            $scope.$parent.modalParams.event_id, $scope.team.roster_team_id
        ).then(function (resp) {
            $scope.rosterValidation = resp.data && resp.data.rosterValidation;
        })
    }

    function __updateAthlete(athlete) {
        var eventId = $scope.$parent.modalParams.event_id,
            teamId  = $scope.team.roster_team_id;

        return RosterTeamMembersService.updateAthlete(eventId, teamId, athlete)
            .then(__validateRoster)
    }
}
