
class Controller {
    constructor (rosterTeamService, $stateParams) {
        this.rosterTeamService = rosterTeamService;
        this.eventID = $stateParams.event;
    }

    updateRoster (member) {
        let memberData = Object.assign({}, member, _.pick(this.team, 'master_team_id', 'roster_team_id'));

        return this.rosterTeamService.addRosterManually(this.eventID, memberData)
            .then(() => {
                this.onRosterChange();
            })
    }
}

Controller.$inject = ['rosterTeamService', '$stateParams'];

angular.module('SportWrench').component('createMemberManually', {
    templateUrl: 'events/dashboard/team-info/create-member-manually/template.html',
    bindings: {
        onRosterChange: '&',
        team: '<'
    },
    controller: Controller
});
