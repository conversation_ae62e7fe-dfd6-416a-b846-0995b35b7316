<uib-alert type="danger" ng-if="loading.error">{{ loading.errMsg }}</uib-alert>
<div ng-if="loading.data_loaded && !loading.error">
    <div class="row" >
        <div class="col-sm-12">
            <p class="lead">Club Staff</p>
            <div class="rowm0 row" ng-repeat="staff in data.club_staff">
                <span class="label label-info">{{::staff.role_name}}</span>
                <genders 
                m="staff.gender === 'male'"
                f="staff.gender === 'female'"
                ></genders>
                <span ng-if="staff.gender">&nbsp;</span>
                <b>{{::staff.first}} {{::staff.last}}</b>
                <span ng-if="s.checkin_description_link" ng-click="$event.stopPropagation()">
                        <a ng-click="openStaffQRCodePage(s)" href="">QR Code</a>
                    </span>
                <br/>
                <a href="" ng-click="confirm.email = staff.email;confirm.show=true">{{::staff.email}}</a>
                <br/>
                {{::staff.phone | tel}}
            </div>
        </div>
    </div>
    <div class="row row-space">
        <div class="col-sm-8">
            <div class="col-sm-4 text-right center-form-text">Sort order:</div>
            <div class="col-sm-6">
                <select class="form-control" ng-model="data.sort">
                    <option value="organization_code">Team code</option>
                    <option value="age">Age</option>
                    <option value="team_name">Name</option>
                </select>
            </div>
        </div>           
    </div>
    <p class="text-center">
        <small>Tap on "eye" to reload popup with this team. Tap on team name to show staff.</small>
    </p>
    <uib-accordion>
        <uib-accordion-group
            class="row-space payments-list--sm"
            ng-repeat="team in data.teams | orderBy:[data.sort]">
            <uib-accordion-heading>
                <a href="" class="block-inline pull-left" data-ng-click="goToModal(team.roster_team_id)">
                    <i class="fa fa-eye pull-right"></i>
                </a>
                &nbsp;
                <a href="" class="block-inline">
                <b>{{::team.team_name}}</b> - {{::team.organization_code}} - {{::team.age}} - {{::team.division_name}}
                </a>
            </uib-accordion-heading>
           <div class="team-staffers-list-wrapper">
               <team-staffers-list
                   team-id="team.roster_team_id"
                   staff="team.staff"
                   remove="removeStaffer(id, additionalAskText, team)"
                   update-staffer="updateStaffer(id, staffer, team)"
               >
               </team-staffers-list>
           </div>
        </uib-accordion-group>
    </uib-accordion>
</div>
<spinner active="!loading.data_loaded"></spinner>
