<aem-initial-template-creation 
	ng-if="showFirstStep()"
	on-close="closeModal()"
	basic-layouts="basicLayouts"
	tmpl-groups="templateGroups"
	on-submit="createTemplate(data)"
	tmpl-role="userRole"
    load-group-types="loadGroupTypes(group)"
	>
</aem-initial-template-creation> 
<aem-template-updating
	ng-if="showSecondStep()"
	on-close="closeModal()"
	update-template="updateTemplate(data)"
	send-template="sendTemplate(html, subject, receiver)"
	tmpl-id="templateId"
	bee-tmpl="beeJSON"
	tmpl="templateJSON"
	tmpl-html="templateHTML"
	tmpl-type="currentTemplateType"
	tmpl-title="currentTemplateTitle"
	tmpl-subject="currentTemplateSubject"
	tmpl-group="currentTemplateGroup"
	on-editor-loaded="onEditorLoaded()"
    save-to-storage="saveTemplateToLocalStorage(json)"
	>
</aem-template-updating>
