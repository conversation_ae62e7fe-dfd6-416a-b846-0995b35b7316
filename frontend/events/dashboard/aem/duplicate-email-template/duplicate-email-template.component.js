angular.module('SportWrench').component('duplicateEmailTemplate', {
    templateUrl: 'events/dashboard/aem/duplicate-email-template/duplicate-email-template.html',
    bindings: {
        onClose     : '&',
        onEdit: '&',
        onLoadData: '&',
        template: '<',
    },
    controller: Component,
});

Component.$inject = ['AEMEventFactory', 'AEMService', '$stateParams', 'INTERNAL_ERROR_MSG', 'toastr'];

function Component(AEMEventFactory, AEMService, $stateParams, INTERNAL_ERROR_MSG, toastr) {
    this.currentEventSelected = false;

    this.loading = {
        inProcess: true,
        success: false,
        error: false,
        errorMessage: '',
    };

    this.duplicateInProcess = false;

    this.$onInit = () => {
        this.eventID = Number($stateParams.event);
        this.AEMEventService = new AEMEventFactory(this.eventID);

        AEMService.getEvents(this.eventID, 'onlyEvents=true')
            .then(this.onGetEventsSuccess, this.onGetEventsFail)
            .finally(() => {
                this.loading.inProcess = false;
            });
    };

    this.duplicate = () => {
        if (this.duplicateInProcess) {
            return;
        }

        const eventIds = this.eventsWithoutCurrent
            .filter(event => event.selected)
            .map(event => event.event_id);

        if (this.currentEventSelected) {
            eventIds.push(this.eventID);
        }

        if (!eventIds.length) {
            this.loading.errorMessage = 'No Event(s) selected';

            return;
        }

        this.loading.errorMessage = '';
        this.duplicateInProcess = true;

        this.AEMEventService.duplicateTemplate(this.template.id, eventIds)
            .then(this.onDuplicateSuccess)
            .finally(() => {
                this.duplicateInProcess = false;
            })
    };

    this.onGetEventsSuccess = (response) => {
        this.loading.success = true;

        const { events } = response.data;

        const indexOfCurrentEvent = events.findIndex(event => event.event_id === this.eventID);

        this.eventsWithoutCurrent = angular.copy(events);
        this.eventsWithoutCurrent.splice(indexOfCurrentEvent, 1);
    };

    this.onGetEventsFail = (error) => {
        this.loading.error = true;

        this.loading.errorMessage = (error.data && error.data.validation)
            ? error.data.validation
            : INTERNAL_ERROR_MSG;
    };

    this.onDuplicateSuccess = ({ data }) => {
        if (data.template) {
            this.onEdit({ item: data.template });
        } else {
            toastr.success('Template(s) successfully duplicated!');
        }

        this.onLoadData();
    }
}
