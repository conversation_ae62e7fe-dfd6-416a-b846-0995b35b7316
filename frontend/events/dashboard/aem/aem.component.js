angular.module('SportWrench').component('aem', {
	templateUrl 	: 'events/dashboard/aem/aem.html',
	bindings 		: {
		baseState 	 	: '@'
	},
	controller 		: ['APP_ROUTES', '$state', '$rootScope', AEMController]
});

function alwaysVis () {
	return true;
}

function redirectFromBase ($state, baseState, redirectTo) {

	if ($state.is(baseState)) {
		$state.go(redirectTo.state);
	}

}

function AEMController (APP_ROUTES, $state, $rootScope) {
    let self = this;

    this.list = {};

	this.tabs = _.map([
		{ name: 'Templates'     , state: APP_ROUTES.EO.EMAIL_MODULE_TMPLS  },
        { name: 'Contact Lists' , state: APP_ROUTES.EO.EMAIL_CONTACT_LISTS },
		// { name: 'Send' 		, state: APP_ROUTES.EO.EMAIL_MODULE_SEND }
        {
            name        : _listName,
            state       : APP_ROUTES.EO.EMAIL_CONTACT_LIST,
            isVisible   : _listEditVisible,
            stateParams : _listStateParams
        }
	], function (item) {
	    if(_.isUndefined(item.isVisible)) {
            item.isVisible = alwaysVis;
        }

		return item;
	});

	function _listName () {
	    return 'List: ' + self.list.title;
    }

    function _listEditVisible () {
	    return !_.isEmpty(self.list);
    }

    function _listStateParams () {
	    return { event: self.list.event_id, list: self.list.list };
    }

	// THINK: maybe need to store filters' data here

	// redirectFromBase($state, this.baseState, this.tabs[0]);

    $rootScope.$on('LIST_EDIT', function (ev, list) {
        self.list = list;
    });

    $rootScope.$on('LIST_REMOVED', function (ev) {
        self.list = undefined;
    });

}
