angular.module('SportWrench').component('aemTemplatesVariables', {
	templateUrl: 'events/dashboard/aem/variables-block/variables-block.html',
	bindings: {
        variablesLoader: '&'
	},
	controller: ['AEMService', AEMTemplatesVariablesController]
});

function AEMTemplatesVariablesController () {
	this.isLoading 	= false;
	this.isInitLoad = true;
	this.variables 	= [];


	this.$onInit = function () {
        this.loadVariables();
    }

	this.loadVariables = async function () {
		if (this.isLoading) {
			return;
		}

		this.isLoading = true;

		try {
            this.variables = await this.variablesLoader();
            if (this.isInitLoad) {
                this.isInitLoad = false;
            }
        } finally {
			this.isLoading = false;
		}
	}
}
