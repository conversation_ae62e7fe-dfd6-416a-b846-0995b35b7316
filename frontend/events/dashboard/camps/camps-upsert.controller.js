angular
    .module('SportWrench')
    .controller('CampsUpsertController', CampsUpsert);

CampsUpsert.$inject = ['$stateParams',
    '$state',
    '$uibModalInstance',
    '$scope',
    '$rootScope',
    'campsService',
    'toastr',
    'DateService',
    'camp',
    'mode'
];

function CampsUpsert($stateParams,
    $state,
    $uibModalInstance,
    $scope,
    $rootScope,
    campsService,
    toastr,
    DateService,
    camp,
    mode) {

    var vm = this;

    activate();

    /////////////////////////

    function activate() {
        $scope.eventId  = $stateParams.event;
        $scope.camp     = camp;
        $scope.title    = (mode === campsService.CAMP_EDIT_MODE.EDIT) ? 'Edit' : 'Create';

        if ($scope.title === 'Create') {
            $scope.camp.id = 0;
            $scope.camp.age_from = null;
            $scope.camp.age_to = null;
            $scope.camp.date_start = '';
            $scope.camp.date_end = null;
            $scope.camp.description = '';
            $scope.camp.event_id = $scope.eventId;
            $scope.camp.name = '';
            $scope.camp.order = 'label';
            $scope.camp.purchase_start = null;
            $scope.camp.purchase_end = null;
            $scope.camp.age_date = null;
            $scope.camp.visibility = 'published';
        } else {
           $scope.camp.date_start = DateService.normalizeStr($scope.camp.date_start, true);
           $scope.camp.date_end =  DateService.normalizeStr($scope.camp.date_end, true);
           $scope.camp.age_date = DateService.normalizeStr($scope.camp.age_date, true);
           $scope.camp.purchase_start = DateService.normalizeStr($scope.camp.purchase_start, true);
           $scope.camp.purchase_end = DateService.normalizeStr($scope.camp.purchase_end, true);
        }
    };

    $scope.save = function () {
        campsService.upsertCamp($scope.eventId, $scope.camp)
            .then(camp => {
                if ($scope.camp.id > 0) {
                    toastr.success('Camp Successfully Updated');
                    $rootScope.$emit('campUpdated', camp);
                } else {
                    toastr.success('Camp Successfully Created');
                    $rootScope.$emit('campCreated', camp);
                }
                $scope.$dismiss();
            });
    };

    $scope.setCampAgeDateTo = function (dt) {
        $scope.camp.age_date = dt;
    };

    $scope.isAgeDateEqualTo = function(dt) {
        return angular.equals($scope.camp.age_date, dt);
    };

    $scope.delete = function () {
        campsService.delete($scope.eventId, $scope.camp.id)
            .then(() => {
                $rootScope.$emit('campUpdated', $scope.camp, true);
                toastr.success('Camp Successfully Deleted');
                $scope.$dismiss();
            });
    }

    $scope.duplicate = function () {
        campsService.copy($scope.eventId, $scope.camp.id)
            .then(camp => {
                $rootScope.$emit('campCreated', camp);
                toastr.success('Camp Successfully Copied');
                $scope.$dismiss();
            });
    }
}
