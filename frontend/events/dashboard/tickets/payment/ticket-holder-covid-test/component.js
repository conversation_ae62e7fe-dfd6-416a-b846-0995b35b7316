
class Controller {

    constructor (ticketsService, $stateParams, toastr) {
        this.ticketsService = ticketsService;
        this.eventID = $stateParams.event;
        this.toastr = toastr;
    }

    $onInit () {
        let ticket = this.tickets && this.tickets[0] && this.tickets[0];
        this.hasCovidTest = ticket.has_covid_test;
    }

    updateCovidTest () {
        this.isLoading = true;

        let reset = !!this.hasCovidTest;

        this.ticketsService.updateCovidTest(this.eventID, this.barcode, reset)
            .then(response => {
                let value = response.value;

                this.hasCovidTest = value;

                this.onUpdate({ value });

                this.toastr.success('Success');
            }).finally(() => this.isLoading = false);
    }

    buttonText () {
        return this.hasCovidTest ? 'Reset' : 'Set Valid';
    }
}

Controller.$inject = ['ticketsService', '$stateParams', 'toastr'];

angular.module('SportWrench').component('ticketHolderCovidTest', {
    templateUrl: 'events/dashboard/tickets/payment/ticket-holder-covid-test/template.html',
    bindings: {
        tickets: '<',
        barcode: '<',
        onUpdate: '&'
    },
    controller: Controller
});
