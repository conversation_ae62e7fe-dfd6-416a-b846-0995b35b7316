angular.module('SportWrench').directive('waitlistItems', ['ticketsService', '$stateParams', waitlistItems]);

function waitlistItems (ticketsService, $stateParams) {
	return {
		restrict 		: 'E',
		scope 			: {},
		templateUrl 	: 'events/dashboard/tickets/payment/waitlist/items.html',
		require 		: '^paymentModal',
		link: function (scope, elem, attrs, ctrl) {
			scope.payment = ctrl.getPayment();

			scope.moveToPending = function () {
				ticketsService.waitlist.toPending($stateParams.event, scope.payment.barcode)
				.then(getUpdatedPayment)
			}

			scope.cancel = function () {
				ticketsService.waitlist.cancel($stateParams.event, scope.payment.barcode)
				.then(getUpdatedPayment)
			}

			scope.showOpenRegBtn = function () {
				return !scope.payment.status || (scope.payment.status === 'canceled')
			}

			function getUpdatedPayment () {
				return ctrl.reloadPayment(true)
				.then(function () {
					scope.payment = ctrl.getPayment();
				})
			}
		}
	}
}
