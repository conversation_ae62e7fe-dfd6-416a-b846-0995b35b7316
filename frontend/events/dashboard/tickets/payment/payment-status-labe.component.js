angular.module('SportWrench')
.component('paymentStatusLabel', {
    templateUrl: 'events/dashboard/tickets/payment/payment-status-label.html',
    bindings: {
        payment: '<',
    },
    controller: [paymentStatusLabel],
});

function paymentStatusLabel() {
    const statusClassSet = {
        paid: 'text-success',
        pending: 'text-info',
        canceled: 'text-danger',
    };

    this.statusClass = function () {
        return statusClassSet[this.payment.status];
    };
}
