angular.module('SportWrench').component('deactivateButton', {
    templateUrl: 'events/dashboard/tickets/payment/deactivate-ticket-barcode/deactivate-button/deactivate-button.html',
    bindings: {
        action: '<',
        onClick: '&'
    },
    controller: Component,
});

Component.$inject = ['ACTIVATE', 'DEACTIVATE', 'UtilsService'];

function Component(ACTIVATE, DEACTIVATE, UtilsService) {
    this.getText = () => {
        return UtilsService.capitalizeFirstLetter(this.action);
    };

    this.getClass = () => {
        return {
            'btn-danger': this.action === DEACTIVATE,
            'btn-success': this.action === ACTIVATE,
        }
    }
}
