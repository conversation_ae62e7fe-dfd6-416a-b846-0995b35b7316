<table class="table table-condensed">
    <thead>
        <tr>
            <th></th>
            <th>Purchased</th>
            <th ng-if="scanAvailable">Available</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat-start="t in list">
            <td>{{t.label}} ({{t.price_formatted}})</td>
            <td>{{t.quantity}}</td>
            <td ng-if="scanAvailable">{{t.available}}</td>
        </tr>
        <tr sub-row ng-repeat-end ng-if="t.discount" ticket="t" cols="{{(hasScan)?3:2}}" class="remove-border"></tr>
    </tbody>
</table>