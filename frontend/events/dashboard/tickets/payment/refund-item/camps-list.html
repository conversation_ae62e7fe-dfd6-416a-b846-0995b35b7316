<table class="table table-condensed">
    <thead>
        <tr>
            <th>Camp Name</th>
            <th>Type</th>
            <th ng-if="scanAvailable">Available</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat-start="t in list" ng-class="{ 'striked-out': isCanceledCamp(t) }">
            <td>{{t.camp_name}}</td>
            <td>{{t.label}} ({{t.price_formatted}})</td>
            <td ng-if="scanAvailable">{{t.available}}</td>
            <td ng-if="showCancelButton(t)">
                <a sw-confirm="Do you really want to cancel selected camp?"
                   sw-confirm-do="cancelCampParticipation"
                   sw-confirm-args="t"
                   sw-confirm-hide-no
                   class="cancel-participation-btn"
                >Cancel</a>
            </td>
        </tr>
        <tr sub-row ng-repeat-end ng-if="t.discount || t.cancellation" ticket="t" cols="{{(hasScan)?3:2}}" class="remove-border">
        </tr>
    </tbody>
</table>
