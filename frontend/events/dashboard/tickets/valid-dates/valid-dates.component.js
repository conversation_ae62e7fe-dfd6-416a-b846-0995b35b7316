angular.module('SportWrench').component('validDates', {
    templateUrl 	: 'events/dashboard/tickets/valid-dates/valid-dates.html',
    bindings 		: {
        ticketName: '<',
        validDates: '<',
        onSave    : '&',
        eventDays : '<',
        close     : '&',
        disabled  : '<'
    },
    controller      : ValidDatesController
});

function ValidDatesController() {
    this.allDays = {
        selected: false
    };

    this.error = null;

    this.$onInit = function () {
        this.days = __filterNotExistedDates(this.validDates, this.eventDays);

        if(_.isEmpty(this.validDates)) {
            this.allDays.selected = true;
        }
    }

    this.toggleAll = function () {
        this.error = null;

        if(this.allDays.selected) {
            this.days = {};
        }
    }

    this.onCheckboxChange = function (day) {
        this.error = null;

        if(this.allDays.selected && Object.keys(this.days).length) {
            this.allDays.selected = false;
        }

        //delete unchecked day from object
        if(_.isBoolean(this.days[day]) && !this.days[day]) {
            delete this.days[day];
        }
    }

    this.submit = function () {
        let daysArray = Object.keys(this.days);

        if(!this.allDays.selected && !daysArray.length) {
            this.error = 'At least one checkbox should be selected';
        } else {
            this.onSave({ validDates: daysArray });
        }
    }
    
    function __filterNotExistedDates (validDays, eventDays) {
        let filtered = {};

        if(validDays && validDays.length) {
            validDays.forEach(validDay => {
                if(eventDays.includes(validDay)) {
                    filtered[validDay] = true;
                }
            })
        }

        return filtered;
    }
}
