

class Controller {
    constructor ($scope, CouponService, $stateParams, toastr) {
        this.$scope = $scope;
        this.CouponService = CouponService;
        this.$stateParams = $stateParams;
        this.toastr = toastr;
    }

    $onInit () {
        this.$scope.modalTitle = '<h4>Create ticket coupon</h4>';
        this.creationInProgress = false;
        this.isLoading = false;
        this.ticketTypes = [];

        this.coupon = {
            settings: {
                send_immediately: false
            },
            receivers: []
        };

        this.getTicketTypes();
    }

    disableSubmit () {
        return this.creationInProgress;
    }

    fieldHasError (fieldName) {
        return this.createCouponForm.$submitted && this.createCouponForm[fieldName].$invalid
    }

    getTicketTypes () {
        this.isLoading = true;

        return this.CouponService.getTicketTypes(this.$stateParams.event)
            .then(response => {
                this.ticketTypes = response;
            })
            .finally(() => this.isLoading = false);
    }

    updateReceiversList (receivers) {
        this.coupon.receivers = angular.copy(receivers);
    }

    submit () {
        this.createCouponForm.$setSubmitted();

        if(this.createCouponForm.$invalid) {
            this.toastr.warning('Invalid Form Data');
            return;
        }

        if(!Array.isArray(this.coupon.receivers) || !this.coupon.receivers.length) {
            this.toastr.warning('No receivers created');
            return;
        }

        this.creationInProgress = true;

        this.CouponService.createCoupon(this.$stateParams.event, this.coupon)
            .then(response => {
                this.CouponService.showSuccessCouponCreationMessage(response);
                this.onSave();
            })
            .finally(() => this.creationInProgress = false);
    }
}

Controller.$inject = ['$scope', 'CouponService', '$stateParams', 'toastr']

angular.module('SportWrench').component('ticketCouponCreationForm', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-creation/ticket-coupon-creation-form/template.html',
    bindings: {
        onSave: '&',
        close: '&'
    },
    controller: Controller
});
