angular.module('SportWrench').component('ticketCouponList', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-list.html',
    controller: TicketCouponListController,
});

TicketCouponListController.$inject = ['eventDashboardService'];

function TicketCouponListController(eventDashboardService) {
    this.$onInit = function () {
        this.event = eventDashboardService.getEvent();

        this.showCouponsList = this.event.require_coupon;
        this.showTicketBuyEntryCodesList = this.event.event_ticket_buy_entry_code_required;
    }
}
