<div class="container">
	<h3 class="text-center">Camps Statistics</h3>
	<spinner active="data.loading"></spinner>
	<div class="row" ng-if="!data.loading">
		<div class="col-xs-12">
			<div ng-repeat="c in data.camps">
				<table class="table table-condensed camps-stats">
					<thead>
						<tr class="bg-info">
							<th class="camp-name" ng-bind="::c.camp_name"></th>
							<th class="check" ng-if="::data.av_opts.check">Check / Pending</th>
							<th class="check" ng-if="::data.av_opts.ach">ACH / Pending</th>
							<th class="card" ng-if="::data.av_opts.card">Card</th>
							<th class="amount">Amount / Pending</th>
							<th>Participants</th>
							<th>Waitlisted</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="t in c.types">
							<td class="font-bold">{{t.label}} ({{t.price | currency}})</td>
							<td ng-if="::data.av_opts.check">{{t.stats.check_total.qty}} / <span uib-tooltip="Pending Check Payments">{{t.stats.check_pending.qty}}</span></td>
							<td ng-if="::data.av_opts.ach">{{t.stats.ach_total.qty}} / <span uib-tooltip="Pending ACH Payments">{{t.stats.ach_pending.qty}}</span></td>
							<td ng-if="::data.av_opts.card">{{t.stats.card.qty}}</td>
							<td>{{t.stats.amount.total | currency}} / {{t.stats.amount.pending | currency}}</td>
							<td>{{t.total_qty}}</td>
							<td>{{t.stats.waitlisted.qty}}</td>
						</tr>
						<tr class="bg-warning">
							<td class="font-bold">Total</td>
							<td ng-if="::data.av_opts.check">{{c.check_total.qty}} / <span uib-tooltip="Pending Check Payments">{{c.check_pending.qty}}</span></td>
							<td ng-if="::data.av_opts.ach">{{c.ach_total.qty}} / <span uib-tooltip="Pending ACH Payments">{{c.ach_pending.qty}}</span></td>
							<td ng-if="::data.av_opts.card">{{c.card.qty}}</td>
							<td>{{c.amount.total | currency}} / {{c.amount.pending | currency}}</td>
							<td>{{c.total_qty}}</td>
							<td>{{c.waitlisted.qty}}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="panel panel-default">
				<div class="panel-body">
					<h4>Totals:</h4>
					<div class="row">
						<div class="col-xs-2"><strong>Total Amount:</strong></div>
						<div class="col-xs-10" ng-bind="data.totals.total_amount | currency"></div>
					</div>
					<div class="row" ng-if="::data.av_opts.card">
						<div class="col-xs-2"><strong>Card Amount:</strong></div>
						<div class="col-xs-10" ng-bind="data.totals.card_amount | currency"></div>
					</div>
					<div class="row" ng-if="::data.av_opts.check">
						<div class="col-xs-2"><strong>Check Amount:</strong></div>
						<div class="col-xs-10">
							<span>Received: {{data.totals.check_received_amount | currency}}</span> /
							<span>Pending: {{data.totals.check_pending_amount | currency}}, <strong>total:</strong> {{data.totals.total_check | currency}}</span>
						</div>
					</div>
					<div class="row" ng-if="::data.av_opts.ach">
						<div class="col-xs-2"><strong>ACH Amount:</strong></div>
						<div class="col-xs-10">
							<span>Received: {{data.totals.ach_received_amount | currency}}</span> /
							<span>Pending: {{data.totals.ach_pending_amount | currency}}, <strong>total: </strong> {{data.totals.total_ach | currency}}</span>
						</div>
					</div>
					<div class="row">
						<div class="col-xs-2"><strong>Stripe Fee:</strong></div>
						<div class="col-xs-10" ng-bind="data.totals.stripe_fee | currency"></div>
					</div>
					<div class="row" ng-if="penaltyExists()">
						<div class="col-xs-2"><strong>Penalty:</strong></div>
						<div class="col-xs-10">
							<span class="padding-sm"><b>Failed ACH Payments:</b> {{data.penalty.ach.qty}} * {{data.penalty.ach.tax | currency}} = {{data.penalty.ach.amount | currency}}</span>
							<span class="padding-sm" ng-if="data.penalty.dispute.qty > 0">
                                <b>Disputes:</b>
                                <span ng-if="data.penalty.dispute.qty_before_change > 0">
                                    {{data.penalty.dispute.qty_before_change}} * {{data.penalty.dispute.fee_before_change | currency}}
                                </span>
                                <span ng-if="data.penalty.dispute.qty_after_change > 0 && data.penalty.dispute.qty_before_change > 0">+</span>
                                <span ng-if="data.penalty.dispute.qty_after_change > 0">
                                    {{data.penalty.dispute.qty_after_change}} * {{data.penalty.dispute.fee_after_change | currency}}
                                </span>
                                        = {{data.penalty.dispute.penalty | currency}}
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-2"><strong>SportWrench Fee:</strong></div>
                        <div class="col-xs-10" ng-bind="data.totals.sw_fee | currency"></div>
                    </div>
				</div>
			</div>
		</div>
	</div>
</div>
