const REFRESH_INTERVAL = 5000;

class Controller {
    constructor (toastr, fileUploadService, ExhibitorTicketService, $stateParams, IMPORT_STATUSES) {
        this.toastr = toastr;
        this.fileUploadService = fileUploadService;
        this.ExhibitorTicketService = ExhibitorTicketService;
        this.$stateParams = $stateParams;
        this.IMPORT_STATUSES = IMPORT_STATUSES;
    }

    $onInit () {
        this.submitting = false;
        this.error = null;
        this.file = null;
    }

    save () {
        this.onChanges();

        if(this.form.$invalid || this.error !== null) {
            this.toastr.warning('Not all data filled');
            return;
        }
        this.submitting = true;
        this.progress = 0;
        this.status = this.IMPORT_STATUSES.RUNNING;

        const data = { file: this.file };

        this.importExhibitorTickets(data)
            .then((response) => {
                this.importID = response.import_id;
                this.updateImportProgress();
            })
    }

    importExhibitorTickets (data) {
        const formData = new FormData();
        formData.append('ticket_type', this.ticketType);

        if(this.borderColour) {
            formData.append('border_colour', this.borderColour);
        }

        formData.append('file', data.file);

        return this.ExhibitorTicketService.import(this.$stateParams.event, formData)
            .then((response) => {
                return response.data;
            })
    }

    updateImportProgress() {
        this.ExhibitorTicketService.getImport(this.importID, this.$stateParams.event)
            .then((response) => {
                this.progress = response.progress;
                this.status = response.status;
                if(this.status === this.IMPORT_STATUSES.RUNNING) {
                    setTimeout(() => this.updateImportProgress(), REFRESH_INTERVAL);
                }
                else {
                    this.submitting = false;
                    if(this.status === this.IMPORT_STATUSES.ERROR) {
                        this._processImportError(response.output);
                    }
                    else if(this.status === this.IMPORT_STATUSES.FINISHED) {
                        this.toastr.success(
                            `${response.output.createdTickets} tickets(s) created. 
                            ${response.output.sentReceipts} receipt(s) sent. 
                             ${response.output.duplicatesCount} duplicate(s) found.`
                        );
                    }
                    this.onClose();
                }
            })
    }

    _processImportError(err) {
        if(err.validation) {
            this.toastr.warning(err.validation);
        }
        else if(err.validationErrors) {
            this.toastr.warning(
                err.validationErrors.map(e => e.message).join('\n')
            );
        }
        else {
            this.toastr.error(err.message || 'Internal server error');
        }
    }

    isSubmitDisabled () {
        return this.submitting;
    }

    getFileErrorMessage () {
        return this.fileUploadService.textFileUploadValidation(this.file);
    }

    onChanges () {
        if(this.form.file.$invalid) {
            this.error = 'File is required';
        } else {
            let fileErrorMessage = this.getFileErrorMessage();

            if(fileErrorMessage) {
                this.error = fileErrorMessage;
            } else {
                this.error = null;
            }
        }
    }

    getTicketTypeLabel() {
        const types = {
            'daily': 'Daily',
            'weekend': 'Weekend',
        }

        return types[this.ticketType];
    }

    isErrorVisible () {
        return this.error !== null && this.form.$submitted;
    }
}

Controller.$inject = ['toastr', 'fileUploadService', 'ExhibitorTicketService', '$stateParams', 'IMPORT_STATUSES'];

angular.module('SportWrench').component('exhibitorTicketImport', {
    templateUrl: 'events/dashboard/tickets/exhibitor-ticket/exhibitor-ticket-import/template.html',
    bindings: {
        onClose: '&',
        ticketType: '<',
    },
    controller: Controller,
})
