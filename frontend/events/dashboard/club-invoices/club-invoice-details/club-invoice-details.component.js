
class Controller {
    constructor ($scope, $rootScope,$stateParams, clubInvoiceService, PAYMENT_TYPE, PAYMENT_STATUS) {
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.eventId = $stateParams.event;
        this.clubInvoiceService = clubInvoiceService;
        this.clubInvoice = {};
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.PAYMENT_STATUS = PAYMENT_STATUS;
    }

    async $onInit () {
        this.$scope.modalTitle = `<h4>Invoice Details</h4>`
        this.$scope.modalSkipFooter = true;
        this.isRefundPending = false;

        this.clubInvoice = await this.clubInvoiceService.getClubInvoicesDetails(this.eventId, this.purchaseId)

        this.refund = async (isConfirmed) => {
            if(!isConfirmed) {
                return;
            }

            this.isRefundPending = true;

            try {
                await this.clubInvoiceService.makeFullRefund(this.eventId, this.purchaseId);

                this.$rootScope.$broadcast('club.invoice.updated');
                this.onClose();
            } catch (err) {
                throw err;
            } finally {
                this.isRefundPending = false;
            }
        }
    }

    showRefundButton (clubInvoice) {
        const isCardPayment = clubInvoice.type === this.PAYMENT_TYPE.CARD;
        const isACHPayment = clubInvoice.type === this.PAYMENT_TYPE.ACH;
        const paidPayment = clubInvoice.status === this.PAYMENT_STATUS.PAID;

        return (isCardPayment || isACHPayment) && paidPayment;
    }
}

Controller.$inject = ['$scope', '$rootScope', '$stateParams', 'clubInvoiceService', 'PAYMENT_TYPE', 'PAYMENT_STATUS'];

angular.module('SportWrench').component('clubInvoiceDetails', {
    templateUrl: 'events/dashboard/club-invoices/club-invoice-details/club-invoice-details.template.html',
    bindings: {
        purchaseId: '<',
        onClose: '&',
    },
    controller: Controller
});
