<form name="housingForm" class="form-horizontal">    
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && housingForm.company.$invalid) }">
        <label class="col-sm-3 control-label">Housing Company</label>
        <div class="col-sm-4">
            <select 
                class="form-control"
                ng-model="tournament.housing_company_id"
                ng-options="company.value as company.name for company in housing_companies"
                name="company"
                required>
                <option value="">Select Housing Company...</option>
            </select>
        </div>
    </div>
    <div ng-if="tournament.housing_company_id === 1"
         ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && housingForm.teams_access_level.$invalid) }">
        <label class="col-sm-3 control-label">Housing Teams Access Level</label>
        <div class="col-sm-4">
            <select
                class="form-control"
                ng-model="tournament.housing_teams_access_level"
                name="teams_access_level"
                ng-options="access.value as access.name for access in housing_teams_access_level"
                required>
                <option value="">Select Level...</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label no-pd-top">Exclude Wait Listed Teams</label>
        <div class="col-sm-4">
            <label>
                <input
                    type="checkbox"
                    ng-model="tournament.teams_settings.skip_waitlist_teams_for_ths"
                    name="skip_waitlist_teams_for_ths">
            </label>
        </div>
    </div>

    <div ng-class="{
            'form-group validation-required': true,
            'has-error': (utils.formSubmitted && housingForm.housing_rooming_list_due_date.$invalid || roomingListDueDateError)
         }"
         ng-if="tournament.housing_company_id === 1">
        <label class="col-sm-3 control-label">Rooming List Due Date</label>
        <div class="col-md-3 col-sm-4">
            <date-time-form-control
                date="tournament.housing_rooming_list_due_date"
                format="MM/dd/yyyy"
                timepicker="false"
                field-required="true"
                min-date="tournament.date_reg_open"
                max-date="tournament.date_start"
                name="housing_rooming_list_due_date">
            </date-time-form-control>
        </div>
        <div class="has-error-div col-sm-5" ng-bind="roomingListDueDateError"></div>
    </div>
    <div ng-if="isCustomCompany()"
         ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && housingForm.custom_company.$invalid) }">
        <label class="col-sm-3 control-label">Custom Company</label>
        <div class="col-sm-7">
            <input 
                type="text" 
                class="form-control" 
                placeholder="Company Name ..."
                ng-model="tournament.custom_housing_company"
                name="custom_company"
                required>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Local Teams Max Distance</label>
        <div class="col-sm-7">
            <input 
                type="number" 
                min="0" 
                class="form-control"
                name="distance"
                ng-model="tournament.housing_local_teams_distance">
            <p class="help-block">Teams will be marked LOCAL within this many miles of the Main event location.</p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Housing Nights Required</label>
        <div class="col-sm-7">
            <input 
                type="number" 
                min="0" 
                name="nights_required"
                class="form-control"
                ng-model="tournament.housing_nights_required">
            <p class="help-block">Minimum number of hotel room nights required per team.</p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Housing Nights Threshold</label>
        <div class="col-sm-7">
            <input 
                type="number" 
                min="0" 
                class="form-control"
                name="nights_threshold"
                ng-model="tournament.housing_nights_threshold">
            <p class="help-block">Caution threshold for teams nearing required minimum room nights. (if minimum room nights is set to 15 and threshold is 2, warning would appear at 15-17 room nights).</p>
        </div>
    </div>
</form>
