angular.module('SportWrench').directive('exhibitorsForm',
    ['_', 'SalesManagerService',
    function (_, SalesManagerService) {
    return {
        restrict: 'E',
        scope: {
            tournament: '=',
        },
        templateUrl: 'events/settings/general/forms/exhibitors-form.html',
        require: '^generalSettings',
        replace: true,
        link: function (scope, elem, attrs, ctrl) {
            scope.utils = {
                formSubmitted: false,
                stripeAccountConnected: false,
            };
            scope.salesManagers = null;

            scope.formName = 'ExhibitorsForm';
            scope.capitalizedType = 'Exhibitors';

            scope.isPropInvalid = function (prop) {
                const form = scope[scope.formName];
                return scope.utils.formSubmitted && form[prop] && form[prop].$invalid;
            };

            scope.isPropWithError = function (prop) {
                const form = scope[scope.formName];
                return form.$error[prop];
            };

            const __validateRegOpen = function () {
                scope.exhibitorsRegStartErr = "";

                if (!scope.tournament.date_start) {
                    scope.exhibitorsRegStartErr = "You need to set event start date";
                }
                if (scope.tournament.date_exhibitors_reg_close && scope.tournament.date_exhibitors_reg_open
                    && scope.tournament.date_exhibitors_reg_open > scope.tournament.date_exhibitors_reg_close) {
                    scope.exhibitorsRegStartErr = "Open date can't be later than close date";
                }
            };

            const __validateRegClose = function () {
                scope.closeDateWarningMessage = '';
                scope.exhibitorsRegEndErr = '';

                const closeDateWarningRules = [
                    scope.tournament.date_exhibitors_reg_close >= scope.tournament.date_start,
                    scope.tournament.date_exhibitors_reg_close <= scope.tournament.date_end
                ];

                if (!scope.tournament.date_start) {
                    scope.exhibitorsRegEndErr = "You need to set event start date";
                }

                if (scope.tournament.date_exhibitors_reg_close > scope.tournament.date_end) {
                    scope.exhibitorsRegEndErr = "Close date can't be later then event end date";
                }

                if (closeDateWarningRules.every(rule => rule)) {
                    scope.closeDateWarningMessage = `
                        Warning: This date is later than event start date.
                        ${scope.capitalizedType} will be able to apply during the event`;
                }
            };

            scope.$watch('tournament.date_exhibitors_reg_open', function () {
                __validateRegOpen();
                __validateRegClose();
            });

            scope.$watch('tournament.date_exhibitors_reg_close', function () {
                __validateRegOpen();
                __validateRegClose();
            });

            scope.$on('EventSettingsForm.DateStartChanged', function () {
                __validateRegOpen();
                __validateRegClose();

            });
            scope.$on('EventSettingsForm.DateEndChanged', function () {
                __validateRegOpen();
                __validateRegClose();
            });

            scope.$on('EventSettingsForm.Submitted', function () {
                scope.utils.formSubmitted = true;

                const errors = [];
                const form = scope[scope.formName];


                if (form.reg_open.$invalid
                    || scope.tournament.date_exhibitors_reg_open > scope.tournament.date_exhibitors_reg_close) {
                    errors.push(`Invalid ${scope.capitalizedType} Registration Dates`);
                }
                if (form.reg_close.$invalid || scope.tournament.date_exhibitors_reg_close > scope.tournament.date_end) {
                    errors.push(`Invalid ${scope.capitalizedType} Registration Close Date`);
                }

                if (form.stripe_statement.$invalid)
                    errors.push('Invalid Card Statement Descriptor');
                if (form.stripe_account && form.stripe_account.$invalid) {
                    errors.push('Stripe Account Required');
                }
                else if (!scope.utils.stripeAccountConnected) {
                    errors.push(
                        `Selected Stripe Account is not connected to SW.
                                Please contact SW to find out how to complete the process.`
                    );
                }

                ctrl.setExhibitorsFormErrors(errors);
            });
            scope.onStripeAccountChange = function(acc) {
                scope.utils.stripeAccountConnected = acc.connected;
                if(!acc.connected) {
                    scope.ExhibitorsForm.stripe_account.$invalid = true;
                }
            };


            scope.isOptRequired = function () {
                const opts = scope.tournament.official_payment_method;

                const picked = _.reduce(Object.keys(opts), function (counter, item) {
                    if (opts[item]) {
                        ++counter;
                    }

                    return counter;
                }, 0);

                return (picked === 0);
            };

            loadManagers();

            ctrl.registerForm(scope.formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(scope.formName);
            });

            scope.salesManagerLabel = (sales) => `${sales.first} ${sales.last}`;

            function loadManagers () {
                SalesManagerService.getAllSM()
                    .then(function (resp) {
                        scope.salesManagers = resp.data.sales;
                    });
            }
        }
    };
}]);
