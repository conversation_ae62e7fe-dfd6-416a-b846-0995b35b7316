angular.module('SportWrench').component('eventUsersList', {
    templateUrl : 'events/settings/general/event-users/event-users-list/event-users-list.html',
    bindings    : {
        openPermissions    : '&',
        showError          : '&'
    },
    controller  : EventUsersListComponent
});

EventUsersListComponent.$inject = ['eventUsersService', '$stateParams', '$document'];

function EventUsersListComponent(eventUsersService, $stateParams, $document) {
    let self = this;

    this.search           = '';
    this.users            = {};
    this.warning          = {
        toManyResults: false,
        noResults    : false
    };
    this.rowQtyLimit      = 0;

    let eventID = $stateParams.event;

    this.findEligibleUsers = function () {
        if (this.search) {
            eventUsersService.findEligibleUsers(eventID, this.search)
                .then(function (res) {
                    if (res.data.total_rows !== 0 ) {
                        self.users                    = res.data.users;
                        self.rowQtyLimit              = res.data.search_limit;
                        self.warning.toManyResults    = (res.data.total_rows > res.data.search_limit);
                        self.warning.noResults        = false;
                    } else {
                        self.users                  = {};
                        self.rowQtyLimit            = 0;
                        self.warning.toManyResults  = false;
                        self.warning.noResults      = true;
                    }
                })
                .catch(function (err) {
                    self.users                  = {};
                    self.rowQtyLimit            = 0;
                    self.warning.toManyResults  = false;
                    self.warning.noResults      = false;

                    self.showError({
                        err: (err.data && err.data.validation) ? err.data.validation : 'Internal Server Error.'
                    });
                });
        }
    };

    $document.bind('keydown', function(event) {
        // if enter pressed
        if(Number(event.keyCode) === 13) {
            self.findEligibleUsers();
        }
    });
}
