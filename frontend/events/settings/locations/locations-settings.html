<button class="btn btn-primary row-space" ng-click="createLocation()">New Location</button>
<div class="row">
    <div class="col-lg-6" ng-repeat="l in locations">
        <div class="form-horizontal row">
            <div class="form-group no-margin">
                <div class="col-xs-4">
                    <h4 class="control-label">{{getLocationTitle(l.number)}}</h4>
                </div>
                <div class="col-xs-8">
                    <button class="btn btn-default" ng-click="editLocation(l.id)"><i class="fa fa-pencil-square-o"></i> Edit</button>
                    <button class="btn btn-danger"  ng-click="removeLocation(l.id)" ng-if="l.number !== 1"><i class="fa fa-times"></i> Remove</button>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">Location name</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.name"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">Short name</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.short_name"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">Address</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.address"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">City</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.city"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">State</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.state_name"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">ZIP</label>
                <div class="col-xs-6">
                    <p class="form-control-static" ng-bind="l.zip"></p>
                </div>
            </div>
            <div class="form-group no-margin">
                <label class="col-xs-4 control-label">Courts range</label>
                <div class="col-xs-6">
                    <p class="form-control-static">from: {{l.courts_from || 'n/a'}} to: {{l.courts_to || 'n/a'}}</p>
                </div>
            </div>
        </div>
    </div>
</div>
