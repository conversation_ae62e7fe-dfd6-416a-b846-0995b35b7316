angular.module('SportWrench').service('EventSettingsService', EventSettingsService)

function EventSettingsService ($http, $q, moment) {
    this.$http      = $http;
    this.$q         = $q;
    this.moment     = moment;
    this.sections   = {
        general          : { title: 'General',               index: 0 },
        divisions        : { title: 'Divisions',             index: 1 },
        locations        : { title: 'Locations',             index: 2 },
        tickets          : { title: 'Tickets',               index: 3 },
        booths           : { title: 'Exhibitors\' Booths',   index: 4 },
        eventUsers       : { title: 'Event Users',           index: 5 },
        assignTemplates  : { title: 'Transactional Emails',  index: 6 },
    };
    this.tournamentDatesFields = [
        'date_end', 'date_reg_close', 'date_reg_open', 
        'date_start',  'roster_deadline', 'date_official_reg_open',
        'date_official_reg_close', 'online_team_checkin_end', 'online_team_checkin_start',
        'date_exhibitors_reg_open', 'date_exhibitors_reg_close',
        'officials_hotel_date_start', 'officials_hotel_date_end',
        'date_staff_reg_open', 'date_staff_reg_close',
        'staff_hotel_date_start', 'staff_hotel_date_end',
        'housing_rooming_list_due_date'
    ];
    this.updateDateFormat       = 'MM/DD/YYYY hh:mm a';
    this.urlPrefix              =  '/api/event/';

    this.staffersMappings = {
        enable_officials_reg        : 'enable_staff_reg',
        date_official_reg_open      : 'date_staff_reg_open',
        date_official_reg_close     : 'date_staff_reg_close',
        official_payment_method     : 'staff_payment_method',
        enable_hotel_for_officials  : 'enable_hotel_for_staff',
        officials_hotel_date_start  : 'staff_hotel_date_start',
        officials_hotel_date_end    : 'staff_hotel_date_end',
        officials_hotel_comp        : 'staff_hotel_comp',
        clothes_requirements        : 'clothes_requirements',
    };
}

EventSettingsService.prototype.stringifyDates = function(data) {
    var fields = this.tournamentDatesFields, 
        dateFormat = this.updateDateFormat,
        tournament = _.clone(data);
    for(var i = 0, l = fields.length, d; i < l; ++i) {
        d = tournament[fields[i]]
        if(d) {
            tournament[fields[i]] = this.moment(d).format(dateFormat)
        }
    }
    return tournament;
}

EventSettingsService.prototype.getSectionsList = function () {
    return _.clone(this.sections);
}

EventSettingsService.prototype.updateTournament = function (id, data, filesToUpload) {
    var tournament = this.stringifyDates(data);

    const fd = new FormData();

    fd.append('tournament', angular.toJson(tournament));

    if(filesToUpload['cover-image'])
        fd.append('images_cover_image', filesToUpload['cover-image'])

    if(filesToUpload['main-logo'])
        fd.append('images_main_logo', filesToUpload['main-logo'])

    return this.$http.put(this.urlPrefix  + id + '/update', fd,  {
        headers: { 
            'Content-Type': undefined, 
            withCredentials: true,
            transformRequest: angular.identity
        }});
}

EventSettingsService.prototype.createTournament = function (data, filesToUpload) {
    var tournament = this.stringifyDates(data);
    
    const fd = new FormData();

    fd.append('tournament', angular.toJson(tournament));

    if(filesToUpload['cover-image'])
        fd.append('images_cover_image', filesToUpload['cover-image'])

    if(filesToUpload['main-logo'])
        fd.append('images_main_logo', filesToUpload['main-logo'])

    return this.$http.post(this.urlPrefix + 'create', fd, {
        headers: { 
            'Content-Type': undefined, 
            withCredentials: true,
            transformRequest: angular.identity
        }});
}

EventSettingsService.prototype.loadTournament = function (id) {
    var defer = this.$q.defer(),
        dateFields = this.tournamentDatesFields;
    this.$http.get(this.urlPrefix + id + '/update/info').success(function (data) {

       var t = data.tournament;

        for(var i = 0, l = dateFields.length, d, nD; i < l; ++i) {

            d = t[dateFields[i]];

            if (d) {
                if (/^\d{4}-\d{2}-\d{2}$/.test(d)) {
                    var dateChunks = d.split('-');

                    nD = new Date();

                    nD.setUTCFullYear(
                        Number(dateChunks[0]),
                        Number(dateChunks[1]) - 1,
                        Number(dateChunks[2])
                    );

                    if (dateFields[i] === 'officials_hotel_date_end' || dateFields[i] === 'staff_hotel_date_end') {
                        nD.setUTCHours(23, 59, 59, 999);
                    } else {
                        nD.setUTCHours(0, 0, 0, 0);
                    }
                } else {
                    nD = new Date(d);
                }

                t[dateFields[i]] = new Date(nD.getTime() + nD.getTimezoneOffset() * 60 * 1000);
            }
        }
        defer.resolve(t)
    }).error(function (data) {
        defer.reject(data);
    });
    return defer.promise;
}

EventSettingsService.prototype.loadDivisions = function (id) {
    console.log('loading Divisions', id);
}

EventSettingsService.prototype.loadLocations = function (id) {
    return this.$http.get(this.urlPrefix  + id + '/locations')
}

EventSettingsService.prototype.loadLocationInfo = function (eventId, locationId) {
    return this.$http.get(this.urlPrefix  + eventId + '/locations/' + locationId)
}

EventSettingsService.prototype.updateLocation = function (eventId, locationId, locationObj) {
    return this.$http.put(this.urlPrefix  + eventId + '/locations/' + locationId, {
        location: locationObj
    })
}

EventSettingsService.prototype.createLocation = function (eventId, locationObj) {
    return this.$http.post(this.urlPrefix  + eventId + '/locations', {
        location: locationObj
    })
}

EventSettingsService.prototype.removeLocation = function (eventId, locationId) {
    return this.$http.delete('/api/event/' + eventId + '/locations/' + locationId)
}

EventSettingsService.prototype.loadSportSanctionings = function (id) {
    return this.$http.get('/api/sport/' + id + '/sanctionings');    
}

EventSettingsService.prototype.loadSportVariations = function (id) {
    return this.$http.get('/api/sport/' + id + '/variations');
}

EventSettingsService.prototype.loadTemplateTypes = function (eventID) {
    return this.$http.get('/api/event/' + eventID + '/template-types').then( resp => resp.data );
}

EventSettingsService.prototype.assignTmplToTrigger = function (eventID, data) {
    return this.$http.put('/api/event/' + eventID + '/assign-templates', { data: data });
}

EventSettingsService.prototype.createDataProxyForStaff = function (targetObj) {
    let self = this;

    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy
    return new Proxy(targetObj, {
        get(target, prop) {

            if(self.staffersMappings[prop]) {
                return targetObj[self.staffersMappings[prop]];
            }

            return targetObj[prop];
        },
        set(target, prop, value) {
            if(self.staffersMappings[prop]) {
                targetObj[self.staffersMappings[prop]] = value;
            }
            return true;
        }
    })
};


EventSettingsService.prototype.loadClothesTypes = function (eventID) {
    return this.$http.get('/api/event/' + eventID + '/clothes-types');
};
