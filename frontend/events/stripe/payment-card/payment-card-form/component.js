
class Controller {
    constructor (StripeElementsService, PaymentCardService, toastr, $scope) {
        this._StripeElementsService = StripeElementsService;
        this._PaymentCardService = PaymentCardService;
        this._toastr = toastr;
        this.$scope = $scope;
    }

    $onInit () {
        this.isLoading = false;
        this.showCardForm = true;

        this.getStripeSettings().then(()=>this.initCardForm())
    }

    initCardForm() {
        this.card = this._StripeElementsService.getCardElement();

        // Add an instance of the card Element into the `card-element` <div>.
        // https://stripe.com/docs/stripe-js/reference#element-mount
        this.card.mount('#card-element');
    }

    async submit () {
        if(this.cardSubmitting) {
            return;
        }

        this.cardSubmitting = true;

        return this._StripeElementsService.confirmCardSetup(this.clientKey, this.card)
            .then(response => {
                let { setupIntent } = response;

                return this._PaymentCardService.savePaymentCard(setupIntent.payment_method)
                    .then(() => {
                        this._toastr.success('Saved');

                        this.onCardSave();
                    })
                    .catch((err) => {
                        if(err.data && err.data.message) {
                            this._toastr.error(err.data.message);
                        }

                        this.getStripeSettings()
                            .then(() => this.initCardForm());

                        this.showError();
                    });
            })
            .catch(err => this.showError(err))
            .finally(() => this.cardSubmitting = false);
    }

    showError (error) {
        const displayError = document.getElementById('card-errors');

        // current validation error
        if (error) {
            displayError.textContent = error.message;
        } else {
            displayError.textContent = '';
        }
    }

    getStripeSettings () {
        this.isLoading = true;

        return this._PaymentCardService.paymentCardCreationSettings()
            .then(data => {
                let settings = data && data.settings;

                this.clientKey = settings.intent_client;
                
                if(settings && settings.stripe_client) {
                    try {
                        this._StripeElementsService.initStripeElements(settings.stripe_client);
                    } catch (err) {
                        console.error(err);

                        // if stripe key is incorrect - disable card payments
                        this.showCardForm = false;
                    }
                }
            })
            .finally(() => this.isLoading = false);
    }

    disableSubmit () {
        return this.cardSubmitting;
    }
}

Controller.$inject = ['StripeElementsService', 'PaymentCardService', 'toastr', '$scope'];

angular.module('SportWrench').component('paymentCardForm', {
    templateUrl: 'events/stripe/payment-card/payment-card-form/template.html',
    bindings: {
        onCardSave: '&'
    },
    controller: Controller
});
