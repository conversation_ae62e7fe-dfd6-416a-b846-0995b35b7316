
class Controller {
    constructor (EventCustomPaymentService, StripeElementsService, toastr) {
        this.EventCustomPaymentService = EventCustomPaymentService;
        this.StripeElementsService = StripeElementsService;
        this.toastr = toastr;
    }

    $onInit () {
        this.payments = [];
        this.isLoading = false;
        this.isLoaded = false;

        this._loadPaymentsRequiresConfirmation();

        //Little hack to save "this" for function called from confirm component
        this.remove = this.remove.bind(this);
    }

    async confirm (payment) {
        payment.paymentInProcess = true;

       try {
           let response = await this.EventCustomPaymentService.getPaymentRequiresConfirmation(
               payment.custom_payment_id
           );

           if(!response && _.isEmpty(response.settings)) {
               this.toastr.warning('Payment not found');
               return;
           }

           let settings = response.settings;

           await this.StripeElementsService.initStripeElements(settings.stripe_key);

           let result = await this.StripeElementsService.stripeInstance.confirmCardPayment(settings.last_error_client_secret, {
               payment_method: settings.last_error_payment_method_id
           });

           if (result.error) {
               // Show error to your customer
               this.toastr.warning(result.error.message);
               return;
           }

           await this.EventCustomPaymentService.confirmPayment(payment.custom_payment_id);

           this.payments = this.payments.filter(p => p.custom_payment_id !== payment.custom_payment_id);

           this.toastr.success('Success');
       } catch (err) {
           payment.paymentInProcess = false;
           throw err;
       }
    }

    remove (confirm, payment) {
        payment.paymentInProcess = true;

        return this.EventCustomPaymentService.cancelPendingPayment(payment.custom_payment_id)
            .then(() => {
                this.payments = this.payments.filter(p => p.custom_payment_id !== payment.custom_payment_id);
                this.toastr.success('Success');
            })
            .catch((err) => {
                payment.paymentInProcess = false;
                throw err;
            });
    }

    _loadPaymentsRequiresConfirmation () {
        this.isLoading = true;

        return this.EventCustomPaymentService.getPaymentsRequireConfirmation()
            .then(response => {
                if(response && !_.isEmpty(response.payments)) {
                    this.payments = response.payments;
                }

                this.isLoaded = true;
            })
            .finally(() => this.isLoading = false);
    }
}

Controller.$inject = ['EventCustomPaymentService', 'StripeElementsService', 'toastr'];

angular.module('SportWrench').component('paymentsRequireActionList', {
    templateUrl: 'events/stripe/payment-card/payments-require-action-list/template.html',
    controller: Controller
});
