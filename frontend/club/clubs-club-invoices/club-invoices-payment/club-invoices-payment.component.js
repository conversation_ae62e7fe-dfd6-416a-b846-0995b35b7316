class Controller {
    constructor ($q, $scope, clubsClubInvoicesService, StripePaymentElementService, purchaseService,
                 toastr, $location, $interval, $rootScope, PAYMENT_TYPE, STRIPE_PAYMENT_TYPE, MIN_AMOUNT_FOR_ACH,
                 CARD_PAYMENT_SUB_TYPES, ACH_PAYMENT_SUB_TYPES, ONLINE_PAYMENT_INTERVAL_PARAMS, ONLINE_PAYMENT_MESSAGES) {
        this.$q = $q;
        this.$scope = $scope;
        this.ClubsClubInvoicesService = clubsClubInvoicesService;
        this.StripePaymentElementService = StripePaymentElementService;
        this.purchaseService = purchaseService;
        this.toastr = toastr;
        this.$location = $location;
        this.$interval = $interval;
        this.$rootScope = $rootScope;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
        this.MIN_AMOUNT_FOR_ACH = MIN_AMOUNT_FOR_ACH;
        this.CARD_PAYMENT_SUB_TYPES = CARD_PAYMENT_SUB_TYPES;
        this.ACH_PAYMENT_SUB_TYPES = ACH_PAYMENT_SUB_TYPES;
        this.ONLINE_PAYMENT_INTERVAL_PARAMS = ONLINE_PAYMENT_INTERVAL_PARAMS;
        this.ONLINE_PAYMENT_MESSAGES = ONLINE_PAYMENT_MESSAGES;

        this.returnUrl = this.$location.absUrl();
    }

    async $onInit () {
        this.$scope.modalTitle = `<h4>Pay Invoice</h4>`

        this.payment = {
            subtotal: 0,
            merchantFee: {},
            total: {},
        };

        this.paymentType = this.PAYMENT_TYPE.CARD;
        this.paymentIntent = null;
        this.invoiceLoading = true;
        this.paymentIsInProgress = false;

        await this.__createPaymentIntent();
        this.__initStripe();
        this.__initPaymentElement();

        this.invoiceLoading = false;
        this.paymentReadyForConfirmation = false;
        this.hidePaymentForm = false;
        this.minAmountForSelectedPaymentMethod = 0;
        this.selectedCard = null;
        this.paymentInterval = null;
    }

    $onDestroy () {
        if(this.paymentInterval) {
            this.$interval.cancel(this.paymentInterval);
        }
    };

    async __createPaymentIntent () {
        const { paymentIntent, savedCards } =
            await this.ClubsClubInvoicesService.createPaymentIntent(this.purchaseId);

        this.paymentIntent = paymentIntent;
        this.invoiceData = paymentIntent ? paymentIntent.invoiceData : {};
        this.payment = paymentIntent ? paymentIntent.payment_amount : {};
        this.savedCards = savedCards;
    }

    __initStripe () {
        if(this.paymentIntent.public_key) {
            try {
                this.StripePaymentElementService.init(this.paymentIntent.public_key);
            } catch (err) {
                this.hidePaymentForm = true;
            }
        }
    }

    __initPaymentElement () {
        let { elements, paymentElement }
            = this.StripePaymentElementService.getPaymentElement(this.paymentIntent.secret, {
            paymentMethodOrder: [this.STRIPE_PAYMENT_TYPE.CARD, this.STRIPE_PAYMENT_TYPE.ACH]
        });

        paymentElement.mount('#payment-element');

        paymentElement.collapse();

        paymentElement.on('change', ({ complete, value: { type: paymentType }  }) => {
            this.__clearError();

            this.__handlePaymentTypeChange(paymentType);

            this.paymentReadyForConfirmation = complete;

            this.$scope.$digest();
        });

        this.elements = elements;
    }

    __handlePaymentTypeChange (paymentType) {
        if(this.CARD_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.PAYMENT_TYPE.CARD;
        }

        if(this.ACH_PAYMENT_SUB_TYPES.includes(paymentType)) {
            paymentType = this.PAYMENT_TYPE.ACH;
        }

        if(paymentType !== this.paymentType) {
            this.paymentType = paymentType;
        }
    }

    isCardSelected() {
        return this.selectedCard !== null
    }

    onCardSelect(card) {
        this.selectedCard = card;
        this.paymentType = this.PAYMENT_TYPE.CARD;
    }

    onCardReset() {
        this.selectedCard = null;
    }

    submitAllowed() {
        const isTooSmallAmountForAch = this.tooSmallAmount()
        const totalAllowed =
            Number(this.payment.total[this.paymentType]) > 0 && !isTooSmallAmountForAch;

        return (
            totalAllowed &&
            !this.paymentIsInProgress &&
            (this.isCardSelected() || this.paymentReadyForConfirmation)
        );
    }

    tooSmallAmount () {
        if(this.paymentType === this.PAYMENT_TYPE.ACH) {
            this.minAmountForSelectedPaymentMethod = this.MIN_AMOUNT_FOR_ACH;

            return this.payment.total[this.paymentType] < this.MIN_AMOUNT_FOR_ACH;
        }

        return false;
    }

    async submit () {
        if(!this.submitAllowed()) {
            return;
        }

        this.paymentIsInProgress = true;

        try {
            await this.updatePaymentIntent();

            const {error, paymentIntent} = await this.confirmPayment();

            if (!_.isEmpty(error)) {
                this.__addError(error);
                this.paymentIsInProgress = false;
            } else {
                await this.updatePayment();

                const paymentIntentId = paymentIntent && paymentIntent.id;
                if (paymentIntentId) {
                    await this.onPaymentConfirmed(paymentIntentId);
                }
            }

            this.$scope.$digest();
        } catch (err) {

        } finally {
            this.paymentIsInProgress = false;
        }
    }

    updatePaymentIntent() {
        const paymentData = {
            payment_type: this.paymentType,
            payment_intent_id: this.paymentIntent.id,
            total_amount: this.payment.total[this.paymentType],
        };

        return this.ClubsClubInvoicesService.updatePaymentIntent(this.purchaseId, paymentData);
    }

    updatePayment() {
        const paymentData = {
            payment_type: this.paymentType,
            payment_intent_id: this.paymentIntent.id,
            total_amount: this.payment.total[this.paymentType],
        };

        return this.ClubsClubInvoicesService.updatePayment(this.purchaseId, paymentData);
    }

    async confirmPayment() {
        if(this.selectedCard !== null) {
            return this.StripePaymentElementService.confirmCardPayment(
                this.paymentIntent.secret,
                this.selectedCard.stripe_payment_method_id,
                this.returnUrl
            );
        }

        return this.StripePaymentElementService.confirmPayment(
            this.elements,
            this.returnUrl
        );
    }

    __addError (error) {
        const messageContainer = document.querySelector('#error-message');

        messageContainer.textContent = error && error.message;
    }

    __clearError () {
        const messageContainer = document.querySelector('#error-message');

        messageContainer.textContent = '';
    }

    onPaymentConfirmed (paymentIntentId) {
        let deferred = this.$q.defer();

        if(!paymentIntentId) {
            deferred.resolve();
        } else {
            this.paymentInterval = this.$interval(async () => {
                const payment = await this.purchaseService.getPayment({paymentIntentId});

                if(!_.isEmpty(payment) && payment.date_paid) {
                    this.payment.id = payment.purchase_id;
                    this.payment.chargeCompleted = true;
                    this.$interval.cancel(this.paymentInterval);

                    this.$rootScope.$broadcast('club.invoice.paid');
                    this.close();
                }

            }, this.ONLINE_PAYMENT_INTERVAL_PARAMS.RETRIES_INTERVAL, this.ONLINE_PAYMENT_INTERVAL_PARAMS.MAX_RETRIES);

            this.paymentInterval.finally(() => {
                if(!this.payment.chargeCompleted) {
                    this.toastr.warning(this.ONLINE_PAYMENT_MESSAGES.INVALID_FLOW_MESSAGE,
                        { closeButton: true, timeOut: 5000, progressBar: true });

                    this.$rootScope.$broadcast('club.invoice.paid');
                    this.close();
                }

                deferred.resolve();
            });
        }

        return deferred.promise;
    }

    close () {
        this.onClose();
    }
}

Controller.$inject = ['$q', '$scope', 'clubsClubInvoicesService', 'StripePaymentElementService', 'purchaseService',
    'toastr', '$location', '$interval', '$rootScope', 'PAYMENT_TYPE', 'STRIPE_PAYMENT_TYPE',
    'MIN_AMOUNT_FOR_ACH', 'CARD_PAYMENT_SUB_TYPES', 'ACH_PAYMENT_SUB_TYPES', 'ONLINE_PAYMENT_INTERVAL_PARAMS',
    'ONLINE_PAYMENT_MESSAGES'];

angular.module('SportWrench').component('clubInvoicesPayment', {
    templateUrl: 'club/clubs-club-invoices/club-invoices-payment/club-invoices-payment.template.html',
    bindings: {
        purchaseId: '<',
        onClose: '&',
    },
    controller: Controller
});
