angular.module('SportWrench')

.controller('MyTeamsEventsController', MyTeamsEventsController);

function MyTeamsEventsController (
    $scope, eventsService, masterClub, masterClubService, userProfileService, USER_ROLE,
    $state, passEventService, $rootScope, APP_ROUTES, loadClub
) {

    $scope.events = [];
    $scope.loaded = false;
    eventsService.getForClub({club_entered: true}, function(resp) {
        $scope.events = resp.data.events;
        $scope.loaded = true;
    });

    let clubNotCompleted = loadClub && loadClub.not_completed;

    $scope.manageEventTeams = function(event, name) {
        if(clubNotCompleted) {
            return userProfileService.openUserProfileCompletenessErrorModal(USER_ROLE.CD);
        } else {
            $rootScope.$broadcast('QUICK_MANAGING_TAB', {
                event_id: event,
                name    : name
            });
        }
    };
    $scope.assign = function(e) {
        if(clubNotCompleted && e.club_entered) {
            return userProfileService.openUserProfileCompletenessErrorModal(USER_ROLE.CD);
        } else {
            passEventService.set(e);
            $state.go(APP_ROUTES.CD.CLUB_EVENTS_ASSING, {
                event: e.event_id
            })
        }
    }; 

    $scope.showYear = function (year, index) {
        if(index === 0) return true;
        var prev = $scope.events[index - 1];
        return prev && (prev.year !== year)
    }   
}
