<div class="row row-space">
    <div class="col-sm-12">
        <img 
            class="loading-image" 
            src="/images/loading-bubbles.svg" 
            width="32" 
            height="32" 
            alt=""
            ng-if="!loaded">
        <p class="text-danger text-center" ng-if="loaded && (events.length === 0)">
            Your teams have not entered any events. Please go to <a href="#/club/events">Enter Events</a> Tab to register.
        </p>
        <table
            class="table table-condensed events-table fade"
            ng-class="{'inin': (events.length>0)}" sticky-header>
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th>Name</th>
                    <th>Fee</th>
                    <th>City and State</th>
                    <th>Website</th>
                    <th>Date</th>
                    <th>Registration</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat-start="event in events" class="bg-info" ng-if="showYear(event.year, $index)">
                    <td colspan="8" class="font-bold text-left">{{event.year}} Events:</td>          
                </tr>
                <tr ng-repeat-end>
                    <td>
                        <button class="btn btn-primary btn-xs" ng-click="manageEventTeams(event.event_id, event.long_name)" ng-if="!event.past">Manage Teams</button>
                        <button class="btn btn-success btn-xs" ng-if="event.past" ng-click="$parent.exportResults(event.event_id);">Results</button>
                    </td>
                    <td>
                        <genders 
                        m="event.has_male_teams === true || event.has_coed_teams === true"
                        f="event.has_female_teams === true || event.has_coed_teams === true"
                        title_m="Has Male Teams"
                        title_f="Has Female Teams"
                        ></genders>
                    </td>
                    <td ng-click="assign(event)" ng-bind="event.long_name"></td>
                    <td>{{event.reg_fee | currency:'$'}}</td>
                    <td ng-click="$event.stopPropagation();">
                        <a ng-href="https://maps.google.com/?q={{event.city}}%20{{event.state}}" target="_blank"
                           title="Open Google Map">{{event.city}} {{event.state}}</a>
                    </td>
                    <td ng-click="$event.stopPropagation();">
                        <short-link
                            href="event.website"
                            title="Open Event Website"
                        ></short-link>
                    </td>
                    <td ng-click="$event.stopPropagation();">                     
                        {{event.date_start | UTCdate: 'MM/DD'}}-{{event.date_end | UTCdate: 'MM/DD'}}
                    </td>
                    <td>{{event.date_reg_open | UTCdate: 'MM/DD'}}-{{event.date_reg_close | UTCdate: 'MM/DD'}}</td>
                </tr>
            </tbody>
        </table>
        
    </div>
</div>
