<div class="modal-header">
    <h4>Delete A Team</h4>
</div>
<div class="modal-body">
    <div ng-show="data.toRemove.length">
        <p class="text-center text-info"><i class="fa fa-exclamation-circle"></i> The following team(s) are currently entered in the events listed below. Please select the checkbox to remove and unassign the team from the listed event.</p>
        <ul class="list-group roster-list">
            <li 
                ng-repeat="t in data.toRemove = (data.teams | filter:data.canRemove)"
                class="list-group-item pointer"
                ng-click="data.toggleSelection(t.master_team_id)"
            >
            <div class="row">
                <div class="col-xs-1 col-checkbox" ng-click="$event.stopPropagation()">
                    <label><input type="checkbox" ng-model="data.utils.selection[t.master_team_id]"></label>
                </div>
                <div class="col-xs-3 font-bold" ng-bind="t.team_name"></div>
                <div class="col-xs-8" ng-bind="t.events_list"></div>
            </div>
            </li>
        </ul>
    </div>
    <div ng-show="data.forbidRemoval.length">
        <p class="text-center text-danger"><i class="fa fa-minus-circle"></i> The following team(s) cannot be removed as they have been paid for and/or are accepted in the listed event. To remove these teams, you will need to contact the respective event.</p>
        <ul class="list-group roster-list">
            <li 
                ng-repeat="t in data.forbidRemoval = (data.teams | filter:data.canNotRemove)"
                class="list-group-item pointer"     
            >
            <div class="row">
                <div class="col-xs-3 font-bold" ng-bind="t.team_name"></div>
                <div class="col-xs-9" ng-bind="t.events_list"></div>
            </div>
            </li>
        </ul>
    </div>
    <button class="btn btn-danger" ng-click="data.save()">Delete Selected Teams</button>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="data.cancel()">Cancel</button>
</div>
