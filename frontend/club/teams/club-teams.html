<form class="form-inline row-space"> 
    <div class="form-group"><p class="lead">{{utils.season}} Teams</p></div>
    <div class="form-group">
        <div class="search-box">
            <input 
                type="text" 
                class="form-control" 
                ng-model="filter.search" 
                ng-change="refilterItems()"
                placeholder="Team Name, Team Code ...">
            <span class="glyphicon glyphicon-search"></span>
        </div>
    </div>

    <a ng-click="openCreateModal()" class="btn btn-primary">New Team</a>
    <a ui-state="states.import" class="btn btn-primary">Import Teams</a>
    <a ng-click="openResultReportModal()" class="btn btn-primary">Result Report</a>

    <div class="form-group" ng-if="selected_teams_count > 0">
        <label>Selected: {{selected_teams_count}}</label>
        <button 
            class="btn btn-danger" 
            sw-confirm="Do you really want to remove selected team(s)?"
            sw-confirm-do="removeTeams"
            sw-confirm-hide-no
        >Delete</button>
    </div>
</form>
<div class="help-block">Previous Season Teams archived here: <a ui-state="states.archive"><span ng-bind="utils.season - 1" ng-if="utils.season"></span> Teams Archive</a></div>
<table class="table table-condensed master-table club-teams" sticky-header>
	<thead>
		<tr>
            <th>
                <input type="checkbox" ng-model="utils.all_selected" ng-change="checkAll();">
            </th>
            <th>Gender</th>
            <th>Team Name</th>
            <th>USAV Code</th>
			<th class="hideable">Age</th>
            <th class="hideable">Rank</th>
			<th class="hideable">Variation</th>
			<th>Athletes</th>
            <th ng-if="clubHasUsavSanctioning()">USAV Seasonality</th>
		</tr>
	</thead>
	<tbody>
		<tr ng-repeat="team in clubTeams | orderBy:'organization_code'">
			<td>
                <input type="checkbox" ng-model="team.selected" ng-change="checkTeam(team);">
            </td>
            <td>
                <genders 
                m="team.gender !== 'female'"
                f="team.gender !== 'male'"
                ></genders>
            </td>
            <td ng-click="showRosterPanel(team, team.master_team_id)">
                <span ng-if="!team.deleted">{{team.team_name}}</span>
                <del ng-if="team.deleted">{{team.team_name}}</del>
            </td>
            <td ng-click="showRosterPanel(team, team.master_team_id)">{{team.organization_code}}</td>
			<td class="hideable" ng-click="showRosterPanel(team, team.master_team_id)" ng-bind="getAgeLabel(team.age)"></td>
			<td class="hideable" ng-click="showRosterPanel(team, team.master_team_id)">{{team.rank}}</td>
            <td class="hideable" ng-click="showRosterPanel(team, team.master_team_id)">{{team.sport_variation_name}}</td>
			<td ng-click="showRosterPanel(team, team.master_team_id)">{{team.athletes}}</td>
            <td ng-if="clubHasUsavSanctioning()" ng-bind="team.seasonality"></td>
		</tr>
        <tr no-data-row cs="9" text="No Teams found" ng-if="!clubTeams.length"></tr>
	</tbody>
</table>
