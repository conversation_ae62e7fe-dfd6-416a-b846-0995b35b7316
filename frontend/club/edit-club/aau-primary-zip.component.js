angular.module('SportWrench').component('editAauPrimaryZip', {
    templateUrl: 'club/edit-club/aau-primary-zip.html',
    bindings: {
        aauPrimaryZip             : '<',

        parentForm                : '<',
        onChange                  : '&',
        hasError                  : '&',
    },
    controller: ['$scope', EditAauPrimaryZipController]
});

function EditAauPrimaryZipController ($scope) {
    this.onAAUPrimaryZipChange = function () {
        let isValid = this.checkAauPrimaryZipValidity();

        if (isValid) {
            this.onChange({ zip: this.aauPrimaryZip });
        }
    }

    this.checkAauPrimaryZipValidity = function () {
        if (!$scope.aauPrimaryZipComponent) {
            return;
        }

        if (!this.aauPrimaryZip) {
            return false;
        }

        return true;
    }
}
