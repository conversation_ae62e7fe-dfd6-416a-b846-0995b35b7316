angular.module('SportWrench').factory('EventMembersService', function ($http) {
    var queryPrefix = '/api/club/event/',
        staff       = '/staff/',
        athlete     = '/athlete/',
        reinstate = function (type, id, event_id) {
            return $http.post(queryPrefix + event_id + type + id + '/reinstate');
        };
    return {
        findAthlete: function (id, event_id) {
            return $http.get(queryPrefix + event_id + athlete + id + '/info')
            .then(function (resp) {
                return resp && resp.data.athlete;
            });
        },
        findStaff: function (id, event_id) {
            return $http.get(queryPrefix + event_id + staff + id + '/info')
            .then(function (resp) {
                return resp && resp.data.staff;
            });
        },
        updateAthlete: function (id, event_id, data) {
            return $http.put(queryPrefix + event_id + athlete + id + '/update', data);
        },
        withdrawAthlete: function (id, event_id) {
            return $http.post(queryPrefix + event_id + athlete + id + '/withdraw');
        },
        updateStaff: function (id, event_id, data) {
            return $http.put(queryPrefix + event_id + staff + id + '/update', data);
        },
        withdrawStaff: function (id, event_id) {
            return $http.post(queryPrefix + event_id + staff + id + '/withdraw');
        },
        reinstateAthlete: function (id, event_id) {
            return reinstate(athlete, id, event_id)
        },
        reinstateStaff: function (id, event_id) {
            return reinstate(staff, id, event_id)
        },
        getStafferTeamsList: function (id, event_id) {
            return $http.get(queryPrefix + event_id + staff + id + '/primary-teams')
            .then(function (resp) {
                return resp && resp.data.staffer;
            })
        }
    }
})
